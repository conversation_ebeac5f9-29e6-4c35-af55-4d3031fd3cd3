.container_warp{
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;

  .operating {
    position: absolute;
    top: 0;
    z-index: 99;
    background-color: rgba(240, 242, 245, 0.7);
    border-radius: 10px;
    padding: 10px 10px;
    box-shadow: 1px 1px 4px 0 #0a0a0a2e;
  }

  .operating_right {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 99;
    background-color: rgba(240, 242, 245, 0.7);
    border-radius: 10px;
    padding: 10px 1px;
    box-shadow: 1px 1px 4px 0 #0a0a0a2e;
  }
}

.btn-group {
    border-right: 1px solid #f1ccaf;
    display: inline-block;
    padding-left: 10px;
    padding-right: 14px;
    position: relative;
    &:last-of-type {
        border-right: 0;
    }
    .component_btn{
      display: inline-block;
      background-color: rgba(7, 65, 121, 0.3);
      border-radius: 3px;
      font-size: 19px;
      padding: 5px 10px;
      cursor: pointer;
    }
    .btn_edit_config{
      display: inline-block;
      border-radius: 3px;
      font-size: 19px;
      padding: 5px 10px;
      cursor: pointer;
      transition: transform 0.3s;
    }
    .btn_edit_config:active {
      transform: scale(0.8);
      transition: transform 0s;
    }
    .btn_job_info{
      display: inline-block;
      border-radius: 3px;
      font-size: 19px;
      padding: 5px 10px;
      cursor: default;
    }
    .btn_config_cancel{
      display: inline-block;
      background-color: rgba(148, 117, 117, 0.2);
      border-radius: 3px;
      font-size: 19px;
      padding: 5px 10px;
      cursor: pointer;
    }
    .btn_config_save{
      display: inline-block;
      background-color: rgba(10, 168, 236, 0.2);
      border-radius: 3px;
      font-size: 19px;
      padding: 5px 10px;
      cursor: pointer;
    }

}

.context-menu {
  position: fixed;
  background-color: rgba(173, 220, 180, 0.5);
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 5px;
  z-index: 1000;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
}

.menu-item {
  padding: 6px 10px;
  cursor: pointer;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.menu-text {
  font-family: Arial, sans-serif;
  color: #333;
}

.x6-port-body{
  visibility: hidden;
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 1px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 220px;
  margin-left: 10px;
  vertical-align: bottom;
}

.el-form-item.requireRedStar::before {
  content: "*";
  color: red;
  margin-right: 4px; /* 可以调整星号与文本之间的距离 */
}

.el-form-item.requireRedStar {
  //display: flex;
  align-items: center;
}
