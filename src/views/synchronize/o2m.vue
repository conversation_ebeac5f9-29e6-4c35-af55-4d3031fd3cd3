<template>
  <div class="app-container">
    <el-row :gutter="10" style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px" class="element-header-tool">
      <el-col :span="2.1">
        <div>
          <el-button size="small" type="primary" icon="el-icon-edit" @click="createJob">新增同步任务</el-button>
        </div>
      </el-col>
      <el-col :span="4">
        <div>
          <el-input
            v-model="jobNameBlurry"
            placeholder="作业名称"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="4">
        <div>
          <el-input
            v-model="createUserNameBlurry"
            placeholder="创建人"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="4">
        <div>
          <el-select v-model="departmentLabelBlurry" placeholder="请选择业务域" clearable @change="doSearch">
            <el-option
              v-for="item in departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </el-col>
      <el-col :span="1.1">
        <div>
          <el-button size="small" type="success" icon="el-icon-search" @click="doSearch">搜索</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col>
        <el-table
          :data="tableData"
          stripe
          highlight-current-row
          :header-cell-style="{textAlign: 'left'}"
          size="medium"
          style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
        >
          <el-table-column
            prop="jobName"
            label="作业名称"
            fixed
            sortable
            align="left"
            width="500"
          />
          <el-table-column
            prop="jobDesc"
            label="作业描述"
            align="left"
            width="200"
          />
          <el-table-column
            prop="status"
            label="运行状态"
            sortable
            align="left"
            width="120"
          >
            <template slot-scope="scope">
              <el-tag type="info" :style="setStatusFontColor(scope.row.status)" size="medium">{{ getJobStatusDesc(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="departmentLabel"
            label="业务域"
            sortable
            align="left"
            width="120"
          />
          <el-table-column
            prop="createName"
            label="创建人"
            sortable
            align="left"
            width="120"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            prop="modifyName"
            label="修改人"
            sortable
            align="left"
            width="120"
          />
          <el-table-column
            prop="modifyTime"
            label="修改时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            label="相关操作"
            width="700"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                plain
                type="primary"
                class="el-icon-link"
                :disabled="scope.row.status !== 3"
                @click="redirectUrl(scope.row)"
              >WebUi</el-button>
              <el-button
                size="mini"
                plain
                class="el-icon-edit-outline"
                @click="updateJob(scope.row)"
              >编辑</el-button>
              <el-button
                size="mini"
                type="info"
                plain
                class="el-icon-view"
                :loading="scope.row.validateButtonLoading"
                @click="validateJob(scope.row)"
              >校验作业</el-button>
              <el-button
                size="mini"
                type="primary"
                plain
                class="el-icon-top"
                :loading="onlineButtonLoading && onlineJobId === scope.row.jobId"
                :disabled="scope.row.status !== 0 && scope.row.status !== 6 && scope.row.status !== 8 && scope.row.status !== 4 && scope.row.status !== 12 && scope.row.status !== 14 && scope.row.status !== 19 && scope.row.status !== 21"
                @click="handleOnlinePre(scope.row)"
              >上线</el-button>
              <el-button
                size="mini"
                type="info"
                plain
                class="el-icon-document-copy"
                @click="handleCopy(scope.$index, scope.row)"
              >复制作业</el-button>
              <el-button
                size="mini"
                type="success"
                plain
                :loading="offlineButtonLoading && offlineJobId === scope.row.jobId"
                :disabled="scope.row.status !== 3 && scope.row.status !== 16"
                class="el-icon-bottom"
                @click="handleOffline(scope.$index, scope.row)"
              >下线</el-button>
              <el-button
                size="mini"
                type="danger"
                plain
                :disabled="scope.row.status !== 0 && scope.row.status !== 6 && scope.row.status !== -1 && scope.row.status !== 14"
                class="el-icon-delete"
                @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>

    </el-row>
    <el-row style="margin-top: 20px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px">
      <el-col>
        <el-pagination
          background
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[10, 15, 30, 50]"
          class="page_tt"
          :total="page_info.total_num"
          :page-size="page_info.page_size"
          :current-page="page_info.current_page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
        />
      </el-col>
    </el-row>

    <!-- 复制作业弹框 -->
    <el-dialog title="复制作业" width="40%" style="text-align: left" :close-on-click-modal="false" :show-close="false" :visible.sync="copyJobDialogFormVisible">
      <el-form ref="copyJobForm" :model="copyJobForm" :rules="rules" style="margin: -15px 3px -25px -5px">
        <el-form-item label="新作业名称" prop="jobName" label-width="100px">
          <el-input v-model="copyJobForm.jobName" autocomplete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel('copyJobForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('copyJobForm')">确 定</el-button>
      </div>
    </el-dialog>

    <o2-m-edit style="position: absolute" :is-show="isM2OEditShow" :edit-json="m2oEditData" @close="closeM2OEditDialog" />
    <OnlineJob style="position: absolute" :is-show="onlineJobFlag" :is-has-latest-checkpoint="isHasLatestCheckpoint" :online-job-name="onlineJobName" @close="handleOnlineRes"/>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import O2MEdit from './o2mEdit.vue'
import OnlineJob from '@/components/Online/index.vue'

import {
  copyJob,
  deleteJob,
  deployJob,
  deployJobWithLatestCheckpoint,
  get_job_webUrl,
  job_is_has_latest_checkpoint,
  queryJobPageable, stopJob,
  validateJob
} from '@/api/job'
import {getJobStatusDesc} from "@/views/realtime/js/common";

export default {
  name: 'Odps2Mq',
  components: {
    O2MEdit,
    OnlineJob
  },
  props: {
  },
  data() {
    return {
      isDrawerShow: false,
      tableData: [],
      page_info: {
        total_num: 0,
        current_page: 1,
        page_size: 10
      },
      loading: false,
      jobNameBlurry: '',
      createUserNameBlurry: '',
      departmentLabelBlurry: '',
      isM2OEditShow: false,
      m2oEditData: { 'jobId': 0, 'jobName': '', 'jobDesc': '', 'departmentLabel': '交易', 'libraOwners': [], 'dagJson': {} },
      copyJobForm: {
        jobName: ''
      },

      copyJobId: '',
      copyJobDialogFormVisible: false,
      isValidateButtonLoading: false,
      validateButtonLoadingJobId: 0,

      onlineJobFlag: false,
      onlineJobName: '',
      isHasLatestCheckpoint: '0',
      onlineJobId: 0,
      offlineJobId: 0,

      onlineButtonLoading: false,
      offlineButtonLoading: false,

      departmentOptions: [{
        value: '交易',
        label: '交易'
      }, {
        value: '社区',
        label: '社区'
      }, {
        value: '用户',
        label: '用户'
      }, {
        value: '增长',
        label: '增长'
      }, {
        value: '测试',
        label: '测试'
      }, {
        value: '客服',
        label: '客服'
      }, {
        value: '汇金',
        label: '汇金'
      }, {
        value: '算法',
        label: '算法'
      }, {
        value: '供应链',
        label: '供应链'
      }]
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
  },
  mounted() {
  },
  created() {
    this.doSearch()
  },
  methods: {
    createJob() {
      this.m2oEditData = { 'jobId': 0, 'jobName': '', 'jobDesc': '', 'departmentLabel': '交易', 'libraOwners': '', 'dagJson': {}}
      this.isM2OEditShow = true
    },
    updateJob(row) {
      if (this.user.username !== row.modifyName) {
        this.$confirm('是否确认解锁编辑?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.m2oEditData.jobId = row.jobId
          this.m2oEditData.jobName = row.jobName
          this.m2oEditData.jobDesc = row.jobDesc
          this.m2oEditData.departmentLabel = row.departmentLabel
          this.m2oEditData.libraOwners = row.libraOwners
          this.m2oEditData.dagJson = row.dagJson

          this.isM2OEditShow = true
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消编辑'
          })
        })
      } else {
        this.m2oEditData.jobId = row.jobId
        this.m2oEditData.jobName = row.jobName
        this.m2oEditData.jobDesc = row.jobDesc
        this.m2oEditData.departmentLabel = row.departmentLabel
        this.m2oEditData.libraOwners = row.libraOwners
        this.m2oEditData.dagJson = row.dagJson

        this.isM2OEditShow = true
      }
    },
    closeM2OEditDialog(tag){
      this.isM2OEditShow = false
      if (tag === '1') {
        this.doSearch()
      }
    },
    doSearch() {
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
      pathParams.push({ 'paramName': 'createUserName', 'paramValue': this.createUserNameBlurry })
      pathParams.push({ 'paramName': 'departmentLabel', 'paramValue': this.departmentLabelBlurry })
      pathParams.push({ 'paramName': 'jobName', 'paramValue': this.jobNameBlurry })
      pathParams.push({ 'paramName': 'type', 'paramValue': 'SYNC_KAFKA_TEMPLATE' })

      queryJobPageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })
    },
    fillTableData(data) {
      const tempTableData = []
      data.forEach(item => {
        tempTableData.push({ 'jobId': item.id, 'jobName': item.jobName, 'jobDesc': item.jobDesc, 'createName': item.createUserName, 'status': item.status, 'createTime': item.createTime, 'modifyTime': item.updateTime, 'modifyName': item.lastModifyUser, 'departmentLabel': item.departmentLabel, 'libraOwners': item.libraOwners, 'dagJson': item.dagJson, 'validateButtonLoading' : false })
      })
      this.tableData = tempTableData
    },
    handleSizeChange(val) {
      this.page_info.page_size = val
      this.doSearch()
    },
    handleCurrentChange(val) {
      this.page_info.current_page = val
      this.doSearch()
    },
    cancel(formName) {
      this.copyJobDialogFormVisible = false
      this.$refs[formName].resetFields()
    },
    handleDelete(index, row) {
      this.$confirm('是否确认删除 ' + row.jobName + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteJob(row.jobId).then(res => {
          if (res.status === 200) {
            this.$message({
              type: 'success',
              message: '删除进入审批流程!'
            })
          }
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.doSearch()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    redirectUrl(row) {
      get_job_webUrl(row.jobId).then( res=> {
        if (res.code === '200') {
          window.open(res.data)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    validateJob(row) {
      row.validateButtonLoading = true
      validateJob(row.jobId).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '校验通过 🎉'
          })
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: '校验失败 😭' + '<br>' + res.mes
          })
        }
        row.validateButtonLoading = false
      })
    },
    handleOnlinePre(row) {
      this.onlineJobId = row.jobId
      job_is_has_latest_checkpoint(row.jobId).then(res => {
        if (res.code === '200') {
          this.onlineJobFlag = true
          this.onlineJobName = row.jobName
          if (res.data === '0') {
            this.isHasLatestCheckpoint = '0'
          }
          if (res.data === '1') {
            this.isHasLatestCheckpoint = '1'
          }
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    handleOnlineRes(tag, startType) {
      this.onlineJobFlag = false
      if (tag === '1') {
        if (startType === '0') {
          this.handleOnlineOld(this.onlineJobId)
        }
        if (startType === '1') {
          this.handleOnlineNew(this.onlineJobId)
        }
      }
    },
    handleOnlineOld(jobId) {
      this.onlineButtonLoading = true
      deployJob(jobId).then(res => {
        this.onlineButtonLoading = false
        if (res.status === 200) {
          this.$message({
            type: 'success',
            message: '上线进入审批流程!'
          })
        }
        if (res.code === '200') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'success',
            message: '提交上线成功!'
          })
          this.doSearch()
        }
        if (res.code === '500') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    handleOnlineNew(jobId) {
      this.onlineButtonLoading = true
      deployJobWithLatestCheckpoint(jobId).then(res => {
        if (res.status === 200) {
          this.onlineButtonLoading = false
          this.$message({
            type: 'success',
            message: '上线进入审批流程!'
          })
        }
        if (res.code === '200') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'success',
            message: '提交上线成功!'
          })
          this.doSearch()
        }
        if (res.code === '500') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    handleCopy(index, row) {
      this.copyJobId = row.jobId
      this.copyJobDialogFormVisible = true
    },
    handleOffline(index, row) {
      this.$confirm('是否确认下线 ' + row.jobName + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.offlineButtonLoading = true
        this.offlineJobId = row.jobId
        stopJob(row.jobId).then(res => {
          if (res.status === 200) {
            this.offlineButtonLoading = false
            this.$message({
              type: 'success',
              message: '下线进入审批流程!'
            })
          }
          if (res.code === '200') {
            this.offlineButtonLoading = false
            this.$message({
              type: 'success',
              message: '提交下线成功!'
            })
            this.doSearch()
          }
          if (res.code === '500') {
            this.offlineButtonLoading = false
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消下线'
        })
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          copyJob(this.copyJobId, this.copyJobForm.jobName).then(res => {
            if (res.code === '200') {
              this.copyJobDialogFormVisible = false
              this.$message({
                type: 'success',
                message: '复制作业成功!'
              })
              this.copyJobForm.jobName = ''
              this.jobNameBlurry = ''
              this.createUserNameBlurry = ''
              this.departmentLabelBlurry = ''
              this.doSearch()
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        } else {
          return false
        }
      })
    },
    setStatusFontColor(jobStatus) {
      switch (jobStatus) {
        case 0:
          return 'color: #930dd3'
        case 1:
          return 'color: #12AAAD'
        case 3:
          return 'color: red'
        case 6:
          return 'color: #221b0a'
        default:
          return 'color: #bcbdae'
      }
    },
    getJobStatusDesc(statusCode) {
      return getJobStatusDesc(statusCode)
    },

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>
