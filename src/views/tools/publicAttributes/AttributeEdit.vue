<template>
  <div class="app-container">
    <el-dialog
      :title="titleShowTag"
      :visible.sync="tag"
      width="30%"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
        <el-form-item label="属性名称" prop="name">
          <el-input v-model="ruleForm.name" :disabled="ruleForm.id !== 0" />
        </el-form-item>
        <el-form-item label="属性描述" prop="description">
          <el-input v-model="ruleForm.description" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { createAttribute, updateAttribute } from '@/api/attribute'

export default {
  name: 'AttributeEdit',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    editJson: {
      type: Object,
      default: function() {
        return { 'id': 0, 'name': '', 'description': '' }
      }
    }
  },
  data() {
    return {
      ruleForm: {
        id: 0,
        name: '',
        description: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入属性名称', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入属性描述', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow
    },
    titleShowTag() {
      return this.ruleForm.id === 0 ? '创建属性' : '修改属性'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.ruleForm.id = this.editJson.id
        this.ruleForm.name = this.editJson.name
        this.ruleForm.description = this.editJson.description
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.id === 0) {
            this.saveAttribute()
          } else {
            this.updateAttribute()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$emit('close', '0')
    },
    saveAttribute() {
      const attribute = { 'name': '', 'description': '' }
      attribute.name = this.ruleForm.name
      attribute.description = this.ruleForm.description
      createAttribute(attribute).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '添加成功!'
          })
          this.$emit('close', '1')
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    updateAttribute() {
      const attribute = { 'id': 0, 'name': '', 'description': '' }
      attribute.id = this.ruleForm.id
      attribute.name = this.ruleForm.name
      attribute.description = this.ruleForm.description
      updateAttribute(attribute).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.$emit('close', '1')
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

