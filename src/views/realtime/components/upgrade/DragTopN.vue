<template>
  <div class="app-container">
    <el-dialog
      title="TopN操作配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px;margin-top: -20px">
        <el-form ref="ruleFormDragTopN" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item label="选择字段" prop="selectExpression">
            <el-input
              v-model="ruleForm.selectExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> username,gender,age,row_num  如需获取排序结果，使用row_num即可；如需选择所有字段，可以填写 *"
              clearable
            />
          </el-form-item>
          <el-form-item label="分区字段" prop="partitionExpression">
            <el-input
              v-model="ruleForm.partitionExpression"
              placeholder="example=> gender"
              clearable
            />
          </el-form-item>
          <el-form-item label="排序字段" prop="orderByExpression">
            <el-input
              v-model="ruleForm.orderByExpression"
              placeholder="example=> age"
              clearable
            />
          </el-form-item>
          <el-form-item label="排序顺序">
            <div class="col-content element-value" style="text-align: right">
              <el-radio v-model="ruleForm.order" label="ASC" border>ASC</el-radio>
              <el-radio v-model="ruleForm.order" label="DESC" border>DESC</el-radio>
            </div>
          </el-form-item>
          <el-form-item label="Top数量">
            <el-input-number v-model="ruleForm.topN" :min="1" :max="100" size="small"></el-input-number>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DragTopN',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleForm: {
        selectExpression: '',
        partitionExpression: '',
        orderByExpression: '',
        order: 'DESC',
        topN: 1
      },
      rules: {
        selectExpression: [
          { required: true, message: '请输入选择字段', trigger: 'blur' }
        ],
        partitionExpression: [
          { required: true, message: '请选择分区字段', trigger: 'blur' }
        ],
        orderByExpression: [
          { required: true, message: '请选择排序字段', trigger: 'blur' }
        ]
      }

    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'TopnTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        const tempExtend = JSON.parse(this.extend)
        this.ruleForm.selectExpression = tempExtend.selectExpressionOrigin === undefined ? '' : tempExtend.selectExpressionOrigin
        this.ruleForm.partitionExpression = tempExtend.partitionExpression === undefined ? '' : tempExtend.partitionExpression
        this.ruleForm.orderByExpression = tempExtend.orderByExpressionOrigin === undefined ? '' : tempExtend.orderByExpressionOrigin
        this.ruleForm.order = tempExtend.order === undefined ? 'DESC' : tempExtend.order
        this.ruleForm.topN = tempExtend.n === undefined ? 1 : tempExtend.n
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragTopN'].validate((valid) => {
          const transformation = { 'selectExpression': '', 'partitionExpression': '', 'orderByExpression': '', 'orderByExpressionOrigin': '', 'selectExpressionOrigin': '', 'order': '', 'topN': 1 }
          transformation.selectExpression = this.ruleForm.selectExpression.trim().replace(/\n/g, '')
          transformation.partitionExpression = this.ruleForm.partitionExpression.trim()
          transformation.orderByExpressionOrigin = this.ruleForm.orderByExpression.trim()
          transformation.orderByExpression = this.ruleForm.orderByExpression.trim() + ' ' + this.ruleForm.order.trim()
          transformation.order = this.ruleForm.order.trim()
          transformation.n = this.ruleForm.topN
          transformation.selectExpressionOrigin = this.ruleForm.selectExpression.trim()
          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragGroupBy'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.$refs['ruleFormDragTopN'].resetFields()
      }
    }

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

