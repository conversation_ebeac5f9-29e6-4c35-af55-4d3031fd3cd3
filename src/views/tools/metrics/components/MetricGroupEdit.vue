<template>
  <div class="app-container">
    <el-dialog
      :title="titleShowTag"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="tag"
      width="55%"
      top="10vh"
    >
      <el-form ref="ruleFormMetricGroup" :model="ruleForm" :rules="rules" label-width="100px" style="margin-top: -20px;margin-bottom: -30px">
        <el-form-item label="名称" prop="metricGroupName">
          <el-input v-model="ruleForm.metricGroupName" clearable :disabled="editTag" placeholder="输入指标组名称" />
        </el-form-item>
        <el-form-item label="描述" prop="metricGroupDesc">
          <el-input v-model="ruleForm.metricGroupDesc" clearable placeholder="输入指标组描述" />
        </el-form-item>
        <el-form-item label="业务域" prop="teBusinessIdAndSubjectId">
          <el-cascader
            v-model="ruleForm.teBusinessIdAndSubjectId"
            :disabled="editTag"
            :options="businessOption"
            placeholder="请选择业务域和业务主题"
            :props="{ expandTrigger: 'hover' }"
            ></el-cascader>
        </el-form-item>
        <el-form-item label="* 维度" prop="teCommonAttributesIds">
          <div>
            <el-tag
              v-for="tag in dynamicTags"
              :key="tag"
              :closable="!editTag"
              size="medium"
              :disable-transitions="false"
              @close="handleClose(tag)"
            >
              {{ tag }}
            </el-tag>
            <el-select v-if="inputVisible" ref="saveTagInput" v-model="optionValue" filterable placeholder="请选择" @change="handleInputConfirm">
              <el-option
                v-for="item in options"
                :key="item.commonAttributeName"
                :label="item.commonAttributeName"
                :value="item.commonAttributeName"
              />
            </el-select>
            <el-button v-else class="button-new-tag" size="mini" :disabled="editTag" @click="showInput">+ New Tag</el-button>
          </div>
        </el-form-item>
        <el-form-item label="存储topic id" prop="reDataSourceMetadataId">
          <el-input
            v-model="ruleForm.reDataSourceMetadataId"
            placeholder="输入存储topic id"
            clearable
            :disabled="editTag"
          />
        </el-form-item>
        <el-form-item label="作业名" prop="job">
          <el-input v-model="ruleForm.job" placeholder="输入产出作业名称" clearable />
        </el-form-item>
        <el-form-item label="扩展字段" prop="ext">
          <el-input v-model="ruleForm.ext" type="textarea" autosize placeholder="输入指标组扩展字段" resize="none" clearable />
        </el-form-item>
        <el-form-item label="指标组样例" prop="dataDemo">
          <el-input v-model="ruleForm.dataDemo" type="textarea" autosize placeholder="输入指标组样例" resize="none" clearable />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('ruleFormMetricGroup')">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleFormMetricGroup')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { validateJson, validateDataSourceMetadataId } from '../js/validator'
import { getAllCommonAttributes, createMetricGroup, updateMetricGroup } from '@/api/metrics'

export default {
  name: 'MetricGroupEdit',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    editJson: {
      type: Object,
      default: function() {
        return { 'id': 0, 'metricGroupName': '', 'metricGroupDesc': '', 'reDataSourceMetadataId': '', 'teBusinessId': '0', 'teBusinessSubjectId': '0','teCommonAttributesIds': '', 'isHaveMetricDetail': 0, 'dataDemo': '', 'ext': '', 'job': '' }
      }
    }
  },
  data() {
    return {
      ruleForm: {
        id: 0,
        metricGroupName: '',
        metricGroupDesc: '',
        teBusinessIdAndSubjectId: [],
        reDataSourceMetadataId: '',
        teCommonAttributesIds: '',
        isHaveMetricDetail: 0,
        dataDemo: '',
        ext: '',
        job: ''
      },
      rules: {
        metricGroupName: [
          { required: true, message: '请输入指标组名称', trigger: 'blur' }
        ],
        metricGroupDesc: [
          { required: true, message: '请输入指标组描述', trigger: 'blur' }
        ],
        teBusinessIdAndSubjectId: [
          { required: true, message: '请选择业务域和业务主题', trigger: 'change' }
        ],
        reDataSourceMetadataId: [
          { required: true, message: '请输入存储topic id', trigger: 'blur' },
          { validator: validateDataSourceMetadataId, message: '格式必须为正整数', trigger: ['blur'] }
        ],
        ext: [
          { validator: validateJson, message: '内容必须符合json格式', trigger: ['blur'] }
        ],
        dataDemo: [
          { validator: validateJson, message: '内容必须符合json格式', trigger: ['blur'] }
        ],
        job: [
          { required: true, message: '请输入产出作业名称', trigger: 'blur' }
        ]
      },
      dynamicTags: [],
      inputVisible: false,
      options: [{
        commonAttributeName: 'spu_id'
      }, {
        commonAttributeName: 'sku_id'
      }, {
        commonAttributeName: 'user_id'
      }, {
        commonAttributeName: 'brand'
      }, {
        commonAttributeName: 'category'
      }],
      optionValue: '',
      businessOption: [
        {
          value: '0',
          label: '默认',
          children: [{
            value: '200000',
            label: '默认'
          }, {
            value: '200001',
            label: '订单'
          }, {
            value: '3645824',
            label: '埋点'
          }]
        },
        {
          value: '1',
          label: '交易',
          children: [{
            value: '210000',
            label: '默认'
          }, {
            value: '210001',
            label: '订单'
          }, {
            value: '210002',
            label: '商品'
          }, {
            value: '210003',
            label: '用户'
          }, {
            value: '210006',
            label: '流量'
          }, {
            value: '8420423',
            label: '雷达'
          }, {
            value: '2111396',
            label: '营销活动'
          }, {
            value: '8734757',
            label: '直播'
          }, {
            value: '2994285',
            label: '出价&库存'
          }, {
            value: '2806718',
            label: '出价'
          }, {
            value: '3556023',
            label: '寄存'
          }, {
            value: '8792358',
            label: '客服'
          }, {
            value: '8661900',
            label: 'BC端'
          }, {
            value: '1572381',
            label: '商家'
          }]
        },
        {
          value: '2',
          label: '社区',
          children: [{
            value: '220000',
            label: '默认'
          }, {
            value: '220006',
            label: '流量'
          }]
        },
        {
          value: '3',
          label: '增长',
          children: [{
            value: '230000',
            label: '默认'
          }, {
            value: '230003',
            label: '用户'
          }, {
            value: '1522069',
            label: '国际'
          }]
        },
        {
          value: '4',
          label: '搜推',
          children: [{
            value: '240000',
            label: '默认'
          }, {
            value: '240006',
            label: '流量'
          }]
        },
        {
          value: '5',
          label: '供应链',
          children: [{
            value: '250000',
            label: '默认'
          }, {
            value: '7740637',
            label: '仓储'
          }, {
            value: '3140159',
            label: '履约'
          }, {
            value: '5859956',
            label: '运配'
          }, {
            value: '1035172',
            label: '劳务'
          }, {
            value: '3408191',
            label: '生产'
          }]
        },
        {
          value: '3494518',
          label: '大数据',
          children: [{
            value: '7273203',
            label: '默认'
          }, {
            value: '1972159',
            label: '智能运营系统'
          }]
        },
        {
          value: '5434147',
          label: '投放',
          children: [{
            value: '6374312',
            label: '默认'
          }, {
            value: '9408224',
            label: '硬广'
          }, {
            value: '3170406',
            label: '软广'
          }, {
            value: '4079922',
            label: '商投'
          }]
        },
        {
          value: '1777122',
          label: '商家',
          children: [{
            value: '1757291',
            label: '默认'
          }, {
            value: '5324177',
            label: '透传'
          }, {
            value: '5799815',
            label: 'ERP'
          }]
        },
        {
          value: '6',
          label: '用户',
          children: [{
            value: '7000340',
            label: '默认'
          }]
        }
      ]
    }
  },
  computed: {
    tag() {
      return this.isShow
    },
    titleShowTag() {
      return this.ruleForm.id === 0 ? '新增指标组' : '修改指标组'
    },
    editTag() {
      return this.ruleForm.isHaveMetricDetail === 1
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.ruleForm.id = this.editJson.id
        this.ruleForm.metricGroupName = this.editJson.metricGroupName
        this.ruleForm.metricGroupDesc = this.editJson.metricGroupDesc
        const arr = []
        arr.push(this.editJson.teBusinessId)
        arr.push(this.editJson.teBusinessSubjectId)
        this.ruleForm.teBusinessIdAndSubjectId = arr
        this.ruleForm.reDataSourceMetadataId = this.editJson.reDataSourceMetadataId
        this.ruleForm.isHaveMetricDetail = this.editJson.isHaveMetricDetail
        this.ruleForm.dataDemo = this.editJson.dataDemo
        this.ruleForm.ext = this.formatExt(this.editJson.ext)
        this.formatExt(this.editJson.ext)
        this.ruleForm.job = this.editJson.job
        if (this.editJson.teCommonAttributesIds !== '') {
          this.dynamicTags = this.editJson.teCommonAttributesIds.split(',')
        }
      }
    }
  },
  mounted() {
    getAllCommonAttributes().then(res => {
      const commonAttributesArr = []
      if (res.code === '200') {
        res.data.forEach(item => {
          commonAttributesArr.push({ 'commonAttributeName': item.name })
        })
        this.options = commonAttributesArr
      }
    })
  },
  methods: {
    formatExt(ext) {
      if (ext !== undefined) {
        return ext.replace(/{/g, '{\n    ').replace(/}/g, '\n}').replace(/,/g, ',\n    ')
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dynamicTags.length === 0) {
            this.$message({
              showClose: true,
              message: '特别警告，维度信息没有选择',
              type: 'warning'
            })
          } else {
            let str = ''
            for (let i = 0; i < this.dynamicTags.length; i++) {
              str += this.dynamicTags[i] + ','
            }
            if (str.length > 0) {
              str = str.substr(0, str.length - 1)
            }
            this.ruleForm.teCommonAttributesIds = str
            if (this.ruleForm.id === 0) {
              this.createMetricGroup()
            } else {
              this.updateMetricGroup()
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
      this.clearData()
    },
    resetForm(formName) {
      this.clearData()
      this.$refs[formName].resetFields()
      this.$emit('close', '0')
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.focus()
      })
    },
    handleInputConfirm() {
      const inputValue = this.optionValue
      if (inputValue) {
        if (this.dynamicTags.indexOf(inputValue) === -1) {
          this.dynamicTags.push(inputValue)
        } else {
          this.$message('已经选择过 ' + inputValue + ' 了！')
        }
      }
      this.inputVisible = false
      this.optionValue = ''
    },
    createMetricGroup() {
      const metricGroup = { 'reDataSourceMetadataId': 0, 'metricGroupName': '', 'metricGroupDesc': '', 'teBusinessId': 0, 'teBusinessSubjectId': 0, 'teCommonAttributesIds': '', 'job': '', 'ext': '', 'dataDemo': '' }
      metricGroup.reDataSourceMetadataId = parseInt(this.ruleForm.reDataSourceMetadataId)
      metricGroup.metricGroupName = this.ruleForm.metricGroupName
      metricGroup.metricGroupDesc = this.ruleForm.metricGroupDesc
      metricGroup.teBusinessId = parseInt(this.ruleForm.teBusinessIdAndSubjectId[0])
      metricGroup.teBusinessSubjectId = parseInt(this.ruleForm.teBusinessIdAndSubjectId[1])
      metricGroup.teCommonAttributesIds = this.ruleForm.teCommonAttributesIds
      metricGroup.job = this.ruleForm.job
      metricGroup.ext = this.ruleForm.ext
      metricGroup.dataDemo = this.ruleForm.dataDemo
      createMetricGroup(metricGroup).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '新增成功!'
          })
          this.$emit('close', '1')
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    updateMetricGroup() {
      const metricGroup = { 'id': 0, 'reDataSourceMetadataId': 0, 'metricGroupName': '', 'metricGroupDesc': '', 'teBusinessId': 0, 'teBusinessSubjectId': 0, 'teCommonAttributesIds': '', 'job': '', 'ext': '', 'dataDemo': '' }
      metricGroup.id = this.ruleForm.id
      metricGroup.reDataSourceMetadataId = parseInt(this.ruleForm.reDataSourceMetadataId)
      metricGroup.metricGroupName = this.ruleForm.metricGroupName
      metricGroup.metricGroupDesc = this.ruleForm.metricGroupDesc
      metricGroup.teBusinessId = parseInt(this.ruleForm.teBusinessIdAndSubjectId[0])
      metricGroup.teBusinessSubjectId = parseInt(this.ruleForm.teBusinessIdAndSubjectId[1])
      metricGroup.teCommonAttributesIds = this.ruleForm.teCommonAttributesIds
      metricGroup.job = this.ruleForm.job
      metricGroup.ext = this.ruleForm.ext
      metricGroup.dataDemo = this.ruleForm.dataDemo
      updateMetricGroup(metricGroup).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.$emit('close', '1')
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    clearData() {
      this.dynamicTags = []
    }
  }
}
</script>

<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    height: 30px;
    line-height: 28px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 200px;
    margin-left: 15px;
    vertical-align: bottom;
  }
</style>
