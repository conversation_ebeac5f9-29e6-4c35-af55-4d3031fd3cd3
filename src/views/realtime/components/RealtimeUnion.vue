<template>
  <div class="app-container">
    <el-dialog
      title="UNION ALL操作配置"
      :visible.sync="tag"
      width="50%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-groupBy-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>左流节点编号:</span>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="col-content element-value">
              <el-input-number v-model="leftNumber" :min="99" size="small" style="width: 188px" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="col-content element-remark">
              <el-tooltip placement="bottom">
                <div slot="content">
                  双击填写好配置的左流节点，提示内容里面会有节点编号！
                </div>
                <div class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>左流选择字段:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="leftSelectExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> username,age,`userid` as `user_id`"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span>左流过滤条件:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="leftFilterExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> coalesce(username,'') <> ''"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>右流节点编号:</span>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="col-content element-value">
              <el-input-number v-model="rightNumber" :min="99" size="small" style="width: 188px" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="col-content element-remark">
              <el-tooltip placement="bottom">
                <div slot="content">
                  双击填写好配置的右流节点，提示内容里面会有节点编号！
                </div>
                <div class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>右流选择字段:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="rightSelectExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> username,age,`user_id`"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span>右流过滤条件:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="rightFilterExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> age > 17"
                clearable
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RealtimeUnion',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      leftNumber: 99,
      rightNumber: 99,
      leftSelectExpression: '',
      leftFilterExpression: '',
      rightSelectExpression: '',
      rightFilterExpression: '',
      showOperation: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'UnionTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'UnionTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.leftNumber = JSON.parse(sessionStorage.getItem('editJsonContent')).leftNumber
            this.rightNumber = JSON.parse(sessionStorage.getItem('editJsonContent')).rightNumber
            this.leftSelectExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).leftSelectExpression
            this.leftFilterExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).leftFilterExpression
            this.rightSelectExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).rightSelectExpression
            this.rightFilterExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).rightFilterExpression
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'leftSelectExpression': '', 'leftFilterExpression': '', 'rightSelectExpression': '', 'rightFilterExpression': '', 'leftNumber': 1, 'rightNumber': 1, 'transformation': '', 'showOperation': '' }

      const transformation = { 'leftSelectExpression': '', 'leftFilterExpression': '', 'rightSelectExpression': '', 'rightFilterExpression': '' }
      transformation.leftSelectExpression = this.leftSelectExpression.trim().replace(/\n/g, '')
      transformation.leftFilterExpression = this.leftFilterExpression.trim().replace(/\n/g, ' ')
      transformation.rightSelectExpression = this.rightSelectExpression.trim().replace(/\n/g, '')
      transformation.rightFilterExpression = this.rightFilterExpression.trim().replace(/\n/g, ' ')

      resultJson.leftNumber = this.leftNumber
      resultJson.rightNumber = this.rightNumber
      resultJson.leftSelectExpression = this.leftSelectExpression.trim()
      resultJson.leftFilterExpression = this.leftFilterExpression.trim()
      resultJson.rightSelectExpression = this.rightSelectExpression.trim()
      resultJson.rightFilterExpression = this.rightFilterExpression.trim()
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      this.showOperation.push('【左流节点编号】' + this.leftNumber)
      this.showOperation.push('【左流选择字段】' + this.leftSelectExpression.trim().replace(/\n/g, ''))
      this.showOperation.push('【左流过滤条件】' + this.leftFilterExpression.trim().replace(/\n/g, ' '))
      this.showOperation.push('【右流节点编号】' + this.rightNumber)
      this.showOperation.push('【右流选择字段】' + this.rightSelectExpression.trim().replace(/\n/g, ''))
      this.showOperation.push('【右流过滤条件】' + this.rightFilterExpression.trim().replace(/\n/g, ' '))
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)
      this.clearData()
    },
    clearData() {
      this.leftSelectExpression = ''
      this.leftFilterExpression = ''
      this.rightSelectExpression = ''
      this.rightFilterExpression = ''
      this.leftNumber = 99
      this.rightNumber = 99
    },
    isFullEdit() {
      if (this.leftSelectExpression.trim() === '' || this.rightSelectExpression.trim() === '' || this.leftNumber === 99 || this.rightNumber === 99) {
        return '0'
      } else {
        return '1'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-groupBy-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

</style>

