<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="表名" prop="whiteTableName">
            <el-input v-model="form.whiteTableName" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="自增ID" />
<!--        <el-table-column prop="createTime" label="创建时间" />-->
<!--        <el-table-column prop="modifyTime" label="修改时间" />-->
        <el-table-column prop="whiteTableName" label="表名" />
        <el-table-column v-if="checkPer(['admin','teWhiteTableForBinlog:edit','teWhiteTableForBinlog:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTeWhiteTableForBinlog from '@/api/teWhiteTableForBinlog'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation.vue'
import crudOperation from '@crud/CRUD.operation.vue'
import udOperation from '@crud/UD.operation.vue'
import pagination from '@crud/Pagination.vue'

const defaultForm = { id: null, createTime: null, modifyTime: null, whiteTableName: null }
export default {
  name: 'TeWhiteTableForBinlog',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '白名单binlog', url: 'api/teWhiteTableForBinlog', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTeWhiteTableForBinlog }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'teWhiteTableForBinlog:add'],
        edit: ['admin', 'teWhiteTableForBinlog:edit'],
        del: ['admin', 'teWhiteTableForBinlog:del']
      },
      rules: {
        whiteTableName: [
          { required: true, message: '表名不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
