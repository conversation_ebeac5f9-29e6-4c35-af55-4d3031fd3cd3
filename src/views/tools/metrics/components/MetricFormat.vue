<template>
  <div class="app-container">
    <el-dialog
      title="指标统一格式介绍"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="tag"
      width="50%"
      top="10vh"
    >
      <el-row style="margin-top: -35px;margin-bottom: -35px">
        <json-viewer :value="jsonContent" :expand-depth="expandDepth" :copyable="copyConfig" theme="jv-light" />
      </el-row>
      <el-divider>重要字段解释<i class="el-icon-bottom" /></el-divider>
      <div style="margin-top: -5px;margin-bottom: -50px">
        <ul>
          <li style="margin-bottom: 10px">metric_value：提供该指标各具体指标字段的指标值</li>
          <li style="margin-bottom: 10px">dim_name：由该指标各维度的维度字段名称通过逗号拼接的字符串</li>
          <li style="margin-bottom: 10px">dim_value：提供该指标各具体维度字段的维度值</li>
          <li>ext：提供一些扩展字段如果有需要的话</li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import JsonViewer from 'vue-json-viewer'

export default {
  name: 'MetricFormat',
  components: {
    JsonViewer
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    tag() {
      return this.isShow
    }
  },
  data() {
    return {
      copyConfig: { copyText: '复制', copiedText: '复制成功' },
      expandDepth: 2,
      jsonContent: {
        'metric_name': '指标名称',
        'metric_value': {
          '指标1': 95,
          '指标2': '10.5%'
        },
        'dim_name': '维度1,维度2,维度3',
        'dim_value': {
          '维度1': 'nike',
          '维度2': '鞋',
          '维度3': '20221102'
        },
        'ext': {
          '扩展字段1': 'balabala',
          '扩展字段2': 'balabala'
        },
        'ts': '指标处理时间,取值为:13位时间戳',
        'job': '对应开发任务的名称,使用方忽略',
        'unique_key': "业务唯一键,取值为:md5(dim_value(sorted),'_',+metric_name)",
        'track_id': '全局唯一id,取值为:UUID()'
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
