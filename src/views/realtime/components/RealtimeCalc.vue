<template>
  <div class="app-container">
    <el-dialog
      title="过滤操作配置"
      :visible.sync="tag"
      width="57%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-calc-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="3">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>选择字段:</span>
            </div>
          </el-col>
          <el-col :span="20">
            <div class="col-content element-value">
              <el-input
                v-model="selectExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> id,age,name  如果选择所有字段，可以填写 *"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="3">
            <div class="col-content element-desc">
              <span>过滤条件:</span>
            </div>
          </el-col>
          <el-col :span="20">
            <div class="col-content element-value">
              <el-input
                v-model="filterExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> date='20220824' and ht='22'  如果没有过滤条件可以什么都不填"
                clearable
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RealtimeCalc',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectExpression: '',
      filterExpression: '',
      showOperation: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'CalcTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'CalcTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.selectExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).selectExpression
            this.filterExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).filterExpression
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'selectExpression': '', 'filterExpression': '', 'transformation': '', 'showOperation': '' }

      const transformation = { 'selectExpression': '', 'filterExpression': '' }
      transformation.selectExpression = this.selectExpression.trim().replace(/\n/g, '')
      transformation.filterExpression = this.filterExpression.trim().replace(/\n/g, ' ')

      resultJson.selectExpression = this.selectExpression.trim()
      resultJson.filterExpression = this.filterExpression.trim()
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      this.showOperation.push('【选择字段】' + this.selectExpression.trim().replace(/\n/g, ''))
      this.showOperation.push('【过滤条件】' + this.filterExpression.trim().replace(/\n/g, ' '))
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)
      this.clearData()
    },
    clearData() {
      this.selectExpression = ''
      this.filterExpression = ''
    },
    isFullEdit() {
      if (this.selectExpression.trim() === '') {
        return '0'
      } else {
        return '1'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-calc-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

</style>

