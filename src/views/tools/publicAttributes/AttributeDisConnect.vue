<template>
  <div class="app-container">
    <el-dialog
      title="未关联topic"
      :visible.sync="tag"
      width="50%"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :before-close="handleClose"
      top="10vh"
    >
      <div style="margin-top: -20px">
        <el-table
          ref="multipleTable"
          :data="tableData"
          style="font-size: medium"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column property="table_name" label="topic" width="300" />
          <el-table-column property="field_desc" label="字段值" width="200" />
          <el-table-column property="data_source_name" label="集群名称" width="300" />
        </el-table>
      </div>

      <div style="text-align: right;margin-top: 10px;margin-bottom: -20px">
        <el-button
          size="medium"
          type="primary"
          plain
          class="el-icon-edit"
          style="margin-top: 10px"
          :disabled="multipleSelection.length === 0"
          @click="updateBatch"
        >批量更新</el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import { getDisConnectTopics, updateConnectBatch } from '@/api/attribute'

export default {
  name: 'AttributeDisConnect',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    attributeId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      tableData: [],
      multipleSelection: []
    }
  },
  computed: {
    tag() {
      return this.isShow
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.initTableData()
      }
    }
  },
  methods: {
    handleClose() {
      this.$refs.multipleTable.clearSelection()
      this.$emit('close')
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    updateBatch() {
      const tempParam = { 'attributeId': 0, 'tableFields': [] }
      tempParam.attributeId = this.attributeId
      this.multipleSelection.forEach(item => {
        tempParam.tableFields.push(item.id)
      })
      updateConnectBatch(tempParam).then(res => {
        if (res.code === '200') {
          this.initTableData()
          this.$message({
            type: 'success',
            message: '批量更新成功!'
          })
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    initTableData() {
      getDisConnectTopics(this.attributeId).then(res => {
        if (res.code === '200') {
          const tempTableData = []
          res.data.forEach(item => {
            tempTableData.push({ 'id': item.id, 'table_name': item.table_name, 'field_desc': item.field_desc, 'data_source_name': item.data_source_name })
          })
          this.tableData = tempTableData
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

