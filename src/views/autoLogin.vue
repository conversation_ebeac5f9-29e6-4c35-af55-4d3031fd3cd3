<template>
  <div />
</template>
<script>

import { getToken } from '@/utils/auth' // getToken from cookie

export default {
  name: 'AutoLogin',
  created() {
    this.handleLogin()
  },
  methods: {
    handleLogin() {
      this.$store.dispatch('LoginWithSsoToken', getToken()).then(() => {
        this.loading = false
        this.$router.push({ path: '/' })
      }).catch(() => {
        this.loading = false
        // this.getCode()
      })
    }
  }
}
</script>
