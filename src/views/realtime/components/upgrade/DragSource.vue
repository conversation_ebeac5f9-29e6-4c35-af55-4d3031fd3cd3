<template>
  <div class="app-container">
    <el-dialog
      title="数据源配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px">
        <el-form ref="ruleFormDragSource" :rules="rules" :model="ruleForm" label-width="120px" style="margin-top: -20px">
          <el-form-item label="数据源" prop="sourceTableId">
            <el-select
              id="sourceSelector"
              v-model="ruleForm.sourceTableId"
              filterable
              remote
              clearable
              reserve-keyword
              no-data-text="无匹配数据源"
              placeholder="请搜索数据源表..."
              style="width: 620px"
              :remote-method="remoteMethod"
              :loading="iLoading"
              @change="selectChange"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="预览字段">
            <div style="display: flex;align-items: flex-start;">
              <el-input
                v-model="fieldsCheck"
                type="textarea"
                :rows="1"
                autosize
                readonly
                resize="none"
                style="margin-right: 3px"
                placeholder="预览该数据源的全部字段..."
              />
              <el-button type="info" @click="generateFields">{{ fieldsCheckShow?'一键预览':'取消预览' }}</el-button>
            </div>
          </el-form-item>
          <el-form-item label="消费起始时间" prop="consumerDatetime">
            <el-date-picker
              v-model="ruleForm.consumerDatetime"
              type="datetime"
              value-format="timestamp"
              default-value="2022-08-14"
              placeholder="选择日期时间"
            />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import {
  get_table_detail_Info_by_ids,
  getDataSourceTypeByTableId,
  getTableFields,
  getTableInfoCommonSql
} from '@/api/job'

export default {
  name: 'DragSource',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      options: [],
      iLoading: false,
      fieldsCheck: '',
      fieldsCheckShow: true,
      consumerDatetime: '',

      ruleForm: {
        sourceTableId: '',
        consumerDatetime: ''
      },

      connector: '',

      rules: {
        sourceTableId: [
          { required: true, message: '请选择数据源', trigger: 'change' }
        ],
        consumerDatetime: [
          { required: true, message: '请选择消费起始时间', trigger: 'blur' },
          { required: true, message: '请选择消费起始时间', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'SourceTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.$refs['ruleFormDragSource'].resetFields()
        const tempExtend = JSON.parse(this.extend)
        if (tempExtend.connectConfig !== undefined) {
          this.connector = tempExtend.connectConfig.connector
          this.ruleForm.sourceTableId = tempExtend.connectConfig.tableId
          this.ruleForm.consumerDatetime = tempExtend.connectConfig['scan.startup.timestamp-millis']

          this.showTableName(this.ruleForm.sourceTableId)
        } else {
          this.connector = ''
          this.ruleForm.sourceTableId = ''
          this.ruleForm.consumerDatetime = ''
        }
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragSource'].validate((valid) => {
          this.fieldsCheckShow = true
          this.fieldsCheck = ''

          const transformation = { 'connectConfig': { 'connector': '', 'tableId': '', 'scan.startup.mode': '', 'scan.startup.timestamp-millis': '' }}
          transformation.connectConfig.connector = this.connector
          transformation.connectConfig.tableId = this.ruleForm.sourceTableId
          transformation.connectConfig['scan.startup.mode'] = 'timestamp'
          transformation.connectConfig['scan.startup.timestamp-millis'] = this.ruleForm.consumerDatetime
          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragSource'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.fieldsCheckShow = true
        this.fieldsCheck = ''
        this.$refs['ruleFormDragSource'].resetFields()
      }
    },
    remoteMethod(query) {
      if (query !== '') {
        this.iLoading = true
        const pathParams = []
        pathParams.push({ 'paramName': 'tableName', 'paramValue': query })
        getTableInfoCommonSql(pathParams).then(res => {
          if (res.code === '200') {
            this.list = res.data.map(item => {
              return { value: item.tableId, label: item.tableName + '(所属实例:' + item.dataSourceName + ')' }
            })
            this.iLoading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      } else {
        this.options = []
      }
    },
    generateFields() {
      if (this.fieldsCheckShow === true) {
        if (this.ruleForm.sourceTableId !== '') {
          getTableFields(this.ruleForm.sourceTableId).then(res => {
            if (res.code === '200') {
              const fieldsArr = []
              res.data.forEach(item => {
                fieldsArr.push(item.fieldName + '    ' + item.dataType.toLowerCase())
              })
              this.fieldsCheck = fieldsArr.join(',\n')
            }
          })
          this.fieldsCheckShow = !this.fieldsCheckShow
        } else {
          this.$message({
            type: 'info',
            message: '先选择数据源'
          })
        }
      } else {
        this.fieldsCheck = ''
        this.fieldsCheckShow = !this.fieldsCheckShow
      }
    },
    selectChange() {
      if (this.ruleForm.sourceTableId !== '') {
        getDataSourceTypeByTableId(this.ruleForm.sourceTableId).then(res => {
          if (res.code === '200') {
            this.connector = res.data
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }
    },
    showTableName(tableId) {
      if (tableId !== '') {
        const tableIdList = []
        tableIdList.push(tableId)
        get_table_detail_Info_by_ids(tableIdList).then(res => {
          if (res.code === '200') {
            this.options = res.data.map(item => {
              return { value: item.tableId, label: item.tableName + '(所属实例:' + item.dataSourceName + ')' }
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>

