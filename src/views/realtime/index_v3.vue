<template>
  <div class="app-container">
    <el-drawer
      :visible.sync="dragShow"
      direction="ttb"
      size="98%"
      style="height: auto"
      :with-header="false"
      :wrapper-closable="false"
    >
      <drag_algo :drag-show="dragShow" :full-job-info="fullJobInfo" @close="closeDrawShow" />
    </el-drawer>

    <div v-show="!dragShow">
      <el-row :gutter="10" style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px" class="element-header-tool">
        <el-col :span="2.1">
          <div>
            <el-button size="small" type="primary" icon="el-icon-edit" @click="createJob">新建作业</el-button>
          </div>
        </el-col>
        <el-col :span="5">
          <div>
            <el-input
              v-model="jobNameBlurry"
              placeholder="作业名"
              clearable
              @keyup.enter.native="doSearchNoRealPageInfo"
            />
          </div>
        </el-col>
        <el-col :span="4">
          <div>
            <el-input
              v-model="ownerNameBlurry"
              placeholder="创建人"
              clearable
              @keyup.enter.native="doSearchNoRealPageInfo"
            />
          </div>
        </el-col>
        <el-col :span="1.1">
          <div>
            <el-button size="small" type="success" icon="el-icon-search" @click="doSearchNoRealPageInfo">搜索</el-button>
          </div>
        </el-col>
      </el-row>

      <el-row style="margin-top: 20px">
        <el-col>
          <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            highlight-current-row
            :header-cell-style="{textAlign: 'left'}"
            size="medium"
            fit
            style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
          >
            <el-table-column
              prop="jobName"
              label="作业名称"
              fixed
              show-overflow-tooltip
              width="450px"
              sortable
              align="left"
            >
              <template slot-scope="scope">
                <el-popover trigger="hover" placement="top-end" title="作业描述">
                  <p style="font: 14px Base"> {{ scope.row.jobDesc }} </p>
                  <div slot="reference" class="name-wrapper">
                    <div style="font: 16px Medium">{{ scope.row.jobName }}</div>
                  </div>
                </el-popover>
              </template>
            </el-table-column>

            <el-table-column
              prop="ownerName"
              label="创建人"
              sortable
              align="left"
              width="120"
            />
            <el-table-column
              prop="createTime"
              label="创建时间"
              sortable
              align="left"
              width="200"
            />
            <el-table-column
              prop="departmentLabel"
              label="部门信息"
              sortable
              align="left"
              width="120"
            />
            <el-table-column
              prop="lastModifyUser"
              label="最近修改人"
              sortable
              align="left"
              width="120"
            />
            <el-table-column
              prop="updateTime"
              label="最近修改时间"
              sortable
              align="left"
              width="200"
            />
            <el-table-column
              prop="status"
              label="运行状态"
              sortable
              align="left"
              width="120"
            >
              <template slot-scope="scope">
                <el-tag type="info" :style="setStatusFontColor(scope.row.status)" size="medium">{{ getJobStatusDesc(scope.row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="相关操作"
              width="800"
            >
              <template slot-scope="scope">
<!--                <el-button-->
<!--                  size="mini"-->
<!--                  plain-->
<!--                  type="primary"-->
<!--                  class="el-icon-link"-->
<!--                  @click="redirectUrlMetrics(scope.row)"-->
<!--                >指标监控</el-button>-->
                <el-button
                  size="mini"
                  plain
                  class="el-icon-edit-outline"
                  @click="updateJob(scope.row)"
                >编辑</el-button>
                <el-button
                  size="mini"
                  plain
                  type="primary"
                  class="el-icon-link"
                  :disabled="scope.row.status !== 3"
                  @click="redirectUrl(scope.row)"
                >WebUi</el-button>
                <el-button
                  size="mini"
                  type="info"
                  plain
                  :loading="isValidateButtonLoading===true && validateButtonLoadingJobId===scope.row.jobId"
                  class="el-icon-view"
                  @click="validateJob(scope.row)"
                >校验作业</el-button>
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  :disabled="scope.row.status !== 0 && scope.row.status !== 6 && scope.row.status !== 4 && scope.row.status !== 12 && scope.row.status !== 14 && scope.row.status !== 19 && scope.row.status !== 21"
                  class="el-icon-top"
                  :loading="onlineButtonLoading && onlineJobId === scope.row.jobId"
                  @click="handleOnlinePre(scope.row)"
                >上线</el-button>
                <el-button
                  size="mini"
                  type="info"
                  plain
                  class="el-icon-document-copy"
                  @click="handleCopy(scope.$index, scope.row)"
                >复制作业</el-button>
                <el-button
                  size="mini"
                  type="success"
                  plain
                  :disabled="scope.row.status !== 3 && scope.row.status !== 16"
                  class="el-icon-bottom"
                  :loading="offlineButtonLoading && offlineJobId === scope.row.jobId"
                  @click="handleOffline(scope.$index, scope.row)"
                >下线</el-button>
                <el-button
                  size="mini"
                  type="danger"
                  plain
                  :disabled="scope.row.status !== 0 && scope.row.status !== 6"
                  class="el-icon-delete"
                  @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>

      </el-row>
      <el-row style="margin-top: 20px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px">
        <el-col>
          <el-pagination
            background
            layout="total, prev, pager, next, sizes,jumper"
            :page-sizes="[10, 15, 30, 50]"
            class="page_tt"
            :total="page_info.total_num"
            :page-size="page_info.page_size"
            :current-page="page_info.current_page"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @prev-click="handleCurrentChange"
            @next-click="handleCurrentChange"
          />
        </el-col>
      </el-row>

      <!-- 复制作业弹框 -->
      <el-dialog title="复制作业" width="40%" style="text-align: left" :close-on-click-modal="false" :show-close="false" :visible.sync="copyJobDialogFormVisible">
        <el-form ref="copyJobForm" :model="copyJobForm" :rules="rules" style="margin: -15px 3px -25px -5px">
          <el-form-item label="新作业名称" prop="jobName" label-width="100px">
            <div style="display: flex;">
              <el-input v-model="copyJobForm.jobName" size="medium" />
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel('copyJobForm')">取 消</el-button>
          <el-button type="primary" :loading="copyJobLoading" @click="submitForm('copyJobForm')">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 作业组件 -->
      <OnlineJob style="position: absolute" :is-show="onlineJobFlag" :is-has-latest-checkpoint="isHasLatestCheckpoint" :online-job-name="onlineJobName" @close="handleOnlineRes"/>

    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import drag_algo from './drag_algo.vue'

import { getJobStatusDesc } from './js/common'
import {
  queryJobPageable,
  deleteJob,
  deployJob,
  stopJob,
  copyJob,
  getJobStatusNum,
  validateJob,
  get_job_webUrl,
  job_is_has_latest_checkpoint,
  deployJobWithLatestCheckpoint
} from '@/api/job'

import OnlineJob from '@/components/Online/index.vue'

export default {
  name: 'Realtime',
  components: {
    drag_algo,
    OnlineJob
  },
  props: {
  },
  data() {
    return {
      dragShow: false,
      isDrawerShow: false,
      isComponentsShow: false,
      componentType: '',
      tempComponentEditData: '',
      loading: false,
      tableHeight: window.innerHeight - 300,
      tableData: [],
      jobNameBlurry: '',
      ownerNameBlurry: '',
      page_info: {
        total_num: 0,
        current_page: 1,
        page_size: 10
      },
      fullJobInfo: { 'jobId': 0, 'jobName': '', 'jobLevel': '4', 'jobDesc': '', 'isMonitor': '1', 'webJson': '{"edges":[],"nodes":[]}', 'dagJson': { 'transformationWrappers': [], 'optionConfigs': '' }},
      copyJobForm: {
        jobName: ''
      },
      copyJobId: 0,
      rules: {
        jobName: [
          { required: true, message: '请输入作业名称', trigger: 'blur' },
          { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ]
      },
      copyJobDialogFormVisible: false,
      copyJobLoading: false,
      jobStatusBar: { 'starting': 0, 'running': 0, 'offline': 0, 'stopping': 0, 'init': 0, 'startFail': 0, 'badRunning': 0 },
      isValidateButtonLoading: false,
      validateButtonLoadingJobId: 0,
      sourceTableNameList: [],
      dimTableNameList: [],
      isHasDimTable: '0',

      onlineJobId: 0,
      offlineJobId: 0,

      onlineButtonLoading: false,
      offlineButtonLoading: false,

      onlineJobFlag: false,
      onlineJobName: '',
      isHasLatestCheckpoint: '0',

    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  watch: {
  },
  mounted() {
  },
  created() {
    this.doSearchNoRealPageInfo()
    // setInterval(this.doSearchInterval, 10000)
  },
  methods: {
    handleDelete(index, row) {
      this.$confirm('是否确认删除 ' + row.jobName + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteJob(row.jobId).then(res => {
          if (res.status === 200) {
            this.$message({
              type: 'success',
              message: '删除进入审批流程!'
            })
          }
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.doSearchNoRealPageInfo()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleOnlinePre(row) {
      this.onlineJobId = row.jobId
      console.log(this.onlineJobId)
      job_is_has_latest_checkpoint(row.jobId).then(res => {
        if (res.code === '200') {
          this.onlineJobFlag = true
          this.onlineJobName = row.jobName
          if (res.data === '0') {
            this.isHasLatestCheckpoint = '0'
          }
          if (res.data === '1') {
            this.isHasLatestCheckpoint = '1'
          }
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    handleOnlineRes(tag, startType) {
      this.onlineJobFlag = false
      if (tag === '1') {
        if (startType === '0') {
          this.handleOnlineOld(this.onlineJobId)
        }
        if (startType === '1') {
          this.handleOnlineNew(this.onlineJobId)
        }
      }
    },
    handleOnlineOld(jobId) {
      this.onlineButtonLoading = true
      deployJob(jobId).then(res => {
        if (res.status === 200) {
          this.onlineButtonLoading = false
          this.$message({
            type: 'success',
            message: '上线进入审批流程!'
          })
        }
        if (res.code === '200') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'success',
            message: '提交上线成功!'
          })
          this.doSearch()
        }
        if (res.code === '500') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    handleOnlineNew(jobId) {
      this.onlineButtonLoading = true
      deployJobWithLatestCheckpoint(jobId).then(res => {
        if (res.status === 200) {
          this.onlineButtonLoading = false
          this.$message({
            type: 'success',
            message: '上线进入审批流程!'
          })
        }
        if (res.code === '200') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'success',
            message: '提交上线成功!'
          })
          this.doSearch()
        }
        if (res.code === '500') {
          this.onlineButtonLoading = false
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    handleOffline(index, row) {
      this.$confirm('是否确认下线 ' + row.jobName + ' ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.offlineButtonLoading = true
        this.offlineJobId = row.jobId
        stopJob(row.jobId).then(res => {
          if (res.status === 200) {
            this.offlineButtonLoading = false
            this.$message({
              type: 'success',
              message: '下线进入审批流程!'
            })
          }
          if (res.code === '200') {
            this.offlineButtonLoading = false
            this.$message({
              type: 'success',
              message: '提交下线成功!'
            })
            this.doSearchNoRealPageInfo()
          }
          if (res.code === '500') {
            this.offlineButtonLoading = false
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消下线'
        })
      })
    },
    validateJob(row) {
      this.isValidateButtonLoading = true
      this.validateButtonLoadingJobId = row.jobId
      validateJob(row.jobId).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '校验通过 🎉'
          })
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: '校验失败 😭' + '<br>' + res.mes
          })
        }
        this.isValidateButtonLoading = false
      })
    },
    handleCopy(index, row) {
      this.copyJobId = row.jobId
      this.copyJobDialogFormVisible = true
    },
    getJobStatusDesc(statusCode) {
      return getJobStatusDesc(statusCode)
    },
    doSearch() {
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
      pathParams.push({ 'paramName': 'ownerName', 'paramValue': this.ownerNameBlurry })
      pathParams.push({ 'paramName': 'jobName', 'paramValue': this.jobNameBlurry })
      pathParams.push({ 'paramName': 'type', 'paramValue': 'freeDrag' })
      pathParams.push({ 'paramName': 'subType', 'paramValue': 'freeDrag_algo' })

      queryJobPageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })

      this.getJobStatusNum()
    },
    doSearchNoRealPageInfo() {
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': 0 })
      pathParams.push({ 'paramName': 'ownerName', 'paramValue': this.ownerNameBlurry })
      pathParams.push({ 'paramName': 'jobName', 'paramValue': this.jobNameBlurry })
      pathParams.push({ 'paramName': 'type', 'paramValue': 'freeDrag' })
      pathParams.push({ 'paramName': 'subType', 'paramValue': 'freeDrag_algo' })

      queryJobPageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })

      this.getJobStatusNum()
    },
    fillTableData(data) {
      const tempTableData = []
      data.forEach(item => {
        tempTableData.push({ 'jobId': item.id, 'jobName': item.jobName, 'jobLevel': item.jobLevel.toString(), 'status': item.status, 'jobDesc': item.jobDesc, 'libraOwners': item.libraOwners, 'libraParallel': item.libraParallel, 'libraAlertGroup': item.libraAlertGroup, 'libraProjectName': item.libraProjectName, 'departmentLabel': item.departmentLabel, 'createTime': item.createTime, 'updateTime': item.updateTime, 'ownerName': item.ownerName, 'lastModifyUser': item.lastModifyUser, 'webJson': item.webJson, 'dagJson': item.dagJson, 'image': item.image, 'isMonitor': item.isMonitor + '' })
      })
      this.tableData = tempTableData
    },
    handleSizeChange(val) {
      this.page_info.page_size = val
      this.doSearch()
    },
    handleCurrentChange(val) {
      this.page_info.current_page = val
      this.doSearch()
    },
    createJob() {
      this.fullJobInfo = { 'jobId': 0, 'jobName': '', 'jobDesc': '', 'libraOwners': '', 'libraAlertGroup': '', 'libraProjectName': '', 'libraParallel': '', 'isMonitor': '1', 'jobLevel': '0', 'webJson': '{"cells":[]}', 'dagJson': '{"transformationWrappers":[],"optionConfigs":""}' }
      this.dragShow = true
    },
    updateJob(row) {
      if (this.user.username !== row.lastModifyUser) {
        this.$confirm('是否确认解锁编辑?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.fullJobInfo.jobId = row.jobId
          this.fullJobInfo.jobName = row.jobName
          this.fullJobInfo.jobLevel = row.jobLevel
          this.fullJobInfo.jobDesc = row.jobDesc
          this.fullJobInfo.libraOwners = row.libraOwners
          this.fullJobInfo.libraAlertGroup = row.libraAlertGroup
          this.fullJobInfo.libraProjectName = row.libraProjectName
          this.fullJobInfo.libraParallel = row.libraParallel
          this.fullJobInfo.isMonitor = row.isMonitor
          this.fullJobInfo.webJson = row.webJson
          this.fullJobInfo.dagJson = row.dagJson

          this.dragShow = true
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消编辑'
          })
        })
      } else {
        this.fullJobInfo.jobId = row.jobId
        this.fullJobInfo.jobName = row.jobName
        this.fullJobInfo.jobLevel = row.jobLevel
        this.fullJobInfo.jobDesc = row.jobDesc
        this.fullJobInfo.libraOwners = row.libraOwners
        this.fullJobInfo.libraAlertGroup = row.libraAlertGroup
        this.fullJobInfo.libraProjectName = row.libraProjectName
        this.fullJobInfo.libraParallel = row.libraParallel
        this.fullJobInfo.isMonitor = row.isMonitor
        this.fullJobInfo.webJson = row.webJson
        this.fullJobInfo.dagJson = row.dagJson

        this.dragShow = true
      }
    },
    setStatusFontColor(jobStatus) {
      switch (jobStatus) {
        case 0:
          return 'color: #930dd3'
        case 1:
          return 'color: #12AAAD'
        case 3:
          return 'color: red'
        case 6:
          return 'color: #221b0a'
        default:
          return 'color: #bcbdae'
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.copyJobLoading = true
          copyJob(this.copyJobId, this.copyJobForm.jobName).then(res => {
            if (res.code === '200') {
              this.copyJobLoading = false
              this.copyJobDialogFormVisible = false
              this.$message({
                type: 'success',
                message: '复制作业成功~'
              })
              this.copyJobForm.jobName = ''
              this.ownerNameBlurry = ''
              this.doSearchNoRealPageInfo()
            }
            if (res.code === '500') {
              this.copyJobLoading = false
              this.copyJobDialogFormVisible = false
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        } else {
          return false
        }
      })
    },
    cancel(formName) {
      this.copyJobDialogFormVisible = false
      this.$refs[formName].resetFields()
    },
    getJobStatusNum() {
      getJobStatusNum().then(res => {
        if (res.code === '200') {
          this.jobStatusBar.starting = res.data['2'] === undefined ? 0 : res.data['2']
          this.jobStatusBar.running = res.data['3'] === undefined ? 0 : res.data['3']
          this.jobStatusBar.offline = res.data['6'] === undefined ? 0 : res.data['6']
          this.jobStatusBar.stopping = res.data['5'] === undefined ? 0 : res.data['5']
          this.jobStatusBar.init = res.data['0'] === undefined ? 0 : res.data['0']
          this.jobStatusBar.startFail = res.data['4'] === undefined ? 0 : res.data['4']
          this.jobStatusBar.badRunning = res.data['7'] === undefined ? 0 : res.data['7']
        }
        if (res.code === '500') {
          console.log(res.mes)
        }
      })
    },
    redirectUrl(row) {
      get_job_webUrl(row.jobId).then(res => {
        if (res.code === '200') {
          window.open(res.data)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    redirectUrlMetrics(row) {
      const jumpUrl = 'https://apm.shizhuang-inc.com/grafana?params=/d/xtZsRQsSz/shu-ju-cang-ku?orgId=1&refresh=5m&var-date=All&var-custom_interval=1m&var-source=REALTIME_FLINK_ALGO_MODEL_LOG_METRIC&var-kubernetes_image=' + row.image + '&var-task_name=' + row.jobName + '&var-tm_name=All&var-app_id=All'
      window.open(jumpUrl)
    },
    closeDrawShow() {
      this.dragShow = false
      this.doSearchNoRealPageInfo()
    }

  }
}
</script>

<style lang="scss" scoped>

.app-container{
  height: auto;
}

</style>
