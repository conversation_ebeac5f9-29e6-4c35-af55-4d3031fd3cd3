<template>
  <div class="app-container">
    <el-dialog
      title="UNION ALL操作配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px;margin-top: -20px">
        <el-form ref="ruleFormDragUnion" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item label="左流节点编号" prop="leftNumber">
            <el-input
              v-model="ruleForm.leftNumber"
              placeholder="请输入左流节点编号，在节点上右键可以复制"
              clearable
            />
          </el-form-item>
          <el-form-item label="左流字段" prop="leftSelectExpression">
            <el-input
              v-model="ruleForm.leftSelectExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> username,age,`userid` as `user_id`"
              clearable
            />
          </el-form-item>
          <el-form-item label="右流节点编号" prop="rightNumber">
            <el-input
              v-model="ruleForm.rightNumber"
              placeholder="请输入右流节点编号，在节点上右键可以复制"
              clearable
            />
          </el-form-item>
          <el-form-item label="右流字段" prop="rightSelectExpression">
            <el-input
              v-model="ruleForm.rightSelectExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> username,age"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DragUnion',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleForm: {
        leftSelectExpression: '',
        rightSelectExpression: '',
        leftNumber: '',
        rightNumber: ''
      },
      rules: {
        leftSelectExpression: [
          { required: true, message: '请输入左流字段', trigger: 'blur' }
        ],
        rightSelectExpression: [
          { required: true, message: '请输入右流字段', trigger: 'blur' }
        ],
        leftNumber: [
          { required: true, message: '请输入左流节点编号', trigger: 'blur' }
        ],
        rightNumber: [
          { required: true, message: '请输入右流节点编号', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'UnionTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        const tempExtend = JSON.parse(this.extend)
        this.ruleForm.leftNumber = tempExtend.leftNumber === undefined ? '' : tempExtend.leftNumber
        this.ruleForm.leftSelectExpression = tempExtend.leftSelectExpressionOrigin === undefined ? '' : tempExtend.leftSelectExpressionOrigin
        this.ruleForm.rightNumber = tempExtend.rightNumber === undefined ? '' : tempExtend.rightNumber
        this.ruleForm.rightSelectExpression = tempExtend.rightSelectExpressionOrigin === undefined ? '' : tempExtend.rightSelectExpressionOrigin
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragUnion'].validate((valid) => {
          const transformation = { 'leftSelectExpression': '', 'rightSelectExpression': '', 'leftNumber': '', 'rightNumber': '', 'leftSelectExpressionOrigin': '', 'rightSelectExpressionOrigin': '' }
          transformation.leftSelectExpression = this.ruleForm.leftSelectExpression.trim().replace(/\n/g, '')
          transformation.rightSelectExpression = this.ruleForm.rightSelectExpression.trim().replace(/\n/g, '')
          transformation.leftSelectExpressionOrigin = this.ruleForm.leftSelectExpression.trim()
          transformation.rightSelectExpressionOrigin = this.ruleForm.rightSelectExpression.trim()
          transformation.leftNumber = this.ruleForm.leftNumber.trim()
          transformation.rightNumber = this.ruleForm.rightNumber.trim()
          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragUnion'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.$refs['ruleFormDragUnion'].resetFields()
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

