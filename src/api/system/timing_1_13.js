import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/jobs_1_13',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/jobs_1_13',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/jobs_1_13',
    method: 'put',
    data
  })
}

export function updateIsPause(id) {
  return request({
    url: 'api/jobs_1_13/' + id,
    method: 'put'
  })
}

export function execution(id) {
  return request({
    url: 'api/jobs_1_13/exec/' + id,
    method: 'put'
  })
}

export default { del, updateIsPause, execution, add, edit }
