<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="规则名" prop="ruleName">
            <el-input v-model="form.ruleName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="表达式" prop="express">
            <el-input v-model="form.express" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新日期" prop="updateTime">
            <el-input v-model="form.updateTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建日期" prop="createTime">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="版本" prop="version">
            <el-input v-model="form.version" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="ruleName" label="规则名" />
        <el-table-column prop="express" label="表达式" />
        <el-table-column prop="updateTime" label="更新日期" />
        <el-table-column prop="createTime" label="创建日期" />
        <el-table-column prop="version" label="版本" />
        <el-table-column v-if="checkPer(['admin','ruleCompare:edit','ruleCompare:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudRuleCompare from '@/api/ruleCompare'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, ruleName: null, express: null, updateTime: null, createTime: null, version: null }
export default {
  name: 'RuleCompare',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '阈值比较', url: 'api/ruleCompare', idField: 'id', sort: 'id,desc', crudMethod: { ...crudRuleCompare }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'ruleCompare:add'],
        edit: ['admin', 'ruleCompare:edit'],
        del: ['admin', 'ruleCompare:del']
      },
      rules: {
        ruleName: [
          { required: true, message: '规则名不能为空', trigger: 'blur' }
        ],
        express: [
          { required: true, message: '表达式不能为空', trigger: 'blur' }
        ],
        updateTime: [
          { required: true, message: '更新日期不能为空', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '创建日期不能为空', trigger: 'blur' }
        ],
        version: [
          { required: true, message: '版本不能为空', trigger: 'blur' }
        ]
      }}
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
