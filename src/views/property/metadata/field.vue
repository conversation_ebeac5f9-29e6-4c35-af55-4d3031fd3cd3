<template>
  <div class="app-container">
    <el-dialog
      title="表字段管理"
      :visible.sync="tag"
      width="50%"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :before-close="handleClose"
      top="10vh"
    >

      <button v-show="false" ref="reloadButton" @click="crud.toQuery">button</button>

      <!--工具栏-->
      <div class="head-container">
        <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
        <crudOperation :permission="permission" />
        <!--表单组件-->
        <el-dialog
          :close-on-click-modal="false"
          :before-close="crud.cancelCU"
          :visible.sync="crud.status.cu > 0"
          :title="crud.status.title"
          :modal-append-to-body="true"
          :append-to-body="true"
          width="500px"
        >
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            size="small"
            label-width="80px"
          >
            <el-form-item
              label="字段名"
              prop="fieldName"
            >
              <el-input
                v-model="form.fieldName"
                style="width: 370px;"
              />
            </el-form-item>

            <el-form-item
              label="字段类型"
              prop="dataType"
            >
              <el-select
                v-model="form.dataType"
                filterable
                placeholder="请选择"
                style="width: 370px;"
              >
                <el-option
                  v-for="item in dict.字段类型"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="字段描述"
              prop="fieldDesc"
            >
              <el-input
                v-model="form.fieldDesc"
                style="width: 370px;"
              />
            </el-form-item>

            <el-form-item
              v-show="false"
              label="所属表id"
              prop="reDataSourceMetadataId"
            >
              <el-input
                v-show="false"
                v-model="form.reDataSourceMetadataId"
                style="width: 370px;"
              />
            </el-form-item>
          </el-form>
          <div
            slot="footer"
            class="dialog-footer"
          >
            <el-button
              type="text"
              @click="crud.cancelCU"
            >
              取消
            </el-button>
            <el-button
              :loading="crud.status.cu === 2"
              type="primary"
              @click="crud.submitCU"
            >
              确认
            </el-button>
          </div>
        </el-dialog>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :data="crud.data"
          size="small"
          style="width: 100%;"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column
            prop="id"
            label="自增id"
          />
          <el-table-column
            prop="fieldName"
            label="字段名"
          />
          <el-table-column
            prop="dataType"
            label="字段类型"
          />
          <el-table-column
            prop="fieldDesc"
            label="字段描述"
          />
          <el-table-column
            prop="reDataSourceMetadataId"
            label="所属表id"
          />
          <el-table-column
            prop="tableName"
            label="所属表名"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
          />
          <el-table-column
            v-if="checkPer(['admin','reDataSourceTableField:edit','reDataSourceTableField:del'])"
            label="操作"
            width="150px"
            align="center"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import crudReDataSourceTableField from '@/api/reDataSourceTableField'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, createTime: null, modifyTime: null, fieldName: null, createBy: null, dept: null, reDataSourceMetadataId: null }

export default {
  name: 'ReDataSourceTableField',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['字段类型', '全量表'],
  cruds() {
    return CRUD({ title: '表字段信息', url: 'api/reDataSourceTableField', idField: 'id', sort: 'id,desc', crudMethod: { ...crudReDataSourceTableField }})
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    editTableId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      permission: {
        add: ['admin', 'reDataSourceTableField:add'],
        edit: ['admin', 'reDataSourceTableField:edit'],
        del: ['admin', 'reDataSourceTableField:del']
      },
      rules: {
        fieldName: [
          { required: true, message: '字段名不能为空', trigger: 'blur' }
        ],
        reDataSourceMetadataId: [
          { required: true, message: '所属表id不能为空', trigger: 'blur' }
        ],
        dataType: [
          { required: true, message: '所属表id不能为空', trigger: 'blur' }
        ]
      }}
  },
  computed: {
    tag() {
      return this.isShow
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        defaultForm.reDataSourceMetadataId = this.editTableId
        this.$nextTick(() => {
          this.$refs.reloadButton.dispatchEvent(new MouseEvent('click'))
        })
      }
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    handleClose(done) {
      sessionStorage.setItem('editField', '0')
      this.$emit('close')
    }
  }
}
</script>

<style scoped>

</style>
