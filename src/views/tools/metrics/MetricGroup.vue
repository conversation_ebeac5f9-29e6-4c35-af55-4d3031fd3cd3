<template>
  <div class="app-container">
    <el-row :gutter="5" style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px" class="element-header-tool">
      <el-col :span="3">
        <div>
          <el-button size="small" type="primary" icon="el-icon-edit" @click="addMetricGroup">新增指标组</el-button>
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-input
            v-model="metricGroupNameBlurry"
            placeholder="名称"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-input
            v-model="metricGroupDescBlurry"
            placeholder="描述"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-input
            v-model="createNameBlurry"
            placeholder="创建人"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-select v-model="businessBlurry" clearable placeholder="业务域">
            <el-option label="交易" value="1" />
            <el-option label="社区" value="2" />
            <el-option label="增长" value="3" />
            <el-option label="搜推" value="4" />
            <el-option label="供应链" value="5" />
            <el-option label="用户" value="6" />
            <el-option label="公共" value="0" />
          </el-select>
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-button size="small" type="success" icon="el-icon-search" @click="doSearch">搜索</el-button>
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-button size="small" type="success" plain icon="el-icon-s-opportunity" @click="openMetricFormat">指标统一格式介绍</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col>
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          highlight-current-row
          :header-cell-style="{textAlign: 'left'}"
          size="medium"
          style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
        >
          <el-table-column
            prop="metricGroupName"
            label="名称"
            fixed
            sortable
            align="left"
            width="500"
          />
          <el-table-column
            label="业务域"
            align="left"
            width="160"
          >
            <template slot-scope="scope">
              <el-tag size="medium">{{ getBusinessName(scope.row.teBusinessId) }} / {{ getBusinessName(scope.row.teBusinessSubjectId) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="metricGroupDesc"
            label="描述"
            sortable
            align="left"
            width="500"
          />
          <el-table-column
            label="维度"
            align="left"
            width="400"
          >
            <template slot-scope="scope">
              <el-tag v-for="tag in scope.row.teCommonAttributesIds.split(',')" :key="tag" size="medium"> {{ tag }} </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="reDataSourceMetadataName"
            label="存储topic"
            sortable
            align="left"
            width="500"
          />
          <el-table-column
            prop="job"
            label="作业名"
            sortable
            align="left"
            width="500"
          />
          <el-table-column
            prop="createName"
            label="创建人"
            sortable
            align="left"
            width="140"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            prop="modifyName"
            label="操作人"
            sortable
            align="left"
            width="140"
          />
          <el-table-column
            prop="modifyTime"
            label="更新时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            label="相关操作"
            width="720"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                plain
                class="el-icon-edit-outline"
                @click="updateMetricGroup(scope.row)"
              >修改</el-button>
              <el-button
                size="mini"
                plain
                type="success"
                class="el-icon-view"
                @click="openMetricDemo(scope.row.dataDemo)"
              >样例查看</el-button>
              <el-button
                size="mini"
                plain
                type="success"
                class="el-icon-view"
                @click="openMetricExt(scope.row.ext)"
              >扩展字段查看</el-button>
              <el-button
                size="mini"
                plain
                type="primary"
                class="el-icon-plus"
                @click="addMetricDetail(scope.row.id)"
              >添加指标明细</el-button>
              <el-button
                size="mini"
                plain
                type="success"
                class="el-icon-s-unfold"
                @click="openMetricDetailDialog(scope.row.id)"
              >查看指标明细</el-button>
              <el-button
                size="mini"
                type="danger"
                plain
                :disabled="scope.row.isHaveMetricDetail===1"
                class="el-icon-delete"
                @click="deleteMetricGroup(scope.row.id,scope.row.metricGroupName,scope.row.teCommonAttributesIds)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px">
      <el-col>
        <el-pagination
          background
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[10, 15, 30, 50]"
          class="page_tt"
          :total="page_info.total_num"
          :page-size="page_info.page_size"
          :current-page="page_info.current_page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
        />
      </el-col>
    </el-row>

    <metric-format :is-show="isMetricFormatShow" style="position: absolute" @close="closeMetricFormat" />

    <metric-group-edit :is-show="isMetricGroupEditShow" :edit-json="fullMetricGroupJson" style="position: absolute" @close="closeMetricGroupEdit" />

    <metric-demo :is-show="isMetricDemoShow" :metric-content="metricDemo" style="position: absolute" @close="closeMetricDemo" />

    <metric-detail-edit :is-show="isMetricDetailEditShow" :edit-json="fullMetricDetailJson" style="position: absolute" @close="closeMetricDetailEdit" />

    <metric-detail-dialog :is-show="isMetricDetailDialogShow" :metric-group-id="metricGroupId" @close="closeMetricDetailDialog"></metric-detail-dialog>
  </div>
</template>
<script>
import { queryMetricGroupPageable, updateMetricGroup } from '@/api/metrics'
import { checkPermission } from './js/tools'
import MetricFormat from './components/MetricFormat'
import MetricDemo from './components/MetricDemo'
import MetricGroupEdit from './components/MetricGroupEdit'
import MetricDetailEdit from './components/MetricDetailEdit'
import MetricDetailDialog from './components/MetricDetailDialog'

export default {
  name: 'MetricGroup',
  components: {
    MetricFormat,
    MetricGroupEdit,
    MetricDemo,
    MetricDetailEdit,
    MetricDetailDialog
  },
  data() {
    return {
      copyConfig: { copyText: '复制', copiedText: '复制成功' },
      metricGroupNameBlurry: '',
      metricGroupDescBlurry: '',
      businessBlurry: '',
      createNameBlurry: '',
      isMetricFormatShow: false,
      isMetricGroupEditShow: false,
      isMetricDemoShow: false,
      isMetricDetailEditShow: false,
      isMetricDetailDialogShow: false,
      metricDemo: '',
      metricGroupId: 0,
      tableData: [
        {
          'id': 10,
          'metricGroupName': 'order_metrics',
          'reDataSourceMetadataId': 2511,
          'reDataSourceMetadataName': 'soc_user_biz_valid_day',
          'metricGroupDesc': '订单指标',
          'teBusinessId': 1000,
          'teCommonAttributesIds': 'spu_id,sku_id',
          'isDel': 0,
          'createName': 'liminghui',
          'createTime': '2022-11-01 20:10:36',
          'modifyName': 'liuyang',
          'modifyTime': '2022-11-04 10:18:00',
          'isHaveMetricDetail': 1,
          'job': 'ads_liminghui_xxx',
          'ext': '{\n    "ext_01":"int类型 功能xxx",\n    "ext_02":"string类型 功能xxx"\n}'
        },
        {
          'id': 11,
          'metricGroupName': 'daily_first_active',
          'reDataSourceMetadataId': 2512,
          'reDataSourceMetadataName': 'MM_NEWCATEGORY',
          'metricGroupDesc': '用户日首次活跃',
          'teBusinessId': 3000,
          'teCommonAttributesIds': 'user_id',
          'isDel': 0,
          'createName': 'wanglin',
          'createTime': '2022-11-04 10:19:44',
          'modifyName': 'stephen',
          'modifyTime': '2022-11-04 10:20:45',
          'isHaveMetricDetail': 1,
          'job': 'stephen_test_ads',
          'dataDemo': '{\n    "dim_name":"user_id",\n    "ext":null,\n    "metric_name":"daily_first_active",\n    "unique_key":"d6b9bc0905887701879b7c7f67611955",\n    "track_id":"97a3efb0-f923-4229-8b2c-87c24b79b7f9",\n    "metric_value":{\n        "metric_date":"20221017",\n        "event_time":1665986599647\n    },\n    "job":"community_reward_for_the_author_of_change_interruption_step1",\n    "dim_value":{\n        "user_id":122183690\n    },\n    "ts":1665986600315\n}'
        }
      ],
      page_info: {
        total_num: 0,
        current_page: 1,
        page_size: 10
      },
      loading: false,
      fullMetricGroupJson: { 'id': 0, 'metricGroupName': '', 'metricGroupDesc': '', 'reDataSourceMetadataId': '', 'teBusinessId': '0', 'teCommonAttributesIds': '', 'isHaveMetricDetail': 0, 'dataDemo': '', 'ext': '', 'job': '' },
      fullMetricDetailJson: { 'id': 0, 'metricGroupId': 0, 'metricDetailName': '', 'metricDetailDesc': '', 'metricType': '', 'metricProductType': '', 'job': '' }
    }
  },
  computed: {
    buttonAdd() {
      return checkPermission('metrics:add')
    }
  },
  mounted() {
  },
  created() {
    this.doSearch()
  },
  methods: {
    doSearch() {
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
      pathParams.push({ 'paramName': 'metricGroupName', 'paramValue': this.metricGroupNameBlurry })
      pathParams.push({ 'paramName': 'metricGroupDesc', 'paramValue': this.metricGroupDescBlurry })
      pathParams.push({ 'paramName': 'teBusinessId', 'paramValue': this.businessBlurry })
      pathParams.push({ 'paramName': 'createName', 'paramValue': this.createNameBlurry })
      pathParams.push({ 'paramName': 'isDel', 'paramValue': 0 })

      queryMetricGroupPageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })
    },
    fillTableData(data) {
      const tempTableData = []
      data.forEach(item => {
        tempTableData.push({ 'id': item.id, 'isDel': item.isDel, 'metricGroupName': item.metricGroupName, 'metricGroupDesc': item.metricGroupDesc, 'reDataSourceMetadataId': item.reDataSourceMetadataId, 'reDataSourceMetadataName': item.reDataSourceMetadataName, 'teBusinessId': item.teBusinessId, 'teBusinessSubjectId': item.teBusinessSubjectId, 'teCommonAttributesIds': item.teCommonAttributesIds, 'createTime': item.createTime, 'modifyName': item.modifyName, 'createName': item.createName, 'modifyTime': item.modifyTime, 'isHaveMetricDetail': item.isHaveMetricDetail, 'job': item.job, 'dataDemo': item.dataDemo, 'ext': item.ext })
      })
      this.tableData = tempTableData
    },
    handleSizeChange(val) {
      this.page_info.page_size = val
      this.doSearch()
    },
    handleCurrentChange(val) {
      this.page_info.current_page = val
      this.doSearch()
    },
    testPermission() {
      const a = checkPermission('metrics:add')
      console.log(a)
    },
    openMetricFormat() {
      this.isMetricFormatShow = true
    },
    closeMetricFormat() {
      this.isMetricFormatShow = false
    },
    addMetricGroup() {
      this.fullMetricGroupJson = { 'id': 0, 'metricGroupName': '', 'metricGroupDesc': '', 'reDataSourceMetadataId': '', 'teBusinessId': '0', 'teBusinessSubjectId': '0','teCommonAttributesIds': '', 'isHaveMetricDetail': 0, 'dataDemo': '', 'ext': '', 'job': '' }
      this.isMetricGroupEditShow = true
    },
    updateMetricGroup(row) {
      this.fullMetricGroupJson.id = row.id
      this.fullMetricGroupJson.metricGroupName = row.metricGroupName
      this.fullMetricGroupJson.metricGroupDesc = row.metricGroupDesc
      this.fullMetricGroupJson.teBusinessId = row.teBusinessId + ''
      this.fullMetricGroupJson.teBusinessSubjectId = row.teBusinessSubjectId + ''
      this.fullMetricGroupJson.reDataSourceMetadataId = row.reDataSourceMetadataId
      this.fullMetricGroupJson.teCommonAttributesIds = row.teCommonAttributesIds
      this.fullMetricGroupJson.isHaveMetricDetail = row.isHaveMetricDetail
      this.fullMetricGroupJson.dataDemo = row.dataDemo
      this.fullMetricGroupJson.ext = row.ext
      this.fullMetricGroupJson.job = row.job
      this.isMetricGroupEditShow = true
    },
    closeMetricGroupEdit(tag) {
      this.isMetricGroupEditShow = false
      if (tag === '1') {
        this.doSearch()
      }
    },
    openMetricDemo(metricDemo) {
      if (metricDemo === '' || metricDemo === undefined) {
        this.$message('指标组样例没有录入，请录入后再查看！')
        return
      }
      this.metricDemo = metricDemo
      this.isMetricDemoShow = true
    },
    openMetricExt(ext) {
      if (ext === '' || ext === undefined) {
        this.$message('指标组扩展字段没有录入，请录入后再查看！')
        return
      }
      this.metricDemo = ext
      this.isMetricDemoShow = true
    },
    closeMetricDemo() {
      this.isMetricDemoShow = false
    },
    addMetricDetail(metricGroupId) {
      this.fullMetricDetailJson = { 'id': 0, 'metricGroupId': metricGroupId, 'metricDetailName': '', 'metricDetailDesc': '', 'metricType': '', 'metricProductType': '', 'job': '' }
      this.isMetricDetailEditShow = true
    },
    closeMetricDetailEdit(tag) {
      this.isMetricDetailEditShow = false
      if (tag === '1') {
        this.doSearch()
      }
    },
    openMetricDetailDialog(metricGroupId) {
      this.metricGroupId = metricGroupId
      this.isMetricDetailDialogShow = true
    },
    closeMetricDetailDialog() {
      this.isMetricDetailDialogShow = false
    },
    getBusinessName(businessId) {
      switch (businessId) {
        case 0:
          return '默认'
        case 200000:
          return '默认'
        case 200001:
          return '订单'
        case 3645824:
          return '埋点'
        case 1:
          return '交易'
        case 210000:
          return '默认'
        case 210001:
          return '订单'
        case 210002:
          return '商品'
        case 210003:
          return '用户'
        case 210006:
          return '流量'
        case 8420423:
          return '雷达'
        case 2111396:
          return '营销活动'
        case 8734757:
          return '直播'
        case 2994285:
          return '出价&库存'
        case 2806718:
          return '出价'
        case 3556023:
          return '寄存'
        case 8792358:
          return '客服'
        case 8661900:
          return 'BC端'
        case 1572381:
          return '商家'
        case 2:
          return '社区'
        case 220000:
          return '默认'
        case 220006:
          return '流量'
        case 3:
          return '增长'
        case 230000:
          return '默认'
        case 230003:
          return '用户'
        case 1522069:
          return '国际'
        case 4:
          return '搜推'
        case 240000:
          return '默认'
        case 240006:
          return '流量'
        case 5:
          return '供应链'
        case 250000:
          return '默认'
        case 7740637:
          return '仓储'
        case 3140159:
          return '履约'
        case 5859956:
          return '运配'
        case 1035172:
          return '劳务'
        case 3408191:
          return '生产'
        case 3494518:
          return '大数据'
        case 7273203:
          return '默认'
        case 1972159:
          return '智能运营系统'
        case 5434147:
          return '投放'
        case 6374312:
          return '默认'
        case 9408224:
          return '硬广'
        case 3170406:
          return '软广'
        case 4079922:
          return '商投'
        case 1777122:
          return '商家'
        case 1757291:
          return '默认'
        case 5324177:
          return '透传'
        case 5799815:
          return 'ERP'
        case 6:
          return '用户'
        case 7000340:
          return '默认'
        default:
          return 'NONE'
      }
    },
    deleteMetricGroup(metricGroupId, metricGroupName, teCommonAttributesIds) {
      const metricGroup = { 'id': 0, 'isDel': 0, 'teCommonAttributesIds': '', 'metricGroupName': '' }
      metricGroup.id = metricGroupId
      metricGroup.isDel = 1
      metricGroup.teCommonAttributesIds = teCommonAttributesIds
      metricGroup.metricGroupName = metricGroupName
      this.$confirm('是否确认删除指标组: ' + metricGroupName + ' ？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateMetricGroup(metricGroup).then(res => {
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.doSearch()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .element-job-status-bar{
    position: absolute;
    top: 16px;
    right: 10px;
  }
  .element-job-status-bar .status-key{
    color: #dd0689
  }
  .element-job-status-bar .status-value{
    color: blueviolet
  }
  .el-tag + .el-tag {
    margin-left: 10px;
  }
</style>
