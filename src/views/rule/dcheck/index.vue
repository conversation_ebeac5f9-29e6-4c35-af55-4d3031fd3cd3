<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">作业名</label>
        <el-input v-model="query.jobName" clearable placeholder="作业名" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">作业状态</label>
        <el-button id="loadButton" @click="crud.toQuery" v-show="false">test</el-button>
        <el-select
          v-model="query.status"
          filterable
          placeholder="请选择"
          clearable
          style="width: 185px;"
          @blur="selectBlur"
          @clear="selectClear"
          @change="selectChange"
        >
          <el-option
            v-for="item in dict.新作业状态"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>
        <label class="el-form-item-label">创建人</label>
        <el-input v-model="query.createUserName" clearable placeholder="创建人" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <rrOperation :crud="crud"/>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission"/>
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0"
                 :title="crud.status.title" width="999px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="作业名" prop="jobName">
            <el-input v-model="form.jobName"  :disabled="form.id" placeholder="" style="width: 800px;"/>
          </el-form-item>
          <el-form-item label="作业描述" prop="jobName">
            <el-input v-model="form.jobDesc"   placeholder="" style="width: 800px;"/>
          </el-form-item>
<!--          <el-form-item label="输入表" prop="sourceTableIds">-->
<!--            <el-select v-model="form.sourceTableIds" filterable multiple placeholder="请选择(多表关联sql,可选多张表)" style="width: 800px;">-->
<!--              <el-option-->
<!--                v-for="item in dict.新全量表"-->
<!--                :key="item.id"-->
<!--                :label="item.label"-->
<!--                :value="item.value"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item label="输入表" prop="sourceTableIds">
            <div class="tableInfo_div">
              <el-select
                v-model="form.sourceTableIds"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="请选择(多表关联sql,可选多张表)"
                style="width: 800px;"
                :remote-method="remoteMethod"
                :loading="iloading">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
<!--              <el-button type="info" plain @click="showTableName(form.sourceTableIds)">显示输入表表名</el-button>-->
            </div>
          </el-form-item>
          <el-form-item label="sql逻辑" prop="sql">
            <el-input v-model="form.sql" :rows="20" type="textarea" style="width: 800px;"/>
          </el-form-item>
<!--          <el-form-item label="输出表" prop="sinkTableIds">-->
<!--            <el-select v-model="form.sinkTableIds" filterable multiple placeholder="请选择" style="width: 800px;">-->
<!--              <el-option-->
<!--                v-for="item in dict.新全量表"-->
<!--                :key="item.id"-->
<!--                :label="item.label"-->
<!--                :value="item.value"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item label="输出表" prop="sinkTableIds">
            <div class="tableInfo_div">
              <el-select
                v-model="form.sinkTableIds"
                multiple
                filterable
                remote
                reserve-keyword
                style="width: 800px;"
                :remote-method="remoteMethod"
                :loading="iloading">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
<!--              <el-button type="info" plain @click="showTableName(form.sinkTableIds)">显示输出表表名</el-button>-->
            </div>

          </el-form-item>

          <el-form-item label="缓存时间" prop="cacheTTL">
            <el-select v-model="form.cacheTTL" filterable placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.统计周期"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="输出表主键字段" >
            <el-input v-model="form.sinkSchemaPrimaryKey" style="width: 800px;"/>
          </el-form-item>

          <el-form-item label="启动时间" >
            <el-input v-model="form.relativeStartTs" placeholder="填day-x或hour-x或minute-1 (day-1表示从昨天0点启动，hour-1表示当前时间前一个小时启动，不填写则从当前时间启动)" style="width: 800px;"/>
          </el-form-item>

          <el-form-item label="作业参数" >
            <el-input v-model="form.optionConfigs"  :rows="3" type="textarea" placeholder="高级配置,一般不填" style="width: 800px;"/>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
          <el-button v-permission="['admin','reDataSourceMetadata:sync']" style="margin-left: -2px" type="success"
                     @click="syncs(form.sourceTableIds)">表字段同步
          </el-button>
        </div>
      </el-dialog>


      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;"
                @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55"/>
        <el-table-column prop="id" label="id"/>
        <el-table-column prop="jobName" label="作业名"/>
        <el-table-column prop="jobDesc" label="作业描述"/>
        <el-table-column prop="cacheTTL" label="缓存时间">
          <template slot-scope="scope">
            {{ dict.label.统计周期[scope.row.cacheTTL] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="作业状态">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间"/>
        <el-table-column prop="ownerName" label="创建人"/>

        <el-table-column v-if="checkPer(['admin','teJobInfo:edit','teJobInfo:del'])" label="操作" width="120px"
                         align="right">
          <template slot-scope="scope">
            <el-button v-permission="['admin','teJobInfo:collect']" type="primary" size="mini"
                       @click="collect(scope.row.sinkTableIds,scope.row.jobName)">输出表数据采集
            </el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','teJobInfo:edit','teJobInfo:del'])" label="" width="500px"
                         align="right">
          <template slot-scope="scope">
            <el-button v-permission="['admin','teJobInfo:check']" type="success" size="mini"
                       @click="toDqc(scope.row.id,scope.row.jobName)">数据演练
            </el-button>
            <el-button v-permission="['admin','teJobInfo:check']" type="primary" size="mini"
                       @click="check(scope.row.id)">校验
            </el-button>
            <el-button  type="success" size="mini"
                        @click="handleCopy(scope.row.id)">复制任务
            </el-button>
            <el-button v-permission="['admin','teJobInfo:deploy']" type="primary" size="mini"
                       @click="deploy(scope.row.id)">发布
            </el-button>
            <el-button v-permission="['admin','teJobInfo:stop']" type="success" size="mini"
                       @click="stop(scope.row.id)">下线
            </el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','teJobInfo:edit','teJobInfo:del'])" label="" width="120px"
                         align="right">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination/>
    </div>
    <Dqc :is-show="isDqcShow" :job-id="dqcJobId" :job-name="dqcJobName" :source-table-name-list="sourceTableNameList" :dim-table-name-list="dimTableNameList" :is-has-dim-table="isHasDimTable" @close="closeDqcDialog" ></Dqc>
    <!-- 复制作业弹框 -->
    <el-dialog title="复制作业" width="40%" style="text-align: left" :close-on-click-modal="false" :show-close="false" :visible.sync="copyJobDialogFormVisible">
      <el-form ref="copyJobForm" :model="copyJobForm" :rules="rules" style="margin: -15px 3px -25px -5px">
        <el-form-item label="新作业名称" prop="jobName" label-width="100px">
          <el-input v-model="copyJobForm.jobName" autocomplete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel('copyJobForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('copyJobForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import crudnewtopn from '@/api/newtopn'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import crudReDataSourceMetadata from '@/api/reDataSourceMetadata'
import { get_job_info_by_dqc, getTableInfoCommonSql, get_table_detail_Info_by_ids, copyJob } from '@/api/job'
import Dqc from '../../realtime/components/Dqc'

const defaultForm = {
  id: null,
  jobName: null,
  moduleName: 'b2s_template',
  sinkTableIds: null,
  sourceTableIds: null,
  sinkSchemaPrimaryKey: null,
  sql: null,
  cacheTTL: null,
  relativeStartTs:null,
  optionConfigs:null,
  jobDesc:null
}
export default {
  name: 'newsql',
  components: {pagination, crudOperation, rrOperation, udOperation, Dqc},
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['关联类型', '新全量表', '统计周期', '二期作业状态','新作业状态'],
  cruds() {
    return CRUD({
      title: 'sql作业模板',
      url: 'api/teJobInfo/dcheck',
      idField: 'id',
      sort: 'id,desc',
      templateType: 'b2s_template',
      crudMethod: {...crudnewtopn}
    })
  },
  data() {
    return {
      permission: {
        add: ['admin', 'teJobInfo:add'],
        edit: ['admin', 'teJobInfo:edit'],
        del: ['admin', 'teJobInfo:del']
      },
      rules: {
        jobName: [
          {required: true, message: '作业名不能为空', trigger: 'blur'}
        ],
        sinkTableIds: [
          {required: true, message: '输出表不能为空', trigger: 'blur'}
        ],
        sourceTableIds: [
          {required: true, message: '输入表不能为空', trigger: 'blur'}
        ],
        sql: [
          {required: true, message: 'sql逻辑不能为空', trigger: 'blur'}
        ],
        cacheTTL: [
          {required: true, message: '数据缓存时间不能为空', trigger: 'blur'}
        ],
        sinkSchemaPrimaryKey: [
          {required: true, message: '输出表主键字段不能为空', trigger: 'blur'}
        ]
      },
      queryTypeOptions: [
        {key: 'jobName', display_name: '作业名称'}
      ],
      isDqcShow: false,
      dqcJobId: 0,
      dqcJobName: '',
      sourceTableNameList: [],
      dimTableNameList: [],
      isHasDimTable: '0',


      options: [],
      value: [],
      list: [],
      iloading: false,

      copyJobId: 0,
      copyJobDialogFormVisible: false,
      copyJobForm: {
        jobName: ''
      },
    }
  },
  methods: {
    syncs(ids) {
      crudReDataSourceMetadata.syncs(ids).then(res => {
        console.log('-------------------------------')
        console.log(ids)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    // 执行
    check(id) {
      crudnewtopn.check(id).then(res => {
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    collect(id,jobName) {
      crudReDataSourceMetadata.collect(id,jobName).then(res => {
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    deploy(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.deploy(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    del(data) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.del(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    stop(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.stop(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    toDqc(id,jobName){
      get_job_info_by_dqc(id).then(res=>{
        if (res.code === '200') {
          this.sourceTableNameList = res.data.sourceTableList
          this.dimTableNameList = res.data.dimTableList
          this.isHasDimTable = res.data.dimTableList.length === 0 ? '0' : '1'
          this.isDqcShow = true
          this.dqcJobId = id
          this.dqcJobName = jobName
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    closeDqcDialog(tag){
      this.isDqcShow = false
    },
    remoteMethod(query) {
      if (query !== '') {
        this.iloading = true;
        const pathParams = []
        pathParams.push({ 'paramName': 'tableName', 'paramValue': query })
        getTableInfoCommonSql(pathParams).then(res=>{
          if(res.code === '200'){
            this.list = res.data.map(item => {
              return { value: item.tableId, label: item.tableName+'(表类型:'+item.dataSourceTypeName+',所属数据源:'+item.dataSourceName+',所属库:'+item.dbName+')' };
            });
            this.iloading = false;
            this.options = this.list.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1;
            });
            console.log(this.options)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      } else {
        this.options = [];
      }
    },
    showTableName(tableIdList){
      get_table_detail_Info_by_ids(tableIdList).then(res=>{
        if(res.code === '200'){
          this.options = res.data.map(item => {
            return { value: item.tableId, label: item.tableName+'(表类型:'+item.dataSourceTypeName+',所属数据源:'+item.dataSourceName+',所属库:'+item.dbName+')' };
          });
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    handleCopy(jobId) {
      this.copyJobId = jobId
      this.copyJobDialogFormVisible = true
    },
    cancel(formName) {
      this.copyJobDialogFormVisible = false
      this.$refs[formName].resetFields()
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          copyJob(this.copyJobId, this.copyJobForm.jobName).then(res => {
            if (res.code === '200') {
              this.copyJobDialogFormVisible = false
              this.$message({
                type: 'success',
                message: '复制作业成功!'
              })
              this.copyJobForm.jobName = ''
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
            document.getElementById("loadButton").click();
          })
        } else {
          return false
        }
      })
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>
  .tableInfo_div {
    display: flex;
  }

</style>
