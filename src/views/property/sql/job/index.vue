<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">bu</label>
        <el-input
          v-model="query.buName"
          clearable
          style="width: 100px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">空间名</label>
        <el-input
          v-model="query.projectName"
          clearable
          style="width: 150px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">空间名</label>
        <el-select
          v-model="query.projectName"
          filterable
          placeholder="空间名"
          clearable
          size="small"
          style="width: 155px;margin-right: 50px"
        >
          <el-option
            v-for="item in dict.作业空间名"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>
        <label class="el-form-item-label">作业名</label>
        <el-input
          v-model="query.taskName"
          clearable
          style="width: 300px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">作业状态</label>
        <el-input
          v-model="query.status"
          clearable
          style="width: 100px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">作业版本</label>
        <el-input
          v-model="query.engineName"
          clearable
          style="width: 100px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />

        <label class="el-form-item-label">血缘解析状态</label>
        <el-select
          v-model="query.parserStatus"
          filterable
          placeholder="血缘解析状态"
          clearable
          size="small"
          style="width: 255px;margin-right: 50px"
        >
          <el-option
            v-for="item in dict.血缘解析状态"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>

        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />

      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="800px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="空间名称" prop="projectName">
            <el-input v-model="form.projectName" style="width: 570px;" />
          </el-form-item>
          <el-form-item label="作业名称" prop="taskName">
            <el-input v-model="form.taskName" style="width: 570px;" />
          </el-form-item>
          <el-form-item label="作业描述" prop="taskDesc">
            <el-input v-model="form.taskDesc" style="width: 570px;" />
          </el-form-item>
          <el-form-item label="负责人" prop="username">
            <el-input v-model="form.operator" style="width: 570px;" />
          </el-form-item>
          <el-form-item label="创建人" prop="creator">
            <el-input v-model="form.creator" style="width: 570px;" />
          </el-form-item>
          <el-form-item label="作业状态" prop="status">
            <el-input v-model="form.status" style="width: 570px;" />
          </el-form-item>


          <el-form-item label="解析情况">
            <el-input v-model="form.sqlContent" :rows="10" type="textarea" style="width: 570px;" />
          </el-form-item>

          <el-form-item label="补缺dim表">
            <el-input v-model="form.lostTableNames" style="width: 570px;" />
          </el-form-item>


          <el-form-item
            label="合规"
            prop="isDelete">
            <el-select
              v-model="form.isGrc"
              style="width: 570px;"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in dict.是否合规"
                :key="item.id"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>


        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>


      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="buName" label="bu" />
        <el-table-column prop="projectName" label="空间名称" />
        <el-table-column prop="taskName" label="作业名称" />
        <el-table-column prop="operator" label="负责人" />
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="status" label="作业状态" />
        <el-table-column prop="engineName" label="作业版本" />

        <el-table-column
          prop="parserStatus"
          label="解析状态"
        >
          <template slot-scope="scope">
            {{ dict.label.血缘解析状态[scope.row.parserStatus] }}
          </template>
        </el-table-column>

        <el-table-column
          prop="isGrc"
          label="是否合规"
        >
          <template slot-scope="scope">
            {{ dict.label.是否合规[scope.row.isGrc] }}
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','flinkSqlJobLibra:edit','flinkSqlJobLibra:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudFlinkSqlJobLibra from '@/api/flinkSqlJobLibra'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { projectName: null, taskName: null, taskDesc: null, username: null, creator: null, operator: null, status: null, updateTime: null, createTime: null, modifyTime: null, id: null, sqlContent: null }
export default {
  name: 'FlinkSqlJobLibra',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['是否合规','血缘解析状态'],
  cruds() {
    return CRUD({ title: '自建平台sql作业', url: 'api/flinkSqlJobLibra', idField: 'id', sort: 'id,desc', crudMethod: { ...crudFlinkSqlJobLibra }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'flinkSqlJobLibra:add'],
        edit: ['admin', 'flinkSqlJobLibra:edit'],
        del: ['admin', 'flinkSqlJobLibra:del']
      },
      rules: {
        projectName: [
          { required: true, message: '空间名称不能为空', trigger: 'blur' }
        ],
        taskName: [
          { required: true, message: '作业名称不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '作业状态不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
