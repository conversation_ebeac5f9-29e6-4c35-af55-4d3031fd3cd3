<template>
  <div class="app-container">
    <el-dialog
      title="双流join操作配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px;margin-top: -20px">
        <el-form ref="ruleFormDragJoin" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item label="关联类型">
            <el-radio v-model="ruleForm.joinType" label="left" border>left</el-radio>
            <el-radio v-model="ruleForm.joinType" label="inner" border>inner</el-radio>
          </el-form-item>
          <el-form-item label="左流节点编号" prop="leftNumber">
            <el-input
              v-model="ruleForm.leftNumber"
              placeholder="请输入左流节点编号，在节点上右键可以复制"
              clearable
            />
          </el-form-item>
          <el-form-item label="右流节点编号" prop="rightNumber">
            <el-input
              v-model="ruleForm.rightNumber"
              placeholder="请输入右流节点编号，在节点上右键可以复制"
              clearable
            />
          </el-form-item>
          <el-form-item label="关联条件" prop="joinConditionExpression">
            <el-input
              v-model="ruleForm.joinConditionExpression"
              placeholder="example=> tl.user_id = tr.userid  注意别名: 左流用tl，维表用tr"
              clearable
            />
          </el-form-item>
          <el-form-item label="选择字段" prop="selectExpression">
            <el-input
              v-model="ruleForm.selectExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> tl.*, tr.country  注意别名: 左流用tl，维表用tr，如果全选可以用*"
              clearable
            />
          </el-form-item>
<!--          <el-form-item label="是否使用最新一条数据关联">-->
<!--            <el-radio v-model="ruleForm.isDeduplicateByJoinKey" label="true" border>是</el-radio>-->
<!--            <el-radio v-model="ruleForm.isDeduplicateByJoinKey" label="false" border>否</el-radio>-->
<!--          </el-form-item>-->
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DragJoin',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleForm: {
        selectExpression: '',
        joinType: 'left',
        leftNumber: '',
        rightNumber: '',
        joinConditionExpression: '',
        isDeduplicateByJoinKey: 'true'
      },
      rules: {
        leftNumber: [
          { required: true, message: '请选择左流节点编号', trigger: 'change' }
        ],
        rightNumber: [
          { required: true, message: '请选择右流节点编号', trigger: 'change' }
        ],
        joinConditionExpression: [
          { required: true, message: '请选择关联条件', trigger: 'blur' }
        ],
        selectExpression: [
          { required: true, message: '请选择字段', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'JoinTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        const tempExtend = JSON.parse(this.extend)

        this.ruleForm.selectExpression = tempExtend.selectExpressionOrigin === undefined ? '' : tempExtend.selectExpressionOrigin
        this.ruleForm.joinConditionExpression = tempExtend.joinConditionExpression === undefined ? '' : tempExtend.joinConditionExpression
        this.ruleForm.joinType = tempExtend.joinType === undefined ? 'left' : tempExtend.joinType
        this.ruleForm.leftNumber = tempExtend.leftNumber === undefined ? '' : tempExtend.leftNumber
        this.ruleForm.rightNumber = tempExtend.rightNumber === undefined ? '' : tempExtend.rightNumber
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragJoin'].validate((valid) => {
          const transformation = { 'selectExpression': '', 'joinConditionExpression': '', 'joinType': '', 'leftViewAlias': 'tl', 'rightViewAlias': 'tr', 'isDeduplicateByJoinKey': false, 'leftNumber': '', 'rightNumber': '', 'selectExpressionOrigin': '' }

          transformation.selectExpression = this.ruleForm.selectExpression.trim().replace(/\n/g, '')
          transformation.selectExpressionOrigin = this.ruleForm.selectExpression.trim()
          transformation.joinType = this.ruleForm.joinType
          transformation.joinConditionExpression = this.ruleForm.joinConditionExpression
          transformation.leftNumber = this.ruleForm.leftNumber.trim()
          transformation.rightNumber = this.ruleForm.rightNumber.trim()

          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragJoin'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.$refs['ruleFormDragJoin'].resetFields()
      }
    },
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

