<template>
  <div class="app-container">
    <el-dialog
      :title="titleShowTag"
      :close-on-click-modal="false"
      :show-close="true"
      :visible.sync="tag"
      :before-close="handleClose"
      :modal-append-to-body="false"
      width="60%"
      top="10vh"
    >
        <el-row style="margin-top: -20px">
          <el-col>
            <el-table
              :data="tableData"
              stripe
              highlight-current-row
              :header-cell-style="{textAlign: 'left'}"
              size="medium"
              style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
            >
              <el-table-column
                prop="jobName"
                label="作业名称"
                fixed
                sortable
                align="left"
                width="500"
              >
                <template slot-scope="scope">
                  <el-popover trigger="hover" placement="top-end" title="表信息">
                    <p style="font: 14px Base"> {{ scope.row.tableDescription }} </p>
                    <div slot="reference" class="name-wrapper">
                      <div style="font: 16px Medium">{{ scope.row.jobName }}</div>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column
                prop="status"
                label="运行状态"
                sortable
                align="left"
                width="100"
              >
                <template slot-scope="scope">
                  <el-tag type="info" :style="setStatusFontColor(scope.row.status)" size="medium">{{ getJobStatusDesc(scope.row.status) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                label="相关操作"
                width="500"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    plain
                    type="primary"
                    class="el-icon-link"
                    :disabled="scope.row.status !== 3"
                    @click="redirectUrl(scope.row)"
                  >WebUi</el-button>
                  <el-button
                    size="mini"
                    type="info"
                    plain
                    class="el-icon-view"
                    :loading="scope.row.validateButtonLoading"
                    @click="validateJob(scope.row)"
                  >校验作业</el-button>
                  <el-button
                    size="mini"
                    type="primary"
                    plain
                    :loading="scope.row.onlineButtonLoading"
                    class="el-icon-top"
                    :disabled="scope.row.status !== 0 && scope.row.status !== 6 && scope.row.status !== 4 && scope.row.status !== 12 && scope.row.status !== 14 && scope.row.status !== 19 && scope.row.status !== 21"
                    @click="handleOnlinePre(scope.row)"
                  >上线</el-button>
                  <el-button
                    size="mini"
                    type="success"
                    plain
                    :loading="scope.row.offlineButtonLoading"
                    :disabled="scope.row.status !== 3 && scope.row.status !== 16"
                    @click="handleOffline(scope.$index, scope.row)"
                    class="el-icon-bottom"
                  >下线</el-button>
                  <el-button
                    size="mini"
                    type="info"
                    plain
                    @click="refresh"
                    class="el-icon-refresh"
                  >刷新</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <OnlineJob style="position: absolute" :is-show="onlineJobFlag" :is-has-latest-checkpoint="isHasLatestCheckpoint" :online-job-name="onlineJobName" @close="handleOnlineRes"/>
    </el-dialog>


  </div>
</template>

<script>
import OnlineJob from '@/components/Online/index'
import { queryJobPageable, deleteJob, deployJob, deployJobWithLatestCheckpoint, stopJob, copyJob, validateJob, get_job_webUrl, job_is_has_latest_checkpoint, get_sub_jobs } from '@/api/job'
import { getJobStatusDesc } from '../realtime/js/common'

export default {
    name: 'SubTask',
    components: {
      OnlineJob
    },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    superTaskId: {
      type: Number,
      default: 0
    },
    superTaskName: {
      type: String,
      default: ''
    }
  },
    data() {
      return {
        tableData: [],
        loading: false,
        validateButtonLoading: false,
        offlineButtonLoading: false,

        onlineJobFlag: false,
        onlineJobName: '',
        isHasLatestCheckpoint: '0',
        onlineJobId: 0
      }
    },
    computed: {
      tag(){
        return this.isShow
      },
      titleShowTag() {
        return this.superTaskName + ' 子任务'
      },
    },
    watch: {
      isShow(newValue, oldValue) {
        if (newValue === true) {
          get_sub_jobs(this.superTaskId).then(res=>{
            console.log(res)
            if (res.code === '200') {
              this.fillTableData(res.data)
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        }
      }
    },
    mounted() {
    },
    created() {
    },
    methods: {
      doSearch(){
        this.loading = true
        get_sub_jobs(this.superTaskId).then(res=>{
          console.log(res)
          if (res.code === '200') {
            this.fillTableData(res.data)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
          this.loading = false
        })
      },
      fillTableData(data) {
        const tempTableData = []
        data.forEach(item => {
          tempTableData.push({ 'jobId': item.id, 'jobName': item.jobName, 'status': item.status, 'tableDescription': item.tableDescription, 'validateButtonLoading':false, 'onlineButtonLoading':false,'offlineButtonLoading':false,'deleteButtonLoading':false})
        })
        this.tableData = tempTableData
      },
      setStatusFontColor(jobStatus) {
        switch (jobStatus) {
          case 0:
            return 'color: #930dd3'
          case 1:
            return 'color: #12AAAD'
          case 3:
            return 'color: red'
          case 6:
            return 'color: #221b0a'
          default:
            return 'color: #bcbdae'
        }
      },
      getJobStatusDesc(statusCode) {
        return getJobStatusDesc(statusCode)
      },
      validateJob(row) {
        row.validateButtonLoading = true
        validateJob(row.jobId).then(res => {
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '校验通过 🎉'
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              dangerouslyUseHTMLString: true,
              message: '校验失败 😭' + '<br>' + res.mes
            })
          }
          row.validateButtonLoading = false
        })
      },
      handleOnlinePre(row){
        row.onlineButtonLoading = true
        this.onlineJobId = row.jobId
        job_is_has_latest_checkpoint(row.jobId).then(res => {
          if (res.code === '200') {
            this.onlineJobFlag = true
            this.onlineJobName = row.jobName
            if(res.data === '0'){
              this.isHasLatestCheckpoint = '0'
            }
            if(res.data === '1'){
              this.isHasLatestCheckpoint = '1'
            }
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
          row.onlineButtonLoading=false
        })
      },
      handleOnlineOld(jobId) {
        deployJob(jobId).then(res => {
          if (res.status === 200) {
            this.$message({
              type: 'success',
              message: '上线进入审批流程!'
            })
          }
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '提交上线成功!'
            })
            this.doSearch()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      },
      handleOnlineNew(jobId) {
        deployJobWithLatestCheckpoint(jobId).then(res => {
          if (res.status === 200) {
            this.$message({
              type: 'success',
              message: '上线进入审批流程!'
            })
          }
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '提交上线成功!'
            })
            this.doSearch()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      },
      handleOffline(index, row) {
        this.$confirm('是否确认下线 ' + row.jobName + ' ?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          row.offlineButtonLoading = true
          stopJob(row.jobId).then(res => {
            if (res.status === 200) {
              this.$message({
                type: 'success',
                message: '下线进入审批流程!'
              })
            }
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '提交下线成功!'
              })
              this.doSearch()
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
            row.offlineButtonLoading = false
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消下线'
          })
        })
      },
      redirectUrl(row){
        get_job_webUrl(row.jobId).then(res=>{
          if (res.code === '200') {
            window.open(res.data)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      },
      handleOnlineRes(tag,startType){
        this.onlineJobFlag = false
        if(tag === '1'){
          if(startType === '0'){
            this.handleOnlineOld(this.onlineJobId)
          }
          if(startType === '1'){
            this.handleOnlineNew(this.onlineJobId)
          }
        }
      },
      handleClose(){
        this.clearData()
        this.$emit('close')
      },
      refresh(){
        this.doSearch()
      },
      clearData(){
        this.tableData = []
      }

    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>
