// 校验作业名称规则
export function validateJobName(rule, value, callback) {
  if (!value) {
    return callback()
  }
  if (value) {
    const reg = /^[a-z][a-z0-9_]*$/
    if (!reg.test(value)) {
      callback(new Error('以字母开头，仅包含小写英文字母、数字、下划线！'))
    } else {
      callback()
    }
  }
}

// 校验作sql
export function validateSql(rule, value, callback) {
  if (!value) {
    return callback()
  }
  if (value) {
    if(value.includes('insert')){
      callback(new Error('sql语句不用添加insert片段！'))
    }else{
      callback()
    }
  }
}

