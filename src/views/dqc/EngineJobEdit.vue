<template>
  <div class="app-container">
    <el-dialog
      :title="titleShowTag"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="tag"
      width="55%"
      top="10vh"
    >
      <el-form ref="ruleForm" :rules="rules" :model="ruleForm" label-width="100px" style="margin-top: -20px;margin-bottom: -30px">
        <el-form-item label="作业名" prop="jobName">
          <el-input v-model="ruleForm.jobName" :disabled="this.ruleForm.jobId !== 0" placeholder="请输入作业名称" clearable />
        </el-form-item>
        <el-form-item label="libra作业名" prop="libraJobName">
          <el-autocomplete
            v-model="ruleForm.libraJobName"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入关联libra平台作业名称"
            clearable
            style="width: 100%"
            @select="handleSelect"
          />
        </el-form-item>
        <el-form-item label="作业描述">
          <el-input v-model="ruleForm.jobDesc" placeholder="请输入作业描述" clearable />
        </el-form-item>
        <el-form-item label="* 输入表">
          <div class="tableInfo_div">
            <el-select
              v-model="ruleForm.sourceTableIds"
              filterable
              multiple
              remote
              reserve-keyword
              placeholder="请输入关键字并选择输入表"
              style="width: 100%;"
              :remote-method="remoteMethod"
              :loading="loading">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-button type="info" plain @click="showTableName(ruleForm.sourceTableIds)">显示表名</el-button>
          </div>
        </el-form-item>
        <el-form-item label="自定义处理">
          <el-input v-model="ruleForm.sql" placeholder="请输入sql语句" type="textarea" clearable />
        </el-form-item>
        <el-form-item label="* 规则列表">
          <el-card shadow="never">
            <div slot="header" style="height: 6px;line-height: 6px">
              <el-button style="float: right;padding: 0;font-size: medium" type="text" @click="addItem">新增规则</el-button>
            </div>
            <div v-for="(item,index) in principles" :key="index" class="text item">
              <el-row :gutter="8">
                <el-col :span="14">
                  <el-input v-model="item.rule" placeholder="请输入规则内容"></el-input>
                </el-col>
                <el-col :span="8">
                  <el-input v-model="item.rule_name" placeholder="请输入规则名称"></el-input>
                </el-col>
                <el-col :span="2">
                  <el-button type="danger" @click="deleteItem(item, index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form-item>
        <el-form-item label="缓存时间" prop="cacheTTL">
          <el-select  style="width: 100%;" v-model="ruleForm.cacheTTL" placeholder="请选择作业缓存时间">
            <el-option
              v-for="item in cacheTTLOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="追溯时间" prop="relativeStartTs">
          <el-input v-model="ruleForm.relativeStartTs" placeholder="填day-x或hour-x或minute-1 (day-1表示从昨天0点启动，hour-1表示当前时间前一个小时启动，不填写则从当前时间启动)" clearable />
        </el-form-item>
        <!--        <el-form-item label="告警开关">-->
        <!--          <el-row :gutter="2">-->
        <!--            <el-col :span="8">-->
        <!--              <template>-->
        <!--                <el-radio v-model="ruleForm.isMonitor" label='1'>开启告警</el-radio>-->
        <!--                <el-radio v-model="ruleForm.isMonitor" label='0'>关闭告警</el-radio>-->
        <!--              </template>-->
        <!--            </el-col>-->
        <!--            <el-col :span="2">-->
        <!--              <el-tooltip placement="right-start">-->
        <!--                <div slot="content">-->
        <!--                  默认开启，在生产测试的时候可以先关闭告警，测试结束后再开启~-->
        <!--                </div>-->
        <!--                <div class="el-icon-info" />-->
        <!--              </el-tooltip>-->
        <!--            </el-col>-->
        <!--          </el-row>-->
        <!--        </el-form-item>-->
        <el-form-item label="作业高级参数">
          <el-input   type="textarea" :rows="4" v-model="ruleForm.optionConfigs" placeholder="请输入作业高级参数，一般不填" clearable />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelForm('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')" :loading="submitButtonLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import { getTableInfoCommonSql, saveJob, updateJob, blurry_search_libra_job, get_table_detail_Info_by_ids } from '@/api/job'
  import { validateJobName } from '../realtime/js/validator'

  export default {
    name: 'DqcEdit',
    components: {
    },
    props: {
      isShow: {
        type: Boolean,
        default: false
      },
      editJson: {
        type: Object,
        default: function() {
          return { 'jobId': 0, 'jobName': '', 'isMonitor': '1', 'dagJson':{} }
        }
      }
    },
    data() {
      return {
        ruleForm: {
          jobId: 0,
          jobName: '',
          libraJobName: '',
          jobDesc: '',
          isMonitor: '1',
          jobSpace: 'DEFAULT',
          departmentLabel: '数据平台',
          jobLevel: '4',
          sourceTableIds: [],
          sql: '',
          filterSelf: '',
          sinkTableIds: [],
          principles: {},
          exprType: 'QLExpress',
          type: 'DQCDynamicRuleTemplate',
          moduleName: 'DQCDynamicRuleTemplate',
          sqlTemplateMode: 'dqc',
          sinkSchemaPrimaryKey: 'MSG,TAG,JOB,TABLE,TS',
          cacheTTL: '3600000',
          relativeStartTs: 'minute-1',
          optionConfigs: '',
          webJson: '',
          dagJson: '',
          sinkTableId:'7063',
        },
        rules: {
          jobName: [
            { required: true, message: '请输入作业名称', trigger: 'blur' },
            { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' },
            { validator: validateJobName, trigger: ['blur'] }
          ],
          libraJobName: [
            { required: true, message: '请输入关联libra作业名称', trigger: 'blur' },
            { min: 1, max: 85, message: '长度在 1 到 60 个字符', trigger: 'blur' }
          ],
          relativeStartTs:[
            { required: true, message: '请输入追溯时间', trigger: 'blur' }
          ],

        },

        options: [],
        value: [],
        list: [],
        list2: [],
        loading: false,

        principles: [],

        cacheTTLOptions: [{
          value: '3600000',
          label: '1小时'
        },{
          value: '86400000',
          label: '1天'
        }, {
          value: '172800000',
          label: '2天'
        }, {
          value: '259200000',
          label: '3天'
        }, {
          value: '432000000',
          label: '5天'
        }, {
          value: '604800000',
          label: '7天'
        }],
        submitButtonLoading: false
      }
    },
    computed: {
      tag() {
        return this.isShow
      },
      titleShowTag() {
        return this.ruleForm.jobId === 0 ? '新增作业' : '修改作业'
      },
      editTag() {
      }
    },
    watch: {
      isShow(newValue, oldValue) {
        if (newValue === true) {
          this.ruleForm.jobId = this.editJson.jobId
          this.ruleForm.jobName = this.editJson.jobName
          this.ruleForm.jobDesc = this.editJson.jobDesc
          this.ruleForm.isMonitor = this.editJson.isMonitor
          if(this.ruleForm.jobId !==0){
            this.ruleForm.libraJobName = this.editJson.dagJson.libraJobName
            this.ruleForm.sourceTableIds = this.editJson.dagJson.sourceTableIds
            this.ruleForm.sinkTableId = this.editJson.dagJson.sinkTableIds[0]
            this.ruleForm.sql = this.editJson.dagJson.sql
            this.ruleForm.filterSelf = this.editJson.dagJson.filter
            this.ruleForm.sinkSchemaPrimaryKey = this.editJson.dagJson.sinkSchemaPrimaryKey
            this.ruleForm.cacheTTL = this.editJson.dagJson.cacheTTL
            this.ruleForm.relativeStartTs = this.editJson.dagJson.relativeStartTs
            this.ruleForm.optionConfigs = this.editJson.dagJson.optionConfigs

            const pc = this.editJson.dagJson.principles
            for(const i in pc){
              this.principles.push({'rule':i,'rule_name':pc[i]})
            }
          }else{
            this.ruleForm.libraJobName = ''
            this.ruleForm.sourceTableIds = []
            this.ruleForm.sinkTableId = '7063'
            this.ruleForm.sql = ''
            this.ruleForm.filterSelf = ''
            this.ruleForm.sinkSchemaPrimaryKey = 'MSG,TAG,JOB,TABLE,TS'
            this.ruleForm.cacheTTL = '3600000'
            this.ruleForm.relativeStartTs = 'minute-1'
            this.ruleForm.optionConfigs = ''
          }
        }
      }
    },
    mounted() {
    },
    methods: {
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            const res = this.validateOtherInfo()
            if(res === '1'){
              const finalJson  = this.prepareFinalJson()

              console.log(JSON.stringify(finalJson))

              this.submitButtonLoading = true
              if(finalJson.id === 0){
                saveJob(JSON.stringify(finalJson)).then(res => {
                  if (res.code === '200') {
                    this.$message({
                      type: 'success',
                      message: '创建成功!'
                    })
                    this.clearData()
                    this.$emit('close', '1')
                  }
                  if (res.code === '500') {
                    this.$message({
                      type: 'error',
                      message: res.mes
                    })
                  }
                  this.submitButtonLoading = false
                })
              }else{
                updateJob(JSON.stringify(finalJson)).then(res => {
                  if (res.code === '200') {
                    this.$message({
                      type: 'success',
                      message: '更新成功!'
                    })
                    this.clearData()
                    this.$emit('close', '1')
                  }
                  if (res.code === '500') {
                    this.$message({
                      type: 'error',
                      message: res.mes
                    })
                  }
                  this.submitButtonLoading = false
                })
              }
            }
          } else {
            return false
          }
        })
      },
      cancelForm(formName) {
        this.clearData()
        this.$refs[formName].resetFields()
        this.$emit('close', '0')
      },
      prepareFinalJson(){
        const tem_principles = {}
        const tem_sinkTableIds = []
        //拼接数据
        this.principles.forEach(item=>{
          this.$set(tem_principles,item.rule,item.rule_name)
        })

        this.ruleForm.principles = tem_principles

        tem_sinkTableIds.push(this.ruleForm.sinkTableId)

        this.ruleForm.sinkTableIds = tem_sinkTableIds

        //拼接最终Json
        const finalJson = {}
        finalJson.id = this.ruleForm.jobId
        finalJson.jobName = this.ruleForm.jobName
        finalJson.jobDesc = this.ruleForm.jobDesc
        finalJson.isMonitor = parseInt(this.ruleForm.isMonitor)
        finalJson.jobSpace = this.ruleForm.jobSpace
        finalJson.departmentLabel = this.ruleForm.departmentLabel
        finalJson.type = this.ruleForm.type
        finalJson.jobLevel = parseInt(this.ruleForm.jobLevel)
        finalJson.webJson = this.ruleForm.webJson

        const finalDagJson = {}
        finalDagJson.libraJobName = this.ruleForm.libraJobName
        finalDagJson.exprType = this.ruleForm.exprType
        finalDagJson.moduleName = this.ruleForm.moduleName
        finalDagJson.sql = this.ruleForm.sql
        finalDagJson.principles = this.ruleForm.principles
        finalDagJson.sqlTemplateMode = this.ruleForm.sqlTemplateMode
        finalDagJson.filter = this.ruleForm.filterSelf
        finalDagJson.sourceTableIds = this.ruleForm.sourceTableIds
        finalDagJson.sinkTableIds = this.ruleForm.sinkTableIds
        finalDagJson.sinkSchemaPrimaryKey = this.ruleForm.sinkSchemaPrimaryKey
        finalDagJson.cacheTTL = this.ruleForm.cacheTTL
        finalDagJson.relativeStartTs = this.ruleForm.relativeStartTs
        finalDagJson.optionConfigs = this.ruleForm.optionConfigs

        finalJson.dagJson = JSON.stringify(finalDagJson)

        return finalJson
      },
      validateOtherInfo(){
        if (this.ruleForm.sourceTableIds.length === 0) {
          this.$message({
            showClose: true,
            message: '特别警告，输入表没有选择',
            type: 'warning'
          })
          return '0'
        }
        if (this.principles.length === 0) {
          this.$message({
            showClose: true,
            message: '特别警告，规则列表没有填写',
            type: 'warning'
          })
          return '0'
        }else{
          try{
            this.principles.forEach(item=>{
              for(const i in item){
                if(i === '' || item[i]===''){
                  this.$message({
                    showClose: true,
                    message: '特别警告，规则列表没有填写完整',
                    type: 'warning'
                  })
                  throw new Error("false")
                }
              }
            })
          }catch (e) {
            return '0'
          }


        }
        return '1'
      },
      clearData(){
        this.ruleForm.jobId = 0,
          this.ruleForm.jobName = '',
          this.ruleForm.jobDesc = '',
          this.ruleForm.libraJobName = '',
          this.ruleForm.jobSpace = 'DEFAULT',
          this.ruleForm.departmentLabel = '数据平台',
          this.ruleForm.jobLevel = '4',
          this.ruleForm.sourceTableIds = [],
          this.ruleForm.sql = '',
          this.ruleForm.filterSelf = '',
          this.ruleForm.sinkTableIds = [],
          this.ruleForm.principles = {},
          this.ruleForm.exprType = 'QLExpress',
          this.ruleForm.type = 'DQCDynamicRuleTemplate',
          this.ruleForm.moduleName = 'DQCDynamicRuleTemplate',
          this.ruleForm.sqlTemplateMode = 'dqc',
          this.ruleForm.sinkSchemaPrimaryKey = 'MSG,TAG,JOB,TABLE,TS',
          this.ruleForm.cacheTTL = '',
          this.ruleForm.relativeStartTs = 'minute-1',
          this.ruleForm.optionConfigs = '',
          this.ruleForm.webJson = '',
          this.ruleForm.dagJson = '',
          this.ruleForm.sinkTableId = '7063',
          this.principles = []
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
          const pathParams = []
          pathParams.push({ 'paramName': 'tableName', 'paramValue': query })
          getTableInfoCommonSql(pathParams).then(res=>{
            if(res.code === '200'){
              this.list = res.data.map(item => {
                return { value: item.tableId, label: item.tableName+'(类型:'+item.dataSourceTypeName+',数据源:'+item.dataSourceName+',库:'+item.dbName+')' };
              });
              this.loading = false;
              this.options = this.list.filter(item => {
                return item.label.toLowerCase()
                  .indexOf(query.toLowerCase()) > -1;
              });
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        } else {
          this.options = [];
        }
      },
      addItem(){
        this.principles.push({
          rule: '',
          rule_name: ''
        });
      },
      deleteItem(item, index) {
        this.principles.splice(index, 1);
      },
      querySearchAsync(queryString, cb) {
        const result = []
        if(queryString !== ''){
          blurry_search_libra_job(queryString).then(res=>{
            res.data.forEach(item=>{
              result.push({'value':item})
            })
          })
        }
        cb(result)
      },
      handleSelect(item) {
        // this.ruleForm.libraJobName = item.value
      },
      showTableName(tableIdList){
        get_table_detail_Info_by_ids(tableIdList).then(res=>{
          if(res.code === '200'){
            this.options = res.data.map(item => {
              return { value: item.tableId, label: item.tableName+'(类型:'+item.dataSourceTypeName+',数据源:'+item.dataSourceName+',库:'+item.dbName+')' };
            });
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }

    }
  }
</script>

<style>
  .text {
    font-size: 14px;
  }
  .item {
    margin-bottom: 10px;
  }

  .tableInfo_div {
    display: flex;
  }
  .tableInfo_div .el-select{
    flex: 50 1 auto;
  }

  .tableInfo_div .el-button{
    flex: 1 1 auto;
  }

</style>
