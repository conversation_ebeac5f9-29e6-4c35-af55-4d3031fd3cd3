<template>
  <div class="app-container">
    <el-dialog
      :title="titleShowTag"
      :visible="tag"
      width="70%"
      :close-on-click-modal="false"
      :show-close="true"
      :before-close="handleClose"
      top="10vh"
    >

      <div class="main-div">
        <el-form ref="ruleForm" :rules="rules" :model="ruleForm" label-width="120px" style="margin-top: -20px">
<!--          <el-form-item label="libra平台作业" prop="taskName">-->
<!--            <el-input v-model="ruleForm.taskName" :disabled="this.ruleForm.jobId !== 0" placeholder="请输入libra平台作业名称" clearable />-->
<!--          </el-form-item>-->
          <el-form-item label="libra作业名" prop="taskName">
            <el-autocomplete
              v-model="ruleForm.taskName"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入libra平台作业名称"
              clearable
              :disabled="this.ruleForm.jobId !== 0"
              style="width: 100%"
              @select="handleSelect"
            />
          </el-form-item>
          <el-form-item label="数据演练描述">
            <el-input v-model="ruleForm.drillDescribe" placeholder="请输入数据演练描述" clearable />
          </el-form-item>
          <el-form-item label="* 数据演练规则">
            <el-card shadow="never" v-loading="isSourceAndDimLoading">
              <div slot="header" style="margin-bottom: 20px">
                <el-button style="float: left; padding: 3px 3px;font-size: large" type="info" plain class="el-icon-s-unfold" @click="getLibraJobSourceAndDimTableInfo">获取作业表信息</el-button>
                <el-button style="float: right; padding: 3px 3px;font-size: large" :disabled="this.tableContentItemList.length === 0" type="primary" plain class="el-icon-caret-right" @click="runDrill" v-loading="isExecuteDqcButtonLoading">执行数据演练</el-button>
                <el-button style="float: right; padding: 3px 3px;font-size: large" :disabled="this.sourceTableNameList.length === 0" type="info" :loading="isSamplingLoading" plain class="el-icon-magic-stick" @click="sampling">源表数据采样</el-button>

              </div>
              <el-row :gutter="20" style="margin-bottom: 2px;display: flex;flex-wrap: wrap">
                <el-col :span="12"  v-for="sourceTableName in sourceTableNameList">
                  <el-card>
                    <div slot="header">
                      <span style="font-size: medium"><b>数据源表: {{sourceTableName}}</b> </span>
                      <el-button  @click="addItem(sourceTableName)" style="float: right;font-size: medium;color: #40c9c6" type="text">新增</el-button>
                    </div>
                    <div v-for="(item,index) in tableContentItemList" :key="index">
                      <div v-if="item.tableName === sourceTableName">
                        <el-row :gutter="5" style="margin-bottom: 2px;">
                          <el-col :span="20">
                            <el-input
                              type="textarea"
                              :rows="2"
                              @blur="touchBlur(item.inputValue)"
                              placeholder="请输入一条json格式记录"
                              v-model="item.inputValue"
                            />
                          </el-col>
                          <el-col :span="4">
                            <el-button @click="deleteItem(item, index)" type="text" style="font-size: medium;color: #42b983">删除</el-button>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-top: 40px;display: flex;flex-wrap: wrap">
                <el-col :span="12" v-for="dimTableName in dimTableNameList">
                  <el-card>
                    <div slot="header">
                      <span style="font-size: medium"><b>维度表: {{dimTableName}}</b> </span>
                      <el-button  @click="addItem(dimTableName)" style="float: right;font-size: medium;color: #40c9c6" type="text">新增</el-button>
                    </div>
                    <div v-for="(item,index) in tableContentItemList" :key="index">
                      <div v-if="item.tableName === dimTableName">
                        <el-row :gutter="5" style="margin-bottom: 2px;">
                          <el-col :span="20">
                            <el-input
                              type="textarea"
                              :rows="2"
                              placeholder="请输入一条json格式记录"
                              @blur="touchBlur(item.inputValue)"
                              v-model="item.inputValue"
                            />
                          </el-col>
                          <el-col :span="4">
                            <el-button @click="deleteItem(item, index)" type="text" style="font-size: medium;color: #42b983">删除</el-button>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </el-card>
          </el-form-item>
          <el-form-item label="数据演练结果">
            <el-card shadow="never" v-loading="isResultLoading">
              <json-viewer :value="dqcResultJson" :expand-depth="expandDepth" :copyable="copyConfig" theme="jv-light" />
            </el-card>
          </el-form-item>
          <el-form-item label="演练期待结果">
            <el-card shadow="never">
              <json-viewer :value="dqcExpectResultJson" :expand-depth="expandDepth" :copyable="copyConfig" theme="jv-light" />
            </el-card>
          </el-form-item>
        </el-form>

      </div>

      <el-button @click="test">test</el-button>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveExpectResult">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { get_job_dqc_res, get_dqc_res, blurry_search_libra_job, createPreDrillJob, updatePreDrillJob, getLibraJobSourceAndDimTableInfo, get_table_message } from '@/api/job'
import { validateJobName } from '../realtime/js/validator'

  export default {
    name: 'DrillPanelEdit',
    components: {
    },
    props: {
      isShow: {
        type: Boolean,
        default: false
      },
      editJson: {
        type: Object,
        default: function() {
          return { 'id': 0, 'taskName': '', 'drillDescribe': '', 'data': {}, 'result': {} }
        }
      }

    },
    data() {
      return {
        tableContentItemList: [{"tableName":"test_source_01","inputValue":"{\"a\":\"111\"}"},{"tableName":"test_source_02","inputValue":"{\"b\":\"222\"}"},{"tableName":"test_dim_table","inputValue":"{\"a\":\"111\"}"},{"tableName":"test_dim_table","inputValue":"{\"b\":\"222\"}"}],
        dqcResultJson: [],
        dqcExpectResultJson: [],
        expandDepth: 10,
        copyConfig: { copyText: '复制', copiedText: '复制成功' },
        dqcRequestBody: {'isDataDrill':true,'sourceTableIds':[],'sourceData':[],'teJobInfoDto':{'jobName':'','type':'B2S_TEMPLATE','dagJson':"{\"rawMode\":\"libra\",\"cacheTTL\":\"3600000\",\"relativeStartTs\":\"minute-1\",\"moduleName\":\"B2S_TEMPLATE\"}"}},
        executeDqcButtonIsDisabled: false,
        executeDqcButtonIsLoading: false,
        executeDqcValidate: false,
        timer: null,

        ruleForm: {
          jobId: 0,
          taskName: '',
          drillDescribe: '',
        },
        rules: {
          taskName: [
            { required: true, message: '请输入作业名称', trigger: 'blur' },
            { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' },
            { validator: validateJobName, trigger: ['blur'] }
          ],

        },
        isSourceAndDimLoading: false,
        isResultLoading: false,
        isExecuteDqcButtonLoading: false,
        sourceTableNameList:[],
        dimTableNameList:[],
        sourceTableIds:[],
        isSamplingLoading: false,
      }
    },
    computed: {
      tag() {
        return this.isShow
      },
      resultSize(){
        return this.dqcResultJson.length
      },
      titleShowTag() {
        return this.ruleForm.jobId === 0 ? '数据演练新增' : '数据演练修改'
      },
    },
    watch: {
      isShow(newValue, oldValue) {
        if (newValue === true) {
          this.ruleForm.jobId = this.editJson.id
          this.ruleForm.taskName = this.editJson.taskName
          this.ruleForm.drillDescribe = this.editJson.drillDescribe
          if(this.ruleForm.jobId !==0){
            const temSourceTableNameList = []
            const temDimTableNameList = []
            const temTableContentItemList = this.editJson.data.sourceData
            this.sourceTableIds = this.editJson.data.sourceTableIds
            this.tableContentItemList = []
            temTableContentItemList.forEach(item=>{
              item.data.forEach(ii=>{
                this.tableContentItemList.push({'tableName':item.dataName,'inputValue':ii})
              })

            })

            this.editJson.data.sourceData.forEach(item=>{
              if(item.isDimTable === '0'){
                temSourceTableNameList.push(item.dataName)
              }
            })

            this.editJson.data.sourceData.forEach(item=>{
              if(item.isDimTable === '1'){
                temDimTableNameList.push(item.dataName)
              }
            })

            this.sourceTableNameList = temSourceTableNameList
            this.dimTableNameList = temDimTableNameList

            this.dqcExpectResultJson = this.editJson.result

            this.dqcResultJson = []
          }else{
            this.tableContentItemList = []
            this.dqcResultJson = []
            this.dqcExpectResultJson = []
            this.sourceTableNameList = []
            this.dimTableNameList = []
          }
        }
      }
    },
    mounted() {
    },
    created() {
    },
    methods: {
      onClose(tag) {
        this.clearData()
        this.$refs['ruleForm'].resetFields()
        this.$emit('close', tag)
      },
      clearData(){
        // this.tableContentItemList = []
        // this.dqcResultJson = []
        // this.dqcRequestBody = {'isDataDrill':true,'jobId':0,'sourceData':[]}
        this.executeDqcButtonIsDisabled = false
        // this.sourceTableNameList = []
        // this.dimTableNameList = []
      },
      addItem(sourceTableName) {
        this.tableContentItemList.push({
          tableName: sourceTableName,
          inputValue: ""
        });
      },
      deleteItem(item, index) {
        this.tableContentItemList.splice(index, 1);
      },
      validateInputTableDataList(){
        try{
          if(this.tableContentItemList.length === 0){
            throw new Error('请先填写数据记录在进行数据演练操作 ！！！')
          }
        }catch (e) {
          this.$message.error(e.message)
          return '0'
        }
        try{
          this.tableContentItemList.forEach(item=>{
            if(item.inputValue === ''){
              throw new Error('请将空白输入框填写完毕再进行数据演练操作 ！！！')
            }
          })
        }catch (e) {
          this.$message.error(e.message)
          return '0'
        }
        try{
          this.tableContentItemList.forEach(item=>{
            if(item.inputValue !== ''){
              if(item.inputValue === '{}'){
                throw new Error('请输入完成的json ！！！')
              }else{
                JSON.parse(item.inputValue)
              }
            }
          })
        }catch (e) {
          this.$message.error("请正确填写JSON格式的数据在进行数据演练操作 ！！！")
          return '0'
        }
        return '1'
      },
      prepareInputTableRecord(){
        this.dqcRequestBody.sourceData = []
        this.dqcRequestBody.sourceTableIds = this.sourceTableIds

        this.sourceTableNameList.forEach(ss=>{
          this.tableContentItemList.forEach(item=>{
            if(item.tableName === ss){
              if(this.isItemInList(ss) === '0'){
                const ttInput = []
                ttInput.push(item.inputValue)
                this.dqcRequestBody.sourceData.push({'dataName':ss,'data':ttInput,'isDimTable':'0'})
              }else{
                const idx = this.getItemIndex(ss)
                const formerValue = this.dqcRequestBody.sourceData[idx - 0].data
                const tempInput = []
                tempInput.push(item.inputValue)
                const tempData = formerValue.concat(tempInput)
                const tempFullData = {'dataName':'', 'data':[],'isDimTable':'0'}
                tempFullData.dataName = ss
                tempFullData.data = tempData
                this.dqcRequestBody.sourceData.splice(idx - 0,1)
                this.dqcRequestBody.sourceData.push(tempFullData)
              }
            }
          })
        })

        this.dimTableNameList.forEach(ss=>{
          this.tableContentItemList.forEach(item=>{
            if(item.tableName === ss){
              if(this.isItemInList(ss) === '0'){
                const ttInput = []
                ttInput.push(item.inputValue)
                this.dqcRequestBody.sourceData.push({'dataName':ss,'data':ttInput,'isDimTable':'1'})
              }else{
                const idx = this.getItemIndex(ss)
                const formerValue = this.dqcRequestBody.sourceData[idx - 0].data
                const tempInput = []
                tempInput.push(item.inputValue)
                const tempData = formerValue.concat(tempInput)
                const tempFullData = {'dataName':'', 'data':[],'isDimTable':'1'}
                tempFullData.dataName = ss
                tempFullData.data = tempData
                this.dqcRequestBody.sourceData.splice(idx - 0,1)
                this.dqcRequestBody.sourceData.push(tempFullData)
              }
            }
          })
        })

        this.dqcRequestBody.teJobInfoDto.jobName = this.ruleForm.taskName
        return JSON.stringify(this.dqcRequestBody)
      },
      isItemInList(tableName){
        try{
          this.dqcRequestBody.sourceData.forEach((item,index)=>{
            if(tableName === item.dataName){
              throw new Error('1')
            }
          })
          throw new Error('0')
        }catch (e) {
          return e.message
        }
      },
      getItemIndex(tableName){
        try{
          this.dqcRequestBody.sourceData.forEach((item,index)=>{
            if(tableName === item.dataName){
              throw new Error(index + '')
            }
          })
        }catch (e) {
          return e.message
        }
      },
      touchBlur(inputValue){
        try {
          if(inputValue !== ''){
            if(inputValue === '{}'){
              throw new Error('请填写完整的JSON格式数据')
            }else{
              JSON.parse(inputValue)
            }
          }
        } catch (e) {
          this.$message(inputValue + ' 不是完整JSON格式数据，请重新填写 ！！！')
        }
      },
      runDrill(){
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            const returnFlag = this.isDimTableEmpty()

            if(returnFlag === 1){
              this.$message({
                type: 'error',
                message: '请将所有的维表信息填写完毕在进行数据演练~'
              })
            }else{
              this.isExecuteDqcButtonLoading = true

              const jsonToRequest = this.prepareInputTableRecord()

              get_job_dqc_res(jsonToRequest).then(res=>{
                if (res.code === '200') {
                  this.isResultLoading = true
                  this.$message({
                    type: 'success',
                    message: '数据演练开始执行~'
                  })

                  const currentJobName = res.data.jobName
                  const currentSessionId = res.data.sessionId

                  this.timer = setInterval(()=>{
                    get_dqc_res(currentJobName,currentSessionId).then(res=>{
                      if(res.code === '200'){
                        if(res.data.code === 300){
                          console.log(res)
                        }
                        if(res.data.code === 200){
                          this.isResultLoading = false
                          this.isExecuteDqcButtonLoading = false
                          this.dqcResultJson = JSON.parse(res.data.actualData)
                          clearInterval(this.timer)
                        }
                      }
                      if (res.code === '500') {
                        this.isResultLoading = false
                        this.isExecuteDqcButtonLoading = false
                        this.$message({
                          type: 'error',
                          message: res.mes
                        })
                      }
                    })
                  },3000);
                }
                if (res.code === '500') {
                  this.isExecuteDqcButtonLoading = false
                  this.$message({
                    type: 'error',
                    message: res.mes
                  })
                }
              })
            }

          } else {
            return false
          }
        })

      },
      getLibraJobSourceAndDimTableInfo(){
          this.$refs['ruleForm'].validate((valid) => {
              if (valid) {
                this.isSourceAndDimLoading = true
                getLibraJobSourceAndDimTableInfo(this.ruleForm.taskName).then(res=>{
                  if (res.code === '200') {
                    this.isSourceAndDimLoading = false
                    this.sourceTableNameList = res.data.streamSourceTableNames
                    this.dimTableNameList = res.data.dimSourceTableNames
                    this.sourceTableIds = res.data.sourceTableIds
                  }
                  if (res.code === '500') {
                    this.isSourceAndDimLoading = false
                    this.$message({
                      type: 'error',
                      message: res.mes
                    })
                  }
                })
            } else {
              return false
            }
        })
      },
      saveExpectResult(){
        const resultSaveReq = {'data':'','result':'','taskName':'','drillDescribe':'','id':0}
        resultSaveReq.data = this.prepareInputTableRecord()
        resultSaveReq.result = this.dqcResultJson
        resultSaveReq.taskName = this.ruleForm.taskName
        resultSaveReq.drillDescribe = this.ruleForm.drillDescribe
        resultSaveReq.id = this.ruleForm.jobId

        if(resultSaveReq.id === 0){
          createPreDrillJob(resultSaveReq).then(res=>{
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '保存结果成功~'
              })
              this.onClose('1')
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        }else{
          updatePreDrillJob(resultSaveReq).then(res=>{
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.onClose('1')
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        }

      },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            const res = this.validateOtherInfo()
            if(res === '1'){
              const finalJson  = this.prepareFinalJson()
              console.log(JSON.stringify(finalJson))

              if(finalJson.id === 0){
                saveJob(JSON.stringify(finalJson)).then(res => {
                  if (res.code === '200') {
                    this.$message({
                      type: 'success',
                      message: '创建成功!'
                    })
                    this.clearData()
                    this.$emit('close', '1')
                  }
                  if (res.code === '500') {
                    this.$message({
                      type: 'error',
                      message: res.mes
                    })
                  }
                })
              }else{
                updateJob(JSON.stringify(finalJson)).then(res => {
                  if (res.code === '200') {
                    this.$message({
                      type: 'success',
                      message: '更新成功!'
                    })
                    this.clearData()
                    this.$emit('close', '1')
                  }
                  if (res.code === '500') {
                    this.$message({
                      type: 'error',
                      message: res.mes
                    })
                  }
                })
              }
            }
          } else {
            return false
          }
        })
      },
      cancelForm(formName) {
        this.clearData()
        this.$refs[formName].resetFields()
        this.$emit('close', '0')
      },
      sampling(){
        this.isSamplingLoading = true
        const tableIds = this.sourceTableIds
        get_table_message(tableIds).then(res=>{
          if (res.code === '200') {
            res.data.forEach(item=>{
              console.log(item.mess === undefined)
              if(item.mess !== undefined){
                console.log(item)
                this.tableContentItemList.push({
                  tableName: item.tableName,
                  inputValue: item.mess
                });
              }
            })
            this.$message({
              type: 'success',
              message: '数据源表采样成功，没有采集到的请手动输入️🎉'
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
          this.isSamplingLoading = false
        })
      },
      querySearchAsync(queryString, cb) {
        const result = []
        if(queryString !== ''){
          blurry_search_libra_job(queryString).then(res=>{
            res.data.forEach(item=>{
              result.push({'value':item})
            })
          })
        }
        cb(result)
      },
      handleSelect(item) {
        // this.ruleForm.libraJobName = item.value
      },
      handleClose(){
        this.onClose('0')
      },
      isDimTableEmpty(){
        let returnFlag = 0
        this.dimTableNameList.forEach(dd=>{
          let isHaveData = 0
          this.tableContentItemList.forEach(item=>{
            if(item.tableName === dd && item.inputValue !== ''){
              isHaveData = 1
            }
          })
          if(isHaveData === 0){
            returnFlag = 1
          }
        })

        return returnFlag
      }

    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .el-row {
    margin-bottom: 1px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-col {
    border-radius: 4px;
  }

  .grid-content {
    border-radius: 2px;
    min-height: 180px;
  }

  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

</style>
