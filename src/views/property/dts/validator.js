// 校验作业名称规则
export function validateTopicNameOversea(rule, value, callback) {
  if (!value) {
    return callback()
  }
  if (value) {
    const reg = /^oversea_ods_[a-zA-Z0-9_]+$/
    if (!reg.test(value)) {
      callback(new Error('必须以 oversea_ods_ 开头，且只能包含字母、数字和下划线'))
    } else {
      callback()
    }
  }
}

export function validateTopicNameShizhuang(rule, value, callback) {
  if (!value) {
    return callback()
  }
  if (value) {
    const reg = /^ods_[a-zA-Z0-9_]+$/
    if (!reg.test(value)) {
      callback(new Error('必须以 ods_ 开头，且只能包含字母、数字和下划线'))
    } else {
      callback()
    }
  }
}
