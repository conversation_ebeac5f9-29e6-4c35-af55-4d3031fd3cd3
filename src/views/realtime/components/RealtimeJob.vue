<template>
  <div class="app-container">
    <el-drawer
      :title="titleShowTag"
      size="98%"
      :visible.sync="tag"
      :close-on-press-escape="false"
      direction="btt"
      :before-close="close"
      :wrapper-closable="false"
    >
      <div class="realtime-job-dialog-content">
        <el-row class="element-job-info">
          <el-col>
            <el-form ref="ruleForm"  :rules="rules" :model="ruleForm" size="medium">
              <el-form-item label="作业名称" prop="jobName">
                <el-input v-model="ruleForm.jobName" :disabled="fullJobInfo.jobId!==0" style="width: 500px" placeholder="填写作业名称" />
              </el-form-item>
              <el-form-item label="作业级别" prop="jobLevel">
                <el-select v-model="ruleForm.jobLevel" clearable style="width: 200px" placeholder="选择级别">
                  <el-option label="P0" value="0" />
                  <el-option label="P1" value="1" />
                  <el-option label="P2" value="2" />
                  <el-option label="P3" value="3" />
                  <el-option label="P4" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="作业描述" prop="jobDesc">
                <el-input v-model="ruleForm.jobDesc" style="width: 500px" placeholder="填写作业描述信息" />
              </el-form-item>
<!--              <el-form-item label="告警开关">-->
<!--                <el-row :gutter="2">-->
<!--                  <el-col :span="4">-->
<!--                    <template>-->
<!--                      <el-radio v-model="ruleForm.isMonitor" label='1'>开启告警</el-radio>-->
<!--                      <el-radio v-model="ruleForm.isMonitor" label='0'>关闭告警</el-radio>-->
<!--                    </template>-->
<!--                  </el-col>-->
<!--                  <el-col :span="2">-->
<!--                    <el-tooltip placement="right-start">-->
<!--                      <div slot="content">-->
<!--                        默认开启，在生产测试的时候可以先关闭告警，测试结束后再开启~-->
<!--                      </div>-->
<!--                      <div class="el-icon-info" />-->
<!--                    </el-tooltip>-->
<!--                  </el-col>-->
<!--                </el-row>-->
<!--              </el-form-item>-->
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="background-color: #fff;padding-top: 3px;padding-left: 3px;margin-bottom: 31px">
            【可选】作业高级配置&nbsp <el-button type="text" class="el-icon-edit" size="medium" @click="openParamsEdit">打开文本编辑</el-button> <el-button type="text" class="el-icon-document-copy" size="medium" @click="doCopy">复制到剪切板</el-button>
            <el-collapse v-model="collapseList">
              <el-collapse-item title="点击折叠or展示高级配置信息">
                <el-table
                  :data="tableDataCompute"
                >
                  <el-table-column
                    prop="key"
                    label="KEY"
                    width="400"
                  />
                  <el-table-column
                    prop="value"
                    label="VALUE"
                    width="300"
                  />
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div
              class="page-content"
              @mousedown="startNodesBus($event)"
              @mousemove="moveNodesBus($event)"
              @mouseup="endNodesBus($event)"
            >
              <!-- 顶栏 -->
              <div class="headbar">
                <el-row :gutter="12">
                  <el-col :span="2">
                    <p @mousedown="dragIt('数据源','SourceTransformation','el-icon-heavy-rain')">数据源</p>
                  </el-col>
                  <el-col :span="2">
                    <p @mousedown="dragIt('结果表','SinkTransformation','el-icon-water-cup')">结果表</p>
                  </el-col>
                  <el-col :span="2">
                    <p @mousedown="dragIt('过滤操作','CalcTransformation','el-icon-refresh')">过滤操作</p>
                  </el-col>
                  <el-col :span="2">
                    <p @mousedown="dragIt('维表join操作','LookupJoinTransformation','el-icon-top-right')">维表join操作</p>
                  </el-col>
                  <el-col :span="2">
                    <p @mousedown="dragIt('聚合操作','GroupByTransformation','el-icon-s-grid')">聚合操作</p>
                  </el-col>
                  <el-col :span="2">
                    <p @mousedown="dragIt('双流join操作','JoinTransformation','el-icon-sort')">双流join操作</p>
                  </el-col>
                  <el-col :span="2">
                    <p @mousedown="dragIt('取TopN操作','TopnTransformation','el-icon-s-flag')">取TopN操作</p>
                  </el-col>
                  <el-col :span="2">
                    <p @mousedown="dragIt('UNION ALL操作','UnionTransformation','el-icon-s-operation')">UNION ALL操作</p>
                  </el-col>
                </el-row>
              </div>

              <!-- DAG-Diagram主体 -->
              <DAGBoard
                ref="dag"
                class="dag-board"
                :data-all="DataAll"
                style="height: 600px"
                @updateDAG="updateDAG"
                @set_json_content="set_json_content"
              />

              <!-- 用来模拟拖拽添加的元素 -->
              <node-bus
                v-if="dragBus"
                :value="busValue.value"
                :pos_x="busValue.pos_x"
                :pos_y="busValue.pos_y"
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="28" style="background: #fff;height: 50px;margin-top: 5px;padding-top: 7px" type="flex" justify="end">
          <el-col :span="1.5">
            <el-button style="height: 40px" size="medium" @click="handleDrawerClose(0)">取消</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button disabled style="height: 40px" size="medium" type="primary" @click="submitForm('ruleForm')" :loading="submitButtonLoading">保存</el-button>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
    <el-dialog
      title="请输入key:value"
      :visible.sync="dialogVisible"
      width="35%"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <el-link type="primary" style="margin-top: -30px" href="https://nightlies.apache.org/flink/flink-docs-release-1.13/docs/deployment/config/" target="_blank">Flink Config参考</el-link>
      <el-input
        v-model="params"
        type="textarea"
        :rows="8"
        resize="none"
        placeholder="example=>  deDup.source.all:false"
        @input="checkInputContent"
      />
      <p style="margin-bottom: -30px;color: red">{{ warnMes }}</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelParams">取 消</el-button>
        <el-button type="primary" @click="saveParams">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { saveJob, updateJob } from '@/api/job'
import { validateJobName } from '../js/validator'

export default {
  name: 'RealtimeJob',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    fullJobInfo: {
      type: Object,
      default: function() {
        return { 'jobId': 0, 'jobName': '', 'jobLevel': '4', 'jobDesc': '', 'isMonitor': '1', 'webJson': { 'edges': [], 'nodes': [] }, 'dagJson': { 'transformationWrappers': [], 'optionConfigs': '' }}
      }
    }
  },
  data() {
    return {
      options: {
        enableBasicAutocompletion: true,
        enableSnippets: true,
        enableLiveAutocompletion: true/* 自动补全 */
      },
      clockOfAnimation: null, // 动画播放计时器
      currentAnimate: 0, // 当前动画播放到第几帧
      maxAnimateFrames: 0,
      animationArr: [],
      dragBus: false,
      busValue: {
        value: 'name',
        pos_x: 100,
        pos_y: 100
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      DataAll: { 'edges': [], 'nodes': [] },
      jobPartInfo: { 'id': 0, 'jobName': '', 'jobDesc': '', 'isMonitor': '1', 'jobLevel': '0', 'departmentLabel': '数据平台', 'webJson': '', 'dagJson': '' },
      ruleForm: {
        jobName: 'stephen-lin-test-01',
        jobLevel: '4',
        jobDesc: 'this is a test',
        isMonitor: '1',
        params: ''
      },
      rules: {
        jobName: [
          { required: true, message: '请输入作业名称', trigger: 'blur' },
          { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' },
          { validator: validateJobName, trigger: ['blur'] }
        ],
        jobLevel: [
          { required: true, message: '请选择作业级别', trigger: 'change' }
        ],
        jobDesc: [
          { required: true, message: '请输入作业描述信息', trigger: 'blur' },
          { min: 1, max: 85, message: '长度在 1 到 85 个字符', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      realParams: '',
      params: '',
      warnMes: '',
      collapseList: ['0'],
      submitButtonLoading: false
    }
  },
  computed: {
    titleShowTag() {
      return this.fullJobInfo.jobId === 0 ? '创建作业' : '修改作业'
    },
    tableDataCompute() {
      const computeTableData = []
      const paramArr = this.realParams.replace(/\r\n/g, ',').replace(/\n/g, ',').split(',')
      paramArr.forEach(item => {
        const itemArr = item.split(':')
        computeTableData.push({ 'key': itemArr[0], 'value': itemArr[1] })
      })
      return computeTableData
    },
    tag() {
      return this.isShow
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.DataAll = JSON.parse(this.fullJobInfo.webJson)
        this.ruleForm.jobName = this.fullJobInfo.jobName
        this.ruleForm.jobLevel = this.fullJobInfo.jobLevel
        this.ruleForm.jobDesc = this.fullJobInfo.jobDesc
        this.ruleForm.isMonitor = this.fullJobInfo.isMonitor
        this.params = JSON.parse(this.fullJobInfo.dagJson).optionConfigs
        this.realParams = JSON.parse(this.fullJobInfo.dagJson).optionConfigs
        this.collapseList = ['1']

        let i = 100
        this.DataAll.nodes.forEach(item => {
          if (item.id > i) {
            i = item.id
          }
        })
        sessionStorage.setItem('nodeIdDefine', i + '')
      }
    }
  },
  mounted() {
  },
  created() {
    sessionStorage['svgScale'] = 1.5
  },
  methods: {
    set_json_content(id) {
      this.$emit('set_json_content', id, this.DataAll)
    },
    handleDrawerClose(tag) {
      this.$emit('close', tag)
      this.$refs['ruleForm'].resetFields()
    },
    updateDAG(data, actionName) {
      // DAG-Board更新 data是最新的数据,  actionName是事件的名称
      this.DataAll = data
    },
    dragIt(val, type, iconClassName) {
      sessionStorage.setItem('nodeIdDefine', (parseInt(sessionStorage.getItem('nodeIdDefine')) + 1) + '')
      sessionStorage['dragDes'] = JSON.stringify({
        drag: true,
        name: val,
        type: type,
        iconClassName: iconClassName
      })
    },
    startNodesBus(e) {
      /**
         *  别的组件调用时, 先放入缓存
         * dragDes: {
         *    drag: true,
         *    name: 组件名称
         *    type: 组件类型
         *    model_id: 跟后台交互使用
         * }
         **/
      let dragDes = null
      if (sessionStorage['dragDes']) {
        dragDes = JSON.parse(sessionStorage['dragDes'])
      }
      if (dragDes && dragDes.drag) {
        const x = e.pageX
        const y = e.pageY
        this.busValue = Object.assign({}, this.busValue, {
          pos_x: x,
          pos_y: y,
          value: dragDes.name
        })
        this.dragBus = true
      }
    },
    moveNodesBus(e) {
      // 移动模拟节点
      if (this.dragBus) {
        const x = e.pageX
        const y = e.pageY
        this.busValue = Object.assign({}, this.busValue, {
          pos_x: x,
          pos_y: y
        })
      }
    },
    endNodesBus(e) {
      // 节点放入svg
      let dragDes = null
      if (sessionStorage['dragDes']) {
        dragDes = JSON.parse(sessionStorage['dragDes'])
      }
      if (dragDes && dragDes.drag && e.toElement.id === 'svgContent') {
        const { type, iconClassName } = dragDes
        const pos_x =
            (e.offsetX - 90 - (sessionStorage['svg_left'] || 0)) /
            (sessionStorage['svgScale'] || 1) // 参数修正
        const pos_y =
            (e.offsetY - 15 - (sessionStorage['svg_top'] || 0)) /
            (sessionStorage['svgScale'] || 1) // 参数修正
        const params = {
          model_id: sessionStorage['newGraph'],
          desp: {
            type,
            pos_x,
            pos_y,
            name: this.busValue.value,
            iconClassName: iconClassName
          }
        }
        this.DataAll.nodes.push({
          ...params.desp,
          id: parseInt(sessionStorage.getItem('nodeIdDefine')) + 1,
          in_ports: [0],
          out_ports: [0],
          json_content: '',
          is_edit: '0'
        })
      }
      window.sessionStorage['dragDes'] = null
      this.dragBus = false
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // const returnMes = this.checkDataAllIsFullEdit()
          // if (returnMes !== '0') {
          //   console.log(returnMes)
          //   this.$message({
          //     type: 'error',
          //     message: returnMes
          //   })
          // } else {
          //   this.saveJob()
          // }
          this.saveJob()
        } else {
          return false
        }
      })
    },
    saveJob() {
      const resultJson = []
      this.DataAll.nodes.forEach(node => {
        const temp = {
          id: node.id,
          name: node.type,
          transformation: node.json_content.transformation,
          inputIds: []
        }
        if (node.type === 'JoinTransformation' || node.type === 'UnionTransformation') {
          temp.inputIds.push(node.json_content.leftNumber)
          temp.inputIds.push(node.json_content.rightNumber)
        } else {
          this.DataAll.edges.forEach(edge => {
            if (edge.dst_node_id === node.id) {
              temp.inputIds.push(edge.src_node_id)
            }
          })
        }
        resultJson.push(temp)
      })

      const resultJsonFinal = { 'transformationWrappers': resultJson, 'optionConfigs': this.realParams }

      this.jobPartInfo.id = this.fullJobInfo.jobId
      this.jobPartInfo.jobName = this.ruleForm.jobName
      this.jobPartInfo.jobLevel = this.ruleForm.jobLevel
      this.jobPartInfo.jobDesc = this.ruleForm.jobDesc
      this.jobPartInfo.isMonitor = parseInt(this.ruleForm.isMonitor)
      this.jobPartInfo.webJson = this.DataAll
      this.jobPartInfo.dagJson = resultJsonFinal

      this.submitButtonLoading = true
      if (this.jobPartInfo.id === 0) {
        saveJob(JSON.stringify(this.jobPartInfo)).then(res => {
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '创建成功!'
            })
            this.clearData()
            this.handleDrawerClose(1)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
          this.submitButtonLoading = false
        })
      }

      if (this.jobPartInfo.id !== 0) {
        updateJob(JSON.stringify(this.jobPartInfo)).then(res => {
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '更新成功!'
            })
            this.clearData()
            this.handleDrawerClose(1)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
          this.submitButtonLoading = false
        })
      }
    },
    clearData() {
      this.ruleForm.jobName = ''
      this.ruleForm.jobLevel = '4'
      this.ruleForm.jobDesc = ''
      this.ruleForm.isMonitor = '1'
      this.DataAll = { 'edges': [], 'nodes': [] }
      this.params = ''
      this.realParams = ''
    },
    checkDataAllIsFullEdit() {
      let isSourceTransformationNodeAppear = '0'
      let isSinkTransformationNodeAppear = '0'
      let isFullEdit = '1'
      let isSourceTransformationOutEdge = '0'
      let isSinkTransformationInEdge = '0'
      let isSourceTransformationInEdge = '0'
      let isSinkTransformationOutEdge = '0'
      let groupByTransformationInEdges = 0
      let calcTransformationInEdges = 0
      let lookupJoinTransformationInEdges = 0
      let topnTransformationInEdges = 0
      let joinTransformationInEdges = 0
      let groupByTransformationNodes = 0
      let calcTransformationNodes = 0
      let lookupJoinTransformationNodes = 0
      let topnTransformationNodes = 0
      let joinTransformationNodes = 0

      this.DataAll.nodes.forEach(item => {
        console.log(item.is_edit)
        if (item.type === 'SourceTransformation') {
          isSourceTransformationNodeAppear = '1'
          this.DataAll.edges.forEach(ii => {
            if (ii.src_node_id === item.id) {
              isSourceTransformationOutEdge = '1'
            }
            if (ii.dst_node_id === item.id) {
              isSourceTransformationInEdge = '1'
            }
          })
        }
        if (item.type === 'SinkTransformation') {
          isSinkTransformationNodeAppear = '1'
          this.DataAll.edges.forEach(ii => {
            if (ii.dst_node_id === item.id) {
              isSinkTransformationInEdge = '1'
            }
            if (ii.src_node_id === item.id) {
              isSinkTransformationOutEdge = '1'
            }
          })
        }
        if (item.type === 'GroupByTransformation' || item.type === 'CalcTransformation' || item.type === 'LookupJoinTransformation' || item.type === 'TopnTransformation' || item.type === 'JoinTransformation') {
          if (item.type === 'GroupByTransformation') {
            groupByTransformationNodes++
          }
          if (item.type === 'CalcTransformation') {
            calcTransformationNodes++
          }
          if (item.type === 'LookupJoinTransformation') {
            lookupJoinTransformationNodes++
          }
          if (item.type === 'TopnTransformation') {
            topnTransformationNodes++
          }
          if (item.type === 'JoinTransformation') {
            joinTransformationNodes++
          }
          this.DataAll.edges.forEach(ii => {
            if (ii.dst_node_id === item.id) {
              if (item.type === 'GroupByTransformation') {
                groupByTransformationInEdges++
              }
              if (item.type === 'CalcTransformation') {
                calcTransformationInEdges++
              }
              if (item.type === 'LookupJoinTransformation') {
                lookupJoinTransformationInEdges++
              }
              if (item.type === 'TopnTransformation') {
                topnTransformationInEdges++
              }
              if (item.type === 'JoinTransformation') {
                joinTransformationInEdges++
              }
            }
          })
        }
        if (item.is_edit === '0') {
          isFullEdit = '0'
        }
      })

      if (isSourceTransformationNodeAppear === '0' || isSinkTransformationNodeAppear === '0') {
        return '拖拽面板至少有一个数据源组件和一个结果表组件！'
      }

      if (isFullEdit === '0') {
        return '请将拖拽面板里面组件的内容填写完毕，可根据组件颜色判断，白色即未填写完整的！'
      }

      if (isSourceTransformationOutEdge === '0') {
        return '数据源组件至少一个出边！'
      }

      if (isSinkTransformationInEdge === '0') {
        return '结果表组件至少一个入边！'
      }

      if (isSourceTransformationInEdge === '1') {
        return '数据源组件不能有入边！'
      }

      if (isSinkTransformationOutEdge === '1') {
        return '结果表组件不能有出边！'
      }

      if (groupByTransformationNodes > 0) {
        if (groupByTransformationInEdges / groupByTransformationNodes > 1) {
          return '聚合操作组件只能有1个入边！'
        }
      }

      if (calcTransformationNodes > 0) {
        if (calcTransformationInEdges / calcTransformationNodes > 1) {
          return '过滤操作组件只能有1个入边！'
        }
      }

      if (lookupJoinTransformationNodes > 0) {
        if (lookupJoinTransformationInEdges / lookupJoinTransformationNodes > 1) {
          return '维表join操作组件只能有1个入边！'
        }
      }

      if (topnTransformationNodes > 0) {
        if (topnTransformationInEdges / topnTransformationNodes > 1) {
          return 'topN操作组件只能有1个入边！'
        }
      }

      if (joinTransformationNodes > 0) {
        if (joinTransformationInEdges / joinTransformationNodes > 2) {
          return '双流join操作组件只能有2个入边！'
        }
      }

      return '0'
    },
    openParamsEdit() {
      this.params = this.realParams
      this.dialogVisible = true
    },
    saveParams() {
      this.realParams = this.params
      this.dialogVisible = false
    },
    cancelParams() {
      this.dialogVisible = false
    },
    checkInputContent() {
      const arr = this.params.replace(/\r\n/g, ',').replace(/\n/g, ',').split(',')
      try {
        arr.forEach((item, index) => {
          if (item !== '') {
            if (!item.includes(':')) {
              this.warnMes = 'Value值不能为空 --- 第' + (index + 1) + '行'
              throw new Error('false')
            } else {
              const tempArr = item.split(':')
              if (tempArr.length === 2 && tempArr[1] === '') {
                this.warnMes = 'Value值不能为空 --- 第' + (index + 1) + '行'
                throw new Error('false')
              } else {
                this.warnMes = ''
              }
            }
          }
        })
        // eslint-disable-next-line no-empty
      } catch (e) {
      }
    },
    doCopy() {
      this.$copyText(this.realParams).then(() => {
        this.$message({
          type: 'success',
          message: '复制成功!'
        })
      })
    },
    close() {
      this.$emit('close', 0)
      this.$refs['ruleForm'].resetFields()
    }
  }

}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-job-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    padding: 11px;
  }

  /*.element-job-info{*/
  /*  background-color: #fff;*/
  /*  height: 50px;*/
  /*  padding-top: 7px;*/
  /*}*/

  .page-content {
    user-select: none;
    background-size: 50px 50px;
    background-image: linear-gradient(0deg, transparent 24%, rgba(255, 255, 255, 0.05) 25%, rgba(255, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(255, 255, 255, 0.05) 75%, rgba(255, 255, 255, 0.05) 76%, transparent 77%, transparent), linear-gradient(90deg, transparent 24%, rgba(255, 255, 255, 0.05) 25%, rgba(255, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(255, 255, 255, 0.05) 75%, rgba(255, 255, 255, 0.05) 76%, transparent 77%, transparent);
    background-color: rgb(60, 60, 60) !important;
  }

  .headbar {
    background-color: rgb(240, 242, 245);
    width: 100%;
    height: 50px;
  }

  .headbar p {
    height: 45px;
    width: 130px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
    line-height: 45px;
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    margin-top: 1px;
    background-size: 50px 50px;
    color: white;
    background-color: rgb(60, 60, 60) !important;
  }

</style>
