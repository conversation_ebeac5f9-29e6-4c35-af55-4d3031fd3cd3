<template>
  <div class="app-container">
    <el-dialog
      title="已关联topic"
      :visible.sync="tag"
      width="50%"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :before-close="handleClose"
      top="10vh"
    >

      <div style="margin-top: -30px;text-align: right">
        <el-button style="font-size: medium" type="text" class="el-icon-document-copy" size="medium" @click="doCopy">复制到剪切板</el-button>
      </div>

      <el-input
        v-model="topics"
        type="textarea"
        resize="none"
        :rows="8"
        readonly
        style="font-size: medium"
      />

    </el-dialog>
  </div>
</template>

<script>
import { getConnectTopics } from '@/api/attribute'

export default {
  name: 'AttributeConnect',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    attributeId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      topics: ''
    }
  },
  computed: {
    tag() {
      return this.isShow
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        getConnectTopics(this.attributeId).then(res => {
          console.log(res)
          if (res.code === '200') {
            let tempTopics = ''
            res.data.forEach(item => {
              tempTopics =  tempTopics + 'Topic：' + item.table_name + ' ===== 集群名称：' + item.data_source_name + '\n'
            })
            this.topics = tempTopics
          }
        })
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    doCopy() {
      this.$copyText(this.topics).then(() => {
        this.$message({
          type: 'success',
          message: '复制成功!'
        })
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

