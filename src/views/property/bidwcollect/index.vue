<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />

      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        width="500px"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          size="small"
          label-width="80px"
        >

        </el-form>
        <div
          slot="footer"
          class="dialog-footer"
        >
          <el-button
            type="text"
            @click="crud.cancelCU"
          >
            取消
          </el-button>
          <el-button
            :loading="crud.status.cu === 2"
            type="primary"
            @click="crud.submitCU"
          >
            确认
          </el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="crud.data"
        size="small"
        style="width: 100%;"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column
          prop="domain1"
          label="一级主题域"
        />
        <el-table-column
          prop="domain2"
          label="二级主题域"
        />
        <el-table-column
          prop="add_cnt"
          label="增量资产数"
        />
        <el-table-column
          prop="add_invalid_cnt"
          label="增量无效资产数"
        />
        <el-table-column
          prop="add_consumer_cnt"
          label="增量资产下游数"
        />
        <el-table-column
          prop="add_consumer_cnt"
          label="增量资产平均下游数量"
        />
        <el-table-column
          prop="history_cnt"
          label="存量资产数"
        />
        <el-table-column
          prop="history_invalid_cnt"
          label="存量无效资产数"
        />
        <el-table-column
          prop="history_consumer_cnt"
          label="存量资产下游数"
        />
        <el-table-column
          prop="history_avg_consumer_cnt"
          label="存量资产平均下游数"
        />

        <el-table-column
          v-if="checkPer(['admin','reDataSourceMetadata:edit','reDataSourceMetadata:del'])"
          label="操作"
          width="150px"
          align="center"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import bi from '@/api/bi'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, uniqueKey: null, createTime: null, modifyTime: null, tableName: null, tableDesc: null, dataSize: null, dataNum: null, reDataSourceId: null, createBy: null, dept: null, business: null }

export default {
  name: 'ReDataSourceMetadata',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['业务域', '数据源名称', '数据源类型', '数据源使用位置'],
  cruds() {
    return CRUD({ title: '表管理', url: 'api/bidwcollect', idField: 'id', sort: 'id,desc', crudMethod: { ...bi }})
  },
  data() {
    return {
      // 部门
      business_list_1: [],
      business_1: null,
      // 人员
      business_list_2: [],
      business_2: null,
      // 禁用
      disabled: true,
      permission: {
        add: ['admin', 'reDataSourceMetadata:add'],
        edit: ['admin', 'reDataSourceMetadata:edit'],
        del: ['admin', 'reDataSourceMetadata:del']
      },
      rules: {
        tableName: [
          { required: true, message: '表名不能为空', trigger: 'blur' }
        ],
        reDataSourceId: [
          { required: true, message: '数据源不能为空', trigger: 'blur' }
        ]
      }}
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
