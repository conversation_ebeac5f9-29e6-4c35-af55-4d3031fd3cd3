import request from '@/utils/request'


export function check(id) {
  return request({
    url: 'api/teJobInfo/validator/' +  id,
    method: 'put'
  })
}

export function deploy(id) {
  return request({
    url: 'api/teJobInfo/deploy/' +  id,
    method: 'put'
  })
}

export function deployWithLatestCheckpoint(id) {
  return request({
    url: 'api/teJobInfo/deployWithLatestCheckpoint/' +  id,
    method: 'put'
  })
}

export function stop(id) {
  return request({
    url: 'api/teJobInfo/stop/' +  id,
    method: 'put'
  })
}

export function getTableList() {
  return request({
    url: 'api/table/service/inner/tablelist?usePlace=1',
    method: 'get'
  })
}


export function add(data) {
  const new_data = deal_data(data)
  return request({
    url: 'api/teJobInfo',
    method: 'post',
    data: new_data
  })
}


function deal_data(data) {
  const jobName = data.jobName
  const moduleName = data.moduleName
  const isMonitor = data.isMonitor
  const id = data.id
  const s = {'id': id, 'dagJson': data, 'jobDesc': '模板平台作业', 'jobName': jobName, 'type': moduleName, 'isMonitor':isMonitor}
  const str = JSON.stringify(s)
  return str
}

export function del(ids) {
  return request({
    url: 'api/teJobInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  const new_data = deal_data(data)
  return request({
    url: 'api/teJobInfo',
    method: 'put',
    data: new_data
  })
}

export default {add, edit, del, deploy, deployWithLatestCheckpoint, stop, getTableList,check}
