<template>
  <div class="app-container">
    <el-dialog
      title="转化过滤配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px;margin-top: -20px">
        <el-form ref="ruleFormDragCalc" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item label="选择字段" prop="selectExpression">
            <el-input
              v-model="ruleForm.selectExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> id,age,name  如果选择所有字段，可以填写 *"
              clearable
            />
          </el-form-item>
          <el-form-item label="过滤条件">
            <el-input
              v-model="ruleForm.filterExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> date='20220824' and ht='22'  如果没有过滤条件可以什么都不填"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DragCalc',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleForm: {
        selectExpression: '',
        filterExpression: ''
      },
      rules: {
        selectExpression: [
          { required: true, message: '请输入选择字段', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'CalcTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        const tempExtend = JSON.parse(this.extend)
        this.ruleForm.selectExpression = tempExtend.selectExpressionOrigin === undefined ? '' : tempExtend.selectExpressionOrigin
        this.ruleForm.filterExpression = tempExtend.filterExpressionOrigin === undefined ? '' : tempExtend.filterExpressionOrigin
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragCalc'].validate((valid) => {
          const transformation = { 'selectExpression': '', 'filterExpression': '', 'selectExpressionOrigin': '', 'filterExpressionOrigin': '' }
          transformation.selectExpression = this.ruleForm.selectExpression.trim().replace(/\n/g, '')
          transformation.filterExpression = this.ruleForm.filterExpression.trim().replace(/\n/g, ' ')
          transformation.selectExpressionOrigin = this.ruleForm.selectExpression.trim()
          transformation.filterExpressionOrigin = this.ruleForm.filterExpression.trim()
          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragCalc'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.$refs['ruleFormDragCalc'].resetFields()
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

