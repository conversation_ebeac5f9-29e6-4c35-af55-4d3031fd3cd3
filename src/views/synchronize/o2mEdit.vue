<template>
  <div class="app-container">
    <el-dialog
      :title="titleShowTag"
      :visible="tag"
      width="60%"
      :close-on-click-modal="false"
      :show-close="true"
      :before-close="handleClose"
      :modal-append-to-body="false"
      top="10vh"
    >

      <div class="main-div">
        <el-form ref="ruleForm" :rules="rules" :model="ruleForm" label-width="120px" style="margin-top: -20px">

          <el-form-item label="任务名称" prop="jobName">
            <el-input v-model="ruleForm.jobName" placeholder="请输入任务名称" :disabled="ruleForm.jobId !== 0" />
          </el-form-item>
          <el-form-item label="任务描述" prop="jobDesc">
            <el-input v-model="ruleForm.jobDesc" placeholder="请输入任务描述" />
          </el-form-item>
          <el-form-item label="数据源表" prop="sourceTableId">
            <div class="format_div">
              <el-select
                v-model="ruleForm.sourceTableId"
                filterable
                remote
                clearable
                @change="changeItemSource"
                reserve-keyword
                placeholder="请输入数据源，支持kafka、rocketmq、odps"
                style="width: 800px;"
                :remote-method="remoteMethod"
                :loading="iloading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
<!--              <el-button type="info" plain @click="showTableName(ruleForm.sourceTableId)">显示数据源表名</el-button>-->
            </div>
          </el-form-item>
          <el-form-item label="kafka topic" prop="sinkTableId">
            <div class="format_div">
              <el-select
                v-model="ruleForm.sinkTableId"
                filterable
                remote
                clearable
                @change="changeItemSink"
                reserve-keyword
                placeholder="请输入kafka topic"
                style="width: 800px;"
                :remote-method="remoteMethod"
                :loading="iloading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
<!--              <el-button type="info" plain @click="showTableName(ruleForm.sinkTableId)">显示topic名称</el-button>-->
            </div>
          </el-form-item>
          <!--          <el-form-item label="du_all表">-->
          <!--            <el-input v-model="ruleForm.sinkTableId" placeholder="请输入数仓表" clearable />-->
          <!--          </el-form-item>-->
          <el-form-item label="追溯时间" prop="relativeStartTs">
            <el-input v-model="ruleForm.relativeStartTs" placeholder="填day-x或hour-x或minute-1 (day-1表示从昨天0点启动，hour-1表示当前时间前一个小时启动，不填写则从当前时间启动)" />
          </el-form-item>
          <el-form-item label="业务域">
            <el-select v-model="ruleForm.departmentLabel" placeholder="请选择业务域">
              <el-option
                v-for="item in departmentOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-row gutter="2">
            <el-col :span="18">
              <el-form-item label="负责人">
                <el-tag
                  v-for="tag in ruleForm.dynamicOwnerTags"
                  :key="tag"
                  closable
                  size="medium"
                  :disable-transitions="false"
                  @close="handleCloseTag(tag)"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-if="ownerInputVisible"
                  ref="saveTagInput"
                  v-model="ownerInput"
                  class="input-new-tag"
                  size="small"
                  placeholder="请填写域名，eg：xiaoming01"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                />
                <el-button class="button-new-tag" size="small" @click="showInput">+ 负责人</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="4" style="margin-left: 10px">
              <div v-show="isInsert" class="col-content element-remark">
                <el-tooltip placement="bottom">
                  <div slot="content">
                    输入域名 (拼音, eg: wangxiaoming0405)
                  </div>
                  <div class="el-icon-info" />
                </el-tooltip>
              </div>
            </el-col>
          </el-row>
          <el-form-item label="高级参数">
            <el-input v-model="ruleForm.optionConfigs" type="textarea" :rows="2" placeholder="作业高级参数,格式： key1:value1,key2:value2" clearable autosize="true"/>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitButtonLoading" @click="saveJob">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveJob,
  updateJob,
  get_table_detail_Info_by_ids,
  getTableInfoCommonSql,
  createOdpsTable
} from '@/api/job'

import { validateJobName } from '../realtime/js/validator'

export default {
  name: 'O2mEdit',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    editJson: {
      type: Object,
      default: function() {
        return { 'jobId': 0, 'jobName': '', 'jobDesc': '', 'departmentLabel': '交易', 'libraOwners': '', 'dagJson': {}}
      }
    }

  },
  data() {
    return {
      iloading: false,
      options: [],
      departmentOptions: [{
        value: '交易',
        label: '交易'
      }, {
        value: '社区',
        label: '社区'
      }, {
        value: '用户',
        label: '用户'
      }, {
        value: '增长',
        label: '增长'
      }, {
        value: '测试',
        label: '测试'
      }, {
        value: '客服',
        label: '客服'
      }, {
        value: '汇金',
        label: '汇金'
      }, {
        value: '算法',
        label: '算法'
      }, {
        value: '供应链',
        label: '供应链'
      }, {
        value: '监控',
        label: '监控'
      }, {
        value: '稳定生产',
        label: '稳定生产'
      }],
      ownerInputVisible: false,
      ownerInput: '',

      ruleForm: {
        jobId: 0,
        jobLevel: '3',
        type: 'SYNC_KAFKA_TEMPLATE',
        moduleName: 'SYNC_KAFKA_TEMPLATE',
        jobName: '',
        jobDesc: '',
        sourceTableId: '',
        sourceTableIds: [],
        sinkTableId: '',
        sinkTableIds: [],
        relativeStartTs: '',
        departmentLabel: '交易',
        dynamicOwnerTags: [],
        optionConfigs: '',
        dagJson: ''
      },

      sourceTableName: '',
      sinkTableName: '',

      odpsForm: {
        odpsTableName: '',
        odpsTableDesc: ''
      },
      rules: {
        jobName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' },
          { validator: validateJobName, trigger: ['blur'] }
        ],
        jobDesc: [
          { required: true, message: '请输入任务描述', trigger: 'blur' }
        ],
        sourceTableId: [
          { required: true, message: '请输入topic', trigger: 'blur' }
        ],
        sinkTableId: [
          { required: true, message: '请输入odps表', trigger: 'blur' }
        ],
        relativeStartTs: [
          { required: true, message: '请输入追溯时间', trigger: 'blur' }
        ],
        odpsTableName: [
          { required: true, message: '请输入odps表名称', trigger: 'blur' }
        ],
        odpsTableDesc: [
          { required: true, message: '请输入odps表描述', trigger: 'blur' }
        ]

      },
      submitButtonLoading: false

    }
  },
  computed: {
    tag() {
      return this.isShow
    },
    titleShowTag() {
      return this.isInsert ? '作业新增' : '作业修改'
    },
    isInsert() {
      return this.ruleForm.jobId === 0
    }

  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.ruleForm.jobId = this.editJson.jobId
        this.ruleForm.jobName = this.editJson.jobName
        this.ruleForm.jobDesc = this.editJson.jobDesc
        this.ruleForm.departmentLabel = this.editJson.departmentLabel

        if (this.editJson.libraOwners !== '') {
          this.ruleForm.dynamicOwnerTags = this.editJson.libraOwners.split(',')
        }

        if (this.ruleForm.jobId !== 0) {
          const dagJson = JSON.parse(this.editJson.dagJson)
          this.ruleForm.sourceTableIds = dagJson.sourceTableIds
          this.ruleForm.sourceTableId = this.ruleForm.sourceTableIds[0]
          this.ruleForm.sinkTableIds = dagJson.sinkTableIds
          this.ruleForm.sinkTableId = this.ruleForm.sinkTableIds[0]
          this.ruleForm.relativeStartTs = dagJson.relativeStartTs
          this.ruleForm.optionConfigs = dagJson.optionConfigs

          this.showTableName(this.ruleForm.sourceTableId, 'source')
          this.showTableName(this.ruleForm.sinkTableId, 'sink')
        } else {
          this.ruleForm.sourceTableIds = []
          this.ruleForm.sourceTableId = ''
          this.ruleForm.sinkTableIds = []
          this.ruleForm.sinkTableId = ''
          this.ruleForm.relativeStartTs = ''
          this.ruleForm.optionConfigs = ''
        }
      }
    }
  },
  mounted() {
  },
  created() {
  },
  methods: {
    onClose(tag) {
      this.$refs['ruleForm'].resetFields()
      this.$emit('close', tag)
    },
    submitOdpsForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const param = { 'tableName': this.odpsForm.odpsTableName, 'tableDesc': this.odpsForm.odpsTableDesc }
          this.createOdpsLoading = true
          createOdpsTable(param).then(res => {
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: this.odpsForm.odpsTableName + '表创建成功~'
              })
              this.createOdpsLoading = false
              this.oneClickCreateFlag = false
            }
            if (res.code === '500') {
              this.createOdpsLoading = false
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        } else {
          return false
        }
      })
    },
    cancelOdpsForm(formName) {
      this.$refs[formName].resetFields()
      this.oneClickCreateFlag = false
    },
    handleClose() {
      this.onClose('0')
    },
    remoteMethod(query) {
      if (query !== '') {
        this.iloading = true
        const pathParams = []
        pathParams.push({ 'paramName': 'tableName', 'paramValue': query })
        getTableInfoCommonSql(pathParams).then(res => {
          if (res.code === '200') {
            this.list = res.data.map(item => {
              return { value: item.tableId, label: item.tableName + '(所属实例:' + item.dataSourceName + ')' }
            })
            this.iloading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1
            })
            console.log(this.options)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      } else {
        this.options = []
      }
    },
    showTableName(tableId, flag) {
      if (tableId !== '') {
        console.log(tableId)
        const tableIdList = []
        tableIdList.push(tableId)
        get_table_detail_Info_by_ids(tableIdList).then(res => {
          if (res.code === '200') {
            this.options = res.data.map(item => {
              if (flag === 'source') {
                this.sourceTableName = item.tableName
              } else {
                this.sinkTableName = item.tableName
              }
              return { value: item.tableId, label: item.tableName + '(所属实例:' + item.dataSourceName + ')' }
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }
    },
    handleCloseTag(tag) {
      this.ruleForm.dynamicOwnerTags.splice(this.ruleForm.dynamicOwnerTags.indexOf(tag), 1)
    },
    showInput() {
      this.ownerInputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      const inputValue = this.ownerInput
      const regex = /^[a-z0-9_]+$/
      if (inputValue !== '') {
        if (inputValue.match(regex)) {
          if (!this.ruleForm.dynamicOwnerTags.includes(inputValue)) {
            this.ruleForm.dynamicOwnerTags.push(inputValue)
          }
        } else {
          this.$message({
            type: 'warning',
            message: '请输入域名,eg: xiaoming01'
          })
        }
      }
      this.ownerInputVisible = false
      this.ownerInput = ''
    },
    checkLibraOwners() {
      return this.ruleForm.dynamicOwnerTags.length !== 0
    },
    saveJob() {
      const validateRes = this.validateJobConfig()
      if (validateRes !== undefined) {
        this.$message({
          message: validateRes,
          type: 'warning'
        })
      } else {
        const finalJson = this.prepareFinalJson()
        console.log(JSON.stringify(finalJson))
        this.submitButtonLoading = true
        if (finalJson.id === 0) {
          saveJob(JSON.stringify(finalJson)).then(res => {
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '创建成功!'
              })
              this.submitButtonLoading = false
              this.$emit('close', '1')
            }
            if (res.code === '500') {
              this.submitButtonLoading = false
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        } else {
          updateJob(JSON.stringify(finalJson)).then(res => {
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '更新成功!'
              })
              this.submitButtonLoading = false
              this.$emit('close', '1')
            }
            if (res.code === '500') {
              this.submitButtonLoading = false
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        }
      }
    },
    validateJobConfig() {
      const jobName = this.ruleForm.jobName
      const jobDesc = this.ruleForm.jobDesc
      const sourceTableId = this.ruleForm.sourceTableId
      const sinkTableId = this.ruleForm.sinkTableId
      const relativeStartTs = this.ruleForm.relativeStartTs
      const flag = this.checkLibraOwners()

      if (jobName === '') {
        return '请填写任务名称！'
      }

      if (jobDesc === '') {
        return '请填写任务描述！'
      }

      if (sourceTableId === '') {
        return '请选择topic！'
      }

      if (sinkTableId === '') {
        return '请选择数仓表！'
      }

      if (this.sourceTableName === this.sinkTableName) {
        return 'kafka topic名称和数据源表名称不能相同，请换一个kafka topic！'
      }

      if (relativeStartTs === '') {
        return '请填写追溯时间！'
      }

      if (flag === false) {
        return "请填写负责人's"
      }
    },
    prepareFinalJson() {
      const tem_sourceTableIds = []
      const tem_sinkTableIds = []

      tem_sourceTableIds.push(this.ruleForm.sourceTableId)
      tem_sinkTableIds.push(this.ruleForm.sinkTableId)

      this.ruleForm.sourceTableIds = tem_sourceTableIds
      this.ruleForm.sinkTableIds = tem_sinkTableIds

      // 拼接最终Json
      const finalJson = {}
      finalJson.id = this.ruleForm.jobId
      finalJson.jobName = this.ruleForm.jobName
      finalJson.jobDesc = this.ruleForm.jobDesc
      finalJson.departmentLabel = this.ruleForm.departmentLabel
      finalJson.type = this.ruleForm.type
      finalJson.jobLevel = parseInt(this.ruleForm.jobLevel)
      finalJson.libraOwners = this.ruleForm.dynamicOwnerTags.join(',')

      const finalDagJson = {}
      finalDagJson.moduleName = this.ruleForm.moduleName
      finalDagJson.sourceTableIds = this.ruleForm.sourceTableIds
      finalDagJson.sinkTableIds = this.ruleForm.sinkTableIds
      finalDagJson.relativeStartTs = this.ruleForm.relativeStartTs
      finalDagJson.optionConfigs = this.ruleForm.optionConfigs
      finalDagJson.cacheTTL = 600000

      finalJson.dagJson = JSON.stringify(finalDagJson)

      return finalJson
    },
    changeItemSource(item) {
      const selectedOption = this.options.find(option => option.value === item)
      this.selectedLabel = selectedOption ? selectedOption.label : ''
      this.sourceTableName = this.selectedLabel.split('(')[0]
    },
    changeItemSink(item) {
      const selectedOption = this.options.find(option => option.value === item)
      this.selectedLabel = selectedOption ? selectedOption.label : ''
      this.sinkTableName = this.selectedLabel.split('(')[0]
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

.el-row {
  margin-bottom: 1px;
  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

.grid-content {
  border-radius: 2px;
  min-height: 180px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.format_div {
  display: flex;
}

.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 1px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 220px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
