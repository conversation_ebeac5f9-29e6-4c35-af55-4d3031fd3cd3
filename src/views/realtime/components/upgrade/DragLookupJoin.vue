<template>
  <div class="app-container">
    <el-dialog
      title="维表join操作配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px;margin-top: -20px">
        <el-form ref="ruleFormDragLookupJoin" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item label="关联类型">
            <el-radio v-model="ruleForm.joinType" label="left" border>left</el-radio>
            <el-radio v-model="ruleForm.joinType" label="inner" border>inner</el-radio>
          </el-form-item>
          <el-form-item label="维表选择" prop="dimTableId">
            <el-select
              id="sourceSelector"
              v-model="ruleForm.dimTableId"
              filterable
              remote
              clearable
              reserve-keyword
              no-data-text="无匹配维表"
              placeholder="请搜索维表..."
              style="width: 620px"
              :remote-method="remoteMethod"
              :loading="iLoading"
              @change="selectChange"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="预览字段">
            <div style="display: flex;align-items: flex-start;">
              <el-input
                v-model="fieldsCheck"
                type="textarea"
                :rows="1"
                autosize
                readonly
                resize="none"
                style="margin-right: 3px"
                placeholder="预览该维表的全部字段..."
              />
              <el-button type="info" @click="generateFields">{{ fieldsCheckShow?'一键预览':'取消预览' }}</el-button>
            </div>
          </el-form-item>
          <el-form-item label="关联条件" prop="joinConditionExpression">
            <el-input
              v-model="ruleForm.joinConditionExpression"
              placeholder="example=> tl.user_id = tr.userid  注意别名: 左流用tl，维表用tr"
              clearable
            />
          </el-form-item>
          <el-form-item label="选择字段" prop="selectExpression">
            <el-input
              v-model="ruleForm.selectExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> tl.*, tr.country  注意别名: 左流用tl，维表用tr，如果全选可以用*"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDataSourceTypeByTableId,
  getTableFields,
  getTableInfoCommonSql
} from '@/api/job'

export default {
  name: 'DragLookupJoin',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      iLoading: false,

      connector: '',
      options: [],

      fieldsCheck: '',
      fieldsCheckShow: true,

      ruleForm: {
        selectExpression: '',
        joinType: 'left',
        joinConditionExpression: '',
        dimTableId: ''
      },
      rules: {
        dimTableId: [
          { required: true, message: '请选择维表', trigger: 'change' }
        ],
        joinConditionExpression: [
          { required: true, message: '请选择关联条件', trigger: 'blur' }
        ],
        selectExpression: [
          { required: true, message: '请选择字段', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'LookupJoinTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        const tempExtend = JSON.parse(this.extend)

        this.ruleForm.selectExpression = tempExtend.selectExpressionOrigin === undefined ? '' : tempExtend.selectExpressionOrigin
        this.ruleForm.joinConditionExpression = tempExtend.joinConditionExpression === undefined ? '' : tempExtend.joinConditionExpression
        this.ruleForm.joinType = tempExtend.joinType === undefined ? 'left' : tempExtend.joinType

        if (tempExtend.connectConfig !== undefined) {
          this.connector = tempExtend.connectConfig.connector
          this.ruleForm.dimTableId = tempExtend.connectConfig.tableId

          this.showTableName(this.ruleForm.dimTableId)
        } else {
          this.connector = ''
          this.ruleForm.dimTableId = ''
        }
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragLookupJoin'].validate((valid) => {
          this.fieldsCheckShow = true
          this.fieldsCheck = ''

          const transformation = { 'selectExpression': '', 'joinConditionExpression': '', 'joinType': '', 'leftViewAlias': 'tl', 'rightViewAlias': 'tr', 'connectConfig': { 'connector': '', 'tableId': '' }, 'selectExpressionOrigin': '' }

          transformation.selectExpression = this.ruleForm.selectExpression.trim().replace(/\n/g, '')
          transformation.selectExpressionOrigin = this.ruleForm.selectExpression.trim()
          transformation.joinType = this.ruleForm.joinType
          transformation.joinConditionExpression = this.ruleForm.joinConditionExpression
          transformation.connectConfig.connector = this.connector
          transformation.connectConfig.tableId = this.ruleForm.dimTableId

          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragLookupJoin'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.fieldsCheckShow = true
        this.fieldsCheck = ''
        this.$refs['ruleFormDragLookupJoin'].resetFields()
      }
    },
    generateFields() {
      if (this.fieldsCheckShow === true) {
        if (this.ruleForm.dimTableId !== '') {
          getTableFields(this.ruleForm.dimTableId).then(res => {
            if (res.code === '200') {
              const fieldsArr = []
              res.data.forEach(item => {
                fieldsArr.push(item.fieldName + '    ' + item.dataType.toLowerCase())
              })
              this.fieldsCheck = fieldsArr.join(',\n')
            }
          })
          this.fieldsCheckShow = !this.fieldsCheckShow
        } else {
          this.$message({
            type: 'info',
            message: '先选择维表'
          })
        }
      } else {
        this.fieldsCheck = ''
        this.fieldsCheckShow = !this.fieldsCheckShow
      }
    },
    remoteMethod(query) {
      if (query !== '') {
        this.iLoading = true
        const pathParams = []
        pathParams.push({ 'paramName': 'tableName', 'paramValue': query })
        getTableInfoCommonSql(pathParams).then(res => {
          if (res.code === '200') {
            this.list = res.data.map(item => {
              return { value: item.tableId, label: item.tableName + '(所属实例:' + item.dataSourceName + ')' }
            })
            this.iLoading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      } else {
        this.options = []
      }
    },
    selectChange() {
      if (this.ruleForm.dimTableId !== '') {
        getDataSourceTypeByTableId(this.ruleForm.dimTableId).then(res => {
          if (res.code === '200') {
            this.connector = res.data
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }
    },
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

