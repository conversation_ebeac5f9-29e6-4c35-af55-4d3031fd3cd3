<template>
  <div v-show="tag" class="drawer_container">
    <el-dialog
      title="请输入key:value"
      :visible.sync="configDialogShow"
      width="35%"
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :show-close="false"
    >
      <el-link type="primary" style="margin-top: -30px" href="https://nightlies.apache.org/flink/flink-docs-release-1.13/docs/deployment/config/" target="_blank">Flink Config参考</el-link>
      <el-input
        v-model="configParam"
        type="textarea"
        :rows="8"
        resize="none"
        placeholder="example=>  deDup.source.all:false"
        @input="checkInputContent"
      />
      <p style="margin-bottom: -30px;color: red">{{ warnMes }}</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelConfigParams">取 消</el-button>
        <el-button type="primary" @click="saveConfigParams">确 定</el-button>
      </span>
    </el-dialog>

    <div class="drawer_wrap">
      <el-row>
        <el-col>
          <div>
            <el-button type="text" class="el-icon-edit" size="medium" @click="openConfigParamsEdit">打开文本编辑</el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" class="el-icon-document-copy" size="medium" @click="doCopy">复制到剪切板</el-button>
          </div>
          <el-table
            :data="tableDataCompute"
            style="width: 100%"
            empty-text="暂无配置内容"
          >
            <el-table-column
              prop="key"
              label="KEY"
              width="100"
            />
            <el-table-column
              prop="value"
              label="VALUE"
              width="300"
            />
          </el-table>
        </el-col>
      </el-row>
    </div>

  </div>
</template>

<script>
export default {
  name: 'RightDrawer',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    superConfigParam: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      configDialogShow: false,
      configParam: '',
      realConfigParam: '',
      warnMes: '',
      gridTypeList: [
        {
          label: '四边网格',
          value: 'mesh'
        },
        {
          label: '点状网格',
          value: 'dot'
        }
      ],
      showGrid: true,
      drawerEdge: {
        EdgeText: '',
        edgeWidth: null,
        edgeColor: ''
      }
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.realConfigParam = this.superConfigParam
      } else {
        this.$emit('close', this.realConfigParam)
      }
    }
  },
  computed: {
    tableDataCompute() {
      const computeTableData = []
      if (this.realConfigParam !== '') {
        const paramArr = this.realConfigParam.replace(/\r\n/g, ',').replace(/\n/g, ',').split(',')
        paramArr.forEach(item => {
          const itemArr = item.split(':')
          computeTableData.push({ 'key': itemArr[0], 'value': itemArr[1] })
        })
      }
      return computeTableData
    },
    tag() {
      return this.isShow
    }
  },
  created() {},
  mounted() {},
  methods: {
    openConfigParamsEdit() {
      this.configParam = this.realConfigParam
      this.configDialogShow = true
    },
    saveConfigParams() {
      this.realConfigParam = this.configParam
      this.configDialogShow = false
    },
    cancelConfigParams() {
      this.configDialogShow = false
    },
    checkInputContent() {
      const arr = this.configParam.replace(/\r\n/g, ',').replace(/\n/g, ',').split(',')
      try {
        arr.forEach((item, index) => {
          if (item !== '') {
            if (!item.includes(':')) {
              this.warnMes = 'Value值不能为空 --- 第' + (index + 1) + '行'
              throw new Error('false')
            } else {
              const tempArr = item.split(':')
              if (tempArr.length === 2 && tempArr[1] === '') {
                this.warnMes = 'Value值不能为空 --- 第' + (index + 1) + '行'
                throw new Error('false')
              } else {
                this.warnMes = ''
              }
            }
          }
        })
        // eslint-disable-next-line no-empty
      } catch (e) {
      }
    },
    doCopy() {
      this.$copyText(this.realConfigParam).then(() => {
        this.$message({
          type: 'success',
          message: '复制成功!'
        })
      })
    },
    close() {
      this.$emit('close', 0)
      this.$refs['ruleForm'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer_container {
  overflow: scroll;
  border-left: 1px solid #eee;
  box-shadow: 1px 1px 4px 0 #0a0a0a2e;
  width: 1000px;

  .drawer_wrap {
    box-sizing: border-box;
    padding: 20px 10px 20px 20px;
  }
}
</style>
