<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">输入表名称</label>
        <el-input
          v-model="query.tableName"
          clearable
          placeholder="表名称"
          style="width: 185px;height: 23px;margin-right: 50px"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">选择数据源</label>

        <el-select
          v-model="query.reDataSourceId"
          filterable
          placeholder="数据源"
          clearable
          size="small"
          style="width: 185px;margin-right: 50px"
        >
          <el-option
            v-for="item in dict.数据源名称"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>

        <label class="el-form-item-label">表状态</label>
        <el-select
          v-model="query.isDelete"
          filterable
          placeholder="表状态"
          clearable
          size="small"
          style="width: 155px;margin-right: 50px"
        >
          <el-option
            v-for="item1 in dict.是否废弃"
            :key="item1.id"
            :label="item1.label"
            :value="item1.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>


        <label class="el-form-item-label">是否管控</label>
        <el-select
          v-model="query.isControl"
          filterable
          placeholder="是否管控"
          clearable
          size="small"
          style="width: 155px;margin-right: 50px"
        >
          <el-option
            v-for="item in dict.是否管控"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>

        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />

      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        width="500px"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          size="small"
          label-width="80px"
        >
          <el-form-item
            label="表名"
            prop="tableName"
          >
            <el-input
              v-model="form.tableName"
              :disabled="form.id"
              style="width: 370px;"
            />
          </el-form-item>
          <el-form-item label="表描述">
            <el-input
              v-model="form.tableDesc"
              style="width: 370px;"
            />
          </el-form-item>
          <el-form-item
            label="数据源"
            prop="reDataSourceId"
          >
            <el-select
              v-model="form.reDataSourceId"
              style="width: 370px;"
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in dict.数据源名称"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="唯一键">
            <el-input
              v-model="form.uniqueKey"
              placeholder="当为Kafka表时,需要填写,不填默认为id,多字段英文逗号分割"
              style="width: 370px;"
            />
          </el-form-item>

          <el-form-item label="分区字段">
            <el-input
              v-model="form.partitionKey"
              placeholder="当为odps表时需要填写分区字段,多字段英文逗号分割"
              style="width: 370px;"
            />
          </el-form-item>

          <el-form-item label="列族名称">
            <el-input
              v-model="form.columnFamily"
              placeholder="当为hbase表时需要填写列族名称"
              style="width: 370px;"
            />
          </el-form-item>
          <el-form-item
            label="状态"
            prop="isDelete"
          >
            <el-select
              v-model="form.isDelete"
              style="width: 370px;"
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in dict.是否废弃"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

          </el-form-item>

            <el-form-item
              label="管控"
              prop="isDelete"
            >
            <el-select
              v-model="form.isControl"
              style="width: 370px;"
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in dict.是否管控"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            </el-form-item>

        </el-form>
        <div
          slot="footer"
          class="dialog-footer"
        >
          <el-button
            type="text"
            @click="crud.cancelCU"
          >
            取消
          </el-button>
          <el-button
            :loading="crud.status.cu === 2"
            type="primary"
            @click="crud.submitCU"
          >
            确认
          </el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="crud.data"
        size="small"
        style="width: 100%;"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column
          prop="id"
          label="自增id"
        />
        <el-table-column
          prop="tableName"
          label="表名"
        />
        <el-table-column
          prop="tableDesc"
          label="表描述"
        />
        <el-table-column
          prop="uniqueKey"
          label="唯一键"
        />
        <el-table-column
          prop="partitionKey"
          label="分区字段"
        />
        <el-table-column
          prop="columnFamily"
          label="列族名称"
        />

        <el-table-column
          prop="dataSourceName"
          label="数据源名称"
        />
        <el-table-column
          prop="reDataSourceId"
          label="数据源id"
        />
        <el-table-column
          prop="dataSourceType"
          label="数据源类型"
        >
          <template slot-scope="scope">
            {{ dict.label.数据源类型[scope.row.dataSourceType] }}
          </template>
        </el-table-column>

        <el-table-column
          prop="partitionNum"
          label="分区数量"
        />
        <el-table-column
          prop="dataNum"
          label="数据条数"
        />
        <el-table-column
          prop="dataSize"
          label="数据容量"
        />
        <el-table-column
          prop="dataTtl"
          label="生命周期"
        />
        <el-table-column
          prop="usePlace"
          label="资产类型"
        >
          <template slot-scope="scope">
            {{ dict.label.数据源使用位置[scope.row.usePlace] }}
          </template>
        </el-table-column>

        <el-table-column
          prop="createTime"
          label="创建时间"
        />
        <el-table-column
          prop="topic_creator"
          label="负责人"
        />

        <el-table-column
          prop="isDelete"
          label="状态"
        >
          <template slot-scope="scope">
            {{ dict.label.是否废弃[scope.row.isDelete] }}
          </template>
        </el-table-column>

        <el-table-column
          prop="isControl"
          label="是否管控"
        >
          <template slot-scope="scope">
            {{ dict.label.是否管控[scope.row.isControl] }}
          </template>
        </el-table-column>


        <el-table-column v-if="checkPer(['admin','reDataSourceMetadata:sync'])" label="操作" width="125px" align="left">
          <template slot-scope="scope">
            <el-button v-permission="['admin','reDataSourceMetadata:sync']" type="primary" plain size="mini" @click="editFiled(scope.row.id)">表字段管理</el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','reDataSourceMetadata:sync'])" label="操作" width="125px" align="left">
          <template slot-scope="scope">
            <el-button v-permission="['admin','reDataSourceMetadata:sync']" :loading="isSyncLoading===true && syncId===scope.row.id" type="info" plain size="mini" @click="sync(scope.row.id)">手动同步字段</el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','reDataSourceMetadata:edit','reDataSourceMetadata:del'])" label="" width="125px" align="left">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>

      </el-table>
      <!--分页组件-->
      <pagination />
    </div>

    <field :is-show="isShow" :edit-table-id="editTableId" @close="closeFieldComponent" />
  </div>
</template>

<script>
import crudReDataSourceMetadata from '@/api/reDataSourceMetadata'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import field from './field'

const defaultForm = { id: null, uniqueKey: null, createTime: null, modifyTime: null, tableName: null, tableDesc: null, dataSize: null, dataNum: null, reDataSourceId: null, createBy: null, dept: null, business: null }

export default {
  name: 'ReDataSourceMetadata',
  components: { pagination, crudOperation, rrOperation, udOperation, field },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['业务域', '数据源名称', '数据源类型', '数据源使用位置','是否废弃','是否管控'],
  cruds() {
    return CRUD({ title: '表管理', url: 'api/reDataSourceMetadata', idField: 'id', sort: 'id,desc', crudMethod: { ...crudReDataSourceMetadata }})
  },
  data() {
    return {
      // 禁用
      disabled: true,
      permission: {
        add: ['admin', 'reDataSourceMetadata:add'],
        edit: ['admin', 'reDataSourceMetadata:edit'],
        del: ['admin', 'reDataSourceMetadata:del'],
        sync: ['admin', 'reDataSourceMetadata:sync']
      },
      rules: {
        tableName: [
          { required: true, message: '表名不能为空', trigger: 'blur' }
        ],
        reDataSourceId: [
          { required: true, message: '数据源不能为空', trigger: 'blur' }
        ]
      },
      isShow: false,
      editTableId: 0,
      isSyncLoading: false,
      syncId: 0
    }
  },
  created() {
  },
  mounted() {
    this.getbusinesslist()
  },
  methods: {
    sync(id) {
      this.isSyncLoading = true
      this.syncId = id
      crudReDataSourceMetadata.sync(id).then(res => {
        this.$message({
          type: 'success',
          message: res
        })
        this.isSyncLoading = false
      }).catch(err => {
        this.isSyncLoading = false
        console.log(err.response.data.message)
      })
    },
    editFiled(id) {
      this.isShow = true
      this.editTableId = id
      sessionStorage.setItem('editField', id)
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    closeFieldComponent() {
      this.isShow = false
    }
  }
}
</script>

<style scoped>

</style>
