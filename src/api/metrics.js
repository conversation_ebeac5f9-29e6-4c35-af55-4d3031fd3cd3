import request from '@/utils/request'

// 获取所有的公共属性
export function getAllCommonAttributes() {
  return request({
    url: 'api/metrics/commonAttributes',
    method: 'get'
  })
}

// 指标明细分页查询
export function queryMetricDetailPageable(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/metrics/metricDetail/queryPageable' + pathParam + '&sort=modifyTime%2Cdesc',
    method: 'get'
  })
}

// 新增作业明细
export function createMetricDetail(param) {
  return request({
    url: 'api/metrics/createMetricDetail',
    method: 'post',
    data: param
  })
}

// 修改指标明细
export function updateMetricDetail(param) {
  return request({
    url: 'api/metrics/updateMetricDetail',
    method: 'post',
    data: param
  })
}

// 上线指标明细
export function onlineMetricDetail(metricDetailId) {
  return request({
    url: 'api/metrics/onlineMetricDetail/' + metricDetailId,
    method: 'get'
  })
}

// 指标组分页查询
export function queryMetricGroupPageable(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/metrics/metricGroup/queryPageable' + pathParam + '&sort=modifyTime%2Cdesc',
    method: 'get'
  })
}

// 新增指标组
export function createMetricGroup(param) {
  return request({
    url: 'api/metrics/createMetricGroup',
    method: 'post',
    data: param
  })
}

// 修改指标组
export function updateMetricGroup(param) {
  return request({
    url: 'api/metrics/updateMetricGroup',
    method: 'post',
    data: param
  })
}




// 测试权限
export function testPermission() {
  return request({
    url: 'api/metrics/testPermission',
    method: 'get'
  })
}
