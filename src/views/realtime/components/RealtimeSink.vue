<template>
  <div class="app-container">
    <el-dialog
      title="结果表配置"
      :visible.sync="tag"
      width="57%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-sink-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>选择结果表:</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="col-content element-source">
              <el-cascader
                v-model="selectData"
                placeholder="试试搜索：kafka"
                :options="options"
                clearable
                filterable
                size="medium"
                @change="cascaderChange()"
                style="width: 100%"
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" v-show="isPrimaryKeyShow===1" style="margin-bottom: 30px;">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>填写主键:</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="col-content element-primarykey">
              <el-input
                v-model="primaryKeyInput"
                placeholder="请输入主键字段"
                clearable
              />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="col-content element-remark">
              <el-tooltip placement="bottom">
                <div slot="content">
                  主键格式： user_id 或 user_id,date <br><br>
                  主键作用： 分区字段[kafka等] 或 更新字段[rds等]
                </div>
                <div class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getSourceAndTheirTable, getSourceTypeNameMapping } from '@/api/job'
import { getDataSourceTypeName, getTableNameByTableId } from '../js/common'

export default {
  name: 'RealtimeSink',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectData: [],
      options: [],
      primaryKeyInput: '',
      showOperation: [],
      isPrimaryKeyShow: 1
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'SinkTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'SinkTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.selectData = JSON.parse(JSON.parse(sessionStorage.getItem('editJsonContent')).selectData)
            this.primaryKeyInput = JSON.parse(sessionStorage.getItem('editJsonContent')).primaryKeyInput
            this.cascaderChange()
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
    console.log('---RealtimeSink---mounted----executed----')
    const sourceOptionArray = []
    getSourceAndTheirTable().then(res => {
      if (res.code === '200') {
        const tableIdAndTableNameArr = []
        res.data.forEach(resItem => {
          // if (resItem.sourceType === 1 || resItem.sourceType === 2 || resItem.sourceType === 3 || resItem.sourceType === 4 || resItem.sourceType === 8 || resItem.sourceType === 9) {
          //   const sourceOptionItem = {}
          //   sourceOptionItem.value = resItem.sourceName
          //   sourceOptionItem.label = resItem.sourceName
          //   sourceOptionItem.children = resItem.sourceTable.map(item => ({ value: item.tableId, label: item.tableName }))
          //   sourceOptionArray.push(sourceOptionItem)
          //
          //   resItem.sourceTable.forEach(tableItem => {
          //     tableIdAndTableNameArr.push({ 'tableId': tableItem.tableId, 'tableName': tableItem.tableName })
          //   })
          // }
          const sourceOptionItem = {}
          sourceOptionItem.value = resItem.sourceName
          sourceOptionItem.label = resItem.sourceName
          sourceOptionItem.children = resItem.sourceTable.map(item => ({ value: item.tableId, label: item.tableName }))
          sourceOptionArray.push(sourceOptionItem)

          resItem.sourceTable.forEach(tableItem => {
            tableIdAndTableNameArr.push({ 'tableId': tableItem.tableId, 'tableName': tableItem.tableName })
          })
        })
        this.options = sourceOptionArray
        console.log(sourceOptionArray)
        sessionStorage.setItem('tableIdAndTableNameArrSink', JSON.stringify(tableIdAndTableNameArr))
      }
    })

    getSourceTypeNameMapping().then(res => {
      if (res.code === '200') {
        sessionStorage.setItem('sourceTypeNameMapping', JSON.stringify(res.data))
      }
    })
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'selectData': '', 'primaryKeyInput': '', 'transformation': '', 'showOperation': '' }

      const transformation = { 'connectConfig': { 'connector': '', 'tableId': '' }, 'schemaConfig': { 'primaryKey': '' }}
      transformation.connectConfig.connector = getDataSourceTypeName(this.selectData[0])
      transformation.connectConfig.tableId = this.selectData[1]
      transformation.schemaConfig.primaryKey = this.primaryKeyInput.trim()

      resultJson.selectData = JSON.stringify(this.selectData)
      resultJson.primaryKeyInput = this.primaryKeyInput.trim()
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      const resultTable = this.selectData[0] === undefined ? '' : this.selectData[0] + '/' + getTableNameByTableId(this.selectData[1], 'SinkTransformation')
      this.showOperation.push('【选择的结果表】' + resultTable)
      this.showOperation.push('【结果表主键】' + this.primaryKeyInput.trim())
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)
      this.clearData()
    },
    clearData() {
      this.selectData = []
      this.primaryKeyInput = ''
    },
    isFullEdit() {
      if (this.selectData.length === 0 || (this.primaryKeyInput.trim() === '') && !this.selectData[0].includes('odps')) {
        return '0'
      } else {
        return '1'
      }
    },
    cascaderChange(){
      const sourceName = this.selectData[0]
      if(sourceName.includes('odps')){
        this.isPrimaryKeyShow = 0
      }else{
        if(this.isPrimaryKeyShow===0){
          this.isPrimaryKeyShow = 1
        }
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-sink-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

  .element-primarykey{
    line-height: 30px;
  }

  .element-remark{
    line-height: 30px;
    margin-top: 1px;
  }

</style>

