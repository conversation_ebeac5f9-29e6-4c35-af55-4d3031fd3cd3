<template>
  <div class="app-container">
    <el-dialog
      title="TopN操作配置"
      :visible.sync="tag"
      width="57%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-topN-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="4">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>选择字段:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="selectExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> username,gender,age,row_num  如需获取排序结果，使用row_num即可；如需选择所有字段，可以填写 *"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="4">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>分区字段:</span>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="col-content element-value">
              <el-input
                v-model="partitionExpression"
                placeholder="example=> username,gender"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="4">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>排序字段:</span>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="col-content element-value">
              <el-input
                v-model="orderByExpression"
                placeholder="example=> age"
                clearable
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="col-content element-value" style="text-align: right">
              <el-radio v-model="order" label="ASC" border>ASC</el-radio>
              <el-radio v-model="order" label="DESC" border>DESC</el-radio>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="4">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>Top数量:</span>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="col-content element-value">
              <el-input-number v-model="topN" :min="1" size="small"></el-input-number>
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RealtimeTopN',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectExpression: '',
      partitionExpression: '',
      orderByExpression: '',
      order: 'ASC',
      topN: 1,
      groupByExpression: '',
      showOperation: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'TopnTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'TopnTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.selectExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).selectExpression
            this.partitionExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).partitionExpression
            this.orderByExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).orderByExpression
            this.order = JSON.parse(sessionStorage.getItem('editJsonContent')).order
            this.topN = JSON.parse(sessionStorage.getItem('editJsonContent')).topN
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'selectExpression': '', 'partitionExpression': '', 'orderByExpression': '', 'order': '', 'topN': 1, 'transformation': '', 'showOperation': '' }

      const transformation = { 'selectExpression': '', 'partitionExpression': '', 'orderByExpression': '', 'n': 1 }
      transformation.selectExpression = this.selectExpression.trim().replace(/\n/g, '')
      transformation.partitionExpression = this.partitionExpression.trim()
      transformation.orderByExpression = this.orderByExpression.trim() + ' ' + this.order
      transformation.n = this.topN

      resultJson.selectExpression = this.selectExpression.trim()
      resultJson.partitionExpression = this.partitionExpression.trim()
      resultJson.orderByExpression = this.orderByExpression.trim()
      resultJson.order = this.order
      resultJson.topN = this.topN
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      this.showOperation.push('【选择字段】' + this.selectExpression.trim().replace(/\n/g, ''))
      this.showOperation.push('【分区字段】' + this.partitionExpression.trim())
      this.showOperation.push('【排序字段】' + this.orderByExpression.trim())
      this.showOperation.push('【排序规则】' + this.order)
      this.showOperation.push('【topN之N】' + this.topN)
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)
      this.clearData()
    },
    clearData() {
      this.selectExpression = ''
      this.partitionExpression = ''
      this.orderByExpression = ''
      this.order = 'ASC'
      this.topN = 1
    },
    isFullEdit() {
      if (this.selectExpression.trim() === '' || this.partitionExpression.trim() === '' || this.orderByExpression === '') {
        return '0'
      } else {
        return '1'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-topN-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

</style>

