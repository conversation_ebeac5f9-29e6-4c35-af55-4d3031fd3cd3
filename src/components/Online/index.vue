<template>
  <div class="app-container">
    <el-dialog
      title="作业上线"
      :visible.sync="tag"
      width="57%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div class="realtime-calc-dialog-content">
        <el-form ref="form" :model="form" label-width="100px">
          <el-form-item label="作业名称：">
            {{form.jobName}}
          </el-form-item>
          <el-form-item label="启动方式：">
            <el-radio-group v-model="form.startType">
              <el-radio label="0">从作业配置启动位点</el-radio>
              <el-radio :disabled="this.isHasLatestCheckpoint === '0'" label="1">从最新checkpoint</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取消</el-button>
        <el-button type="primary" @click="onClose('1')">确定上线</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'OnlineJob',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    isHasLatestCheckpoint: {
      type: String,
      default: '0'
    },
    onlineJobName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        jobName: '',
        startType: '0'
      }
    }
  },
  computed: {
    tag() {
      return this.isShow
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      this.form.jobName = this.onlineJobName
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(tag) {
      this.$emit('close',tag,this.form.startType)
      this.clearData()
    },
    clearData() {
      this.form.startType = '0'
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-calc-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 10px;
    height: 100px;
  }

</style>

