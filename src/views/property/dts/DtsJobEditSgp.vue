<template>
  <div class="app-container">
    <el-dialog
      title="创建任务"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="false"
      :visible.sync="tag"
      width="50%"
      top="10vh"
    >
      <el-form ref="ruleForm" :rules="rules" :model="ruleForm" label-width="120px" style="margin-top: -20px;margin-bottom: -30px">
        <el-form-item label="名称：" prop="itemName">
          <el-input :disabled="ruleForm.id !== 0" v-model="ruleForm.itemName" placeholder="请输入任务名称" clearable />
        </el-form-item>
        <el-form-item label="数据源实例名：" prop="sourceName">
          <el-input :disabled="ruleForm.id !== 0" v-model="ruleForm.sourceName" placeholder="请输入数据源实例名：格式（rm-uf6ihxii1u4s98ev7）" clearable />
        </el-form-item>
        <el-form-item label="库名：" prop="database">
          <div class="flexDiv">
            <el-input :disabled="ruleForm.id !== 0" v-model="ruleForm.database" placeholder="请输入库名" clearable />
            <el-switch
              style="margin-left: 20px;width: 200px"
              v-model="ruleForm.isDatabaseSplit"
              active-text="分库"
              inactive-text="不分库">
            </el-switch>
          </div>
        </el-form-item>
        <el-form-item label="表名：" prop="table">
          <div class="flexDiv">
            <el-input :disabled="ruleForm.id !== 0" v-model="ruleForm.table" placeholder="请输入表名" clearable />
            <el-switch
              style="margin-left: 20px;width: 200px"
              v-model="ruleForm.isTableSplit"
              active-text="分表"
              inactive-text="不分表">
            </el-switch>
          </div>
        </el-form-item>
        <el-form-item label="校验库表权限：">
          <el-button type="info" :disabled="ruleForm.sourceName === '' || ruleForm.database === '' || ruleForm.table === ''" @click="validateTableRegex(ruleForm.sourceName,ruleForm.database,ruleForm.table,ruleForm.isDatabaseSplit,ruleForm.isTableSplit)">校验</el-button>
        </el-form-item>
        <!--        <el-form-item label="逻辑表正则：" prop="tableRegex">-->
        <!--          <div class="flexDiv">-->
        <!--            <el-input :disabled="ruleForm.id !== 0" v-model="ruleForm.tableRegex" placeholder="请输入逻辑表正则" clearable />-->
        <!--            <el-button type="info" :disabled="ruleForm.sourceName === '' || ruleForm.tableRegex === ''" @click="validateTableRegex(ruleForm.sourceName,ruleForm.tableRegex)">校验</el-button>-->
        <!--          </div>-->
        <!--        </el-form-item>-->
        <el-form-item label="topic：" prop="topic">
          <el-row gutter="2">
            <el-col :span="18">
              <el-input :disabled="ruleForm.id !== 0" v-model="ruleForm.topic" placeholder="请输入topic" clearable />
            </el-col>
            <el-col :span="4" style="margin-left: 10px">
              <div class="col-content element-remark">
                <el-tooltip placement="bottom">
                  <div slot="content">
                    topic格式建议：ods_库名_表名
                  </div>
                  <div class="el-icon-info" />
                </el-tooltip>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="分区数：">
          <el-input-number v-model="ruleForm.partition" :min="3" :max="10"></el-input-number>
        </el-form-item>
        <!--        <el-form-item label="分区字段：">-->
        <!--          <el-input disabled v-model="ruleForm.partitionKey" placeholder="请输入分区字段" clearable />-->
        <!--        </el-form-item>-->
      </el-form>
      <!--      <el-button @click="test">tet</el-button>-->
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelForm('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')" :loading="submitButtonLoading">确 定</el-button>
      </span>
      <el-dialog title="库表检查结果" :visible.sync="validateTableRegexResultDialog" :close-on-click-modal="false" :modal-append-to-body="true" 和 :append-to-body="true" width="50%" top="10vh">
        <div style="margin-top: -50px">
          <el-divider/>
          <div style="height: auto;background:#E1F3D8;padding: 10px 20px;border: 1px dotted;line-height: 1.5;margin-bottom: 23px">
            <b>如果数据源A或B不存在按照以下步骤检查</b>:<br/>
            1. 请检查是否库表拼写错误, 示例：单表 foo\.bar 分库分表 foo_\d+\.bar_\d+ 2. 该库表无对应的账号权限, 测试环境账号为du_otter_test, 生产环境账号为du_otter, 请在onedba平台进行申请
          </div>
          <el-table :data="validateTableRegexData" style="margin-top: 1px" :header-cell-style="tableHeaderColor">
            <el-table-column type="index" label="序号" width="100px"/>
            <el-table-column property="data" label="库表"/>
          </el-table>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
import { checkTableRegexSgp, createJobSgp } from '@/api/dts'
import {validateTopicNameOversea} from './validator'

export default {
  name: 'DtsEditSgp',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    editJson: {
      type: Object,
      default: function() {
        return {"id":0,"taskId":0,"sourceName":'',"itemNo":"MQ_SUB_item_230315_1353_tlUY5c","itemName":"du_sns_merc库merc_order_share_snapshot表binlog订阅","tableRegex":"du_sns_merc\\\\\\\\\\\\\\\\.merc_order_share_snapshot","topic":"ods_du_sns_merc_merc_order_share_snapshot","partition":1,"partitionKey":"$pk$"}
      }
    }
  },
  data() {
    return {
      ruleForm: {
        id: 0,
        taskId: 0,
        sourceName: '',
        itemNo: '',
        itemName: '',
        tableRegex: '',
        topic: '',
        partition: 1,
        partitionKey: '$pk$',
        ignoreColumns: null,
        database: '',
        table: '',
        isDatabaseSplit: false,
        isTableSplit: false
      },
      rules: {
        itemName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        sourceName: [
          { required: true, message: '请输入数据源实例名', trigger: 'blur' }
        ],
        database: [
          { required: true, message: '请输入库名', trigger: 'blur' }
        ],
        table: [
          { required: true, message: '请输入表名', trigger: 'blur' }
        ],
        topic: [
          { required: true, message: '请输入topic', trigger: 'blur' },
          { validator: validateTopicNameOversea, trigger: ['blur'] }
        ]
      },
      options: [],
      value: [],
      list: [],
      list2: [],
      loading: false,
      submitButtonLoading: false,
      validateTableRegexResultDialog: false,
      validateTableRegexData: [{
        data: 'sns_counter.counter_content_1 SELECT'
      }, {
        data: 'sns_counter.counter_content_2 SELECT'
      }, {
        data: 'sns_counter.counter_content_3 SELECT'
      }, {
        data: 'sns_counter.counter_content_4 SELECT'
      }],
      fullscreenLoading: false,
    }
  },
  computed: {
    tag() {
      return this.isShow
    },
    editTag() {
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.ruleForm.id = this.editJson.id
        this.ruleForm.taskId = this.editJson.taskId
        this.ruleForm.sourceName = this.editJson.sourceName
        this.ruleForm.itemNo = this.editJson.itemNo
        this.ruleForm.itemName = this.editJson.itemName
        this.ruleForm.tableRegex = this.editJson.tableRegex
        this.ruleForm.topic = this.editJson.topic
        this.ruleForm.partition = this.editJson.partition
        this.ruleForm.partitionKey = this.editJson.partitionKey
      }
    }
  },
  mounted() {
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitButtonLoading = true

          const tmpParams = {'sourceName':this.ruleForm.sourceName,'itemName':this.ruleForm.itemName,'topic':this.ruleForm.topic,'partition':this.ruleForm.partition,'database':this.ruleForm.database,'table':this.ruleForm.table,'isDatabaseSplit':this.ruleForm.isDatabaseSplit,'isTableSplit':this.ruleForm.isTableSplit}

          if(this.ruleForm.id === 0 ){
            createJobSgp(tmpParams).then(res=>{
              if (res.code === '200') {
                this.$message({
                  type: 'success',
                  message: '创建成功!'
                })
                this.clearData()
                this.$emit('close', '1')
              }
              if (res.code === '500') {
                this.$message({
                  type: 'error',
                  message: res.mes
                })
              }
              this.submitButtonLoading = false
            })
          }else{
            this.$emit('close', '0')
            this.submitButtonLoading = false
          }
        }
      })
    },
    cancelForm(formName) {
      this.clearData()
      this.$refs[formName].resetFields()
      this.$emit('close', '0')
    },
    clearData(){
      this.ruleForm.id = 0,
        this.ruleForm.taskId = 0,
        this.ruleForm.sourceName = '',
        this.ruleForm.itemNo = '',
        this.ruleForm.itemName = '',
        this.ruleForm.tableRegex = '',
        this.ruleForm.topic = '',
        this.ruleForm.partition = '',
        this.ruleForm.partitionKey = '',
        this.ruleForm.database = '',
        this.ruleForm.table = '',
        this.ruleForm.isDatabaseSplit = false,
        this.ruleForm.isTableSplit = false
    },
    validateTableRegex(sourceName,database,table,isDatabaseSplit,isTableSplit){
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',

        background: 'rgba(0, 0, 0, 0.5)'
      });

      const tableRegexJson = {'sourceName':sourceName,'database':database,'table':table,'isDatabaseSplit':isDatabaseSplit,'isTableSplit':isTableSplit}

      checkTableRegexSgp(tableRegexJson).then(res=>{
        if(res.code === '200'){
          const tmpValidateTableRegexData = []
          res.data.forEach(item=>{
            tmpValidateTableRegexData.push({'data':item})
          })
          this.validateTableRegexData = tmpValidateTableRegexData
          this.validateTableRegexResultDialog = true
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        loading.close();
      })

    },
    tableHeaderColor({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0) {
        return 'background-color: #F0F0F1;color:black;font-size:14px;font-weight: 410;'
      }
    },
    test(){
      console.log(this.ruleForm.isDatabaseSplit)
      console.log(this.ruleForm.isTableSplit)
    }

  }
}
</script>

<style>
.flexDiv {
  display: flex;
}

.el-dialog__title {
  font-weight: 500;
}


</style>
