<template>
  <div class="app-container">
    <el-dialog
      title="指标明细"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="tag"
      :modal-append-to-body="false"
      width="70%"
      top="10vh"
    >
      <metric-detail ref="metricDetail" :metric-group-id="metricGroupId" style="margin-top: -50px;margin-bottom: -70px" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import MetricDetail from '../MetricDetail'

export default {
  name: 'MetricDetailDialog',
  components: {
    MetricDetail
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    metricGroupId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
    }
  },
  computed: {
    tag() {
      return this.isShow
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        this.$nextTick(() => {
          this.$refs.metricDetail.doSearchForMetricGroup()
        })
      } else {
        this.$refs.metricDetail.clearData()
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
