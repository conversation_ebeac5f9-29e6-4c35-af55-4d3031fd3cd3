<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">作业名称</label>
        <el-input
          v-model="query.jobName"
          clearable
          style="width: 300px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <br>
        <label class="el-form-item-label">查询该表的下游---该表表名</label>
        <el-input
          v-model="query.sourceTable"
          clearable
          style="width: 300px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">and 表类型</label>
        <el-input
          v-model="query.sourceDbType"
          clearable
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">and 表库名</label>
        <el-input
          v-model="query.sourceDatabase"
          clearable
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">and 表地址</label>
        <el-input
          v-model="query.sourceInstance"
          clearable
          style="width: 300px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <br>
        <label class="el-form-item-label">查询该表的上游---该表表名</label>
        <el-input
          v-model="query.targetTable"
          clearable
          style="width: 300px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">and 表类型</label>
        <el-input
          v-model="query.targetDbType"
          clearable
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">and 表库名</label>
        <el-input
          v-model="query.targetDatabase"
          clearable
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">and 表地址</label>
        <el-input
          v-model="query.targetInstance"
          clearable
          style="width: 300px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="自增id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="来源物理表" prop="sourceTable">
            <el-input v-model="form.sourceTable" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="来源视图表" prop="sourceViewTable">
            <el-input v-model="form.sourceViewTable" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="来源数据库" prop="sourceDatabase">
            <el-input v-model="form.sourceDatabase" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="来源数据库类型" prop="sourceDbType">
            <el-input v-model="form.sourceDbType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="来源实例地址" prop="creator">
            <el-input v-model="form.sourceInstance" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="目标表" prop="targetTable">
            <el-input v-model="form.targetTable" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="目标视图表" prop="targetViewTable">
            <el-input v-model="form.targetViewTable" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="目标数据库" prop="targetDatabase">
            <el-input v-model="form.targetDatabase" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="目标数据库类型" prop="targetDbType">
            <el-input v-model="form.targetDbType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="目标实例地址" prop="creator">
            <el-input v-model="form.targetInstance" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="作业名称" prop="jobName">
            <el-input v-model="form.jobName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="修改时间" prop="modifyTime">
            <el-input v-model="form.modifyTime" style="width: 370px;" />
          </el-form-item>
          <!--          <el-form-item label="作业归属人" prop="creator">-->
          <!--            <el-input v-model="form.creator" style="width: 370px;" />-->
          <!--          </el-form-item>-->
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
<!--        <el-table-column prop="id" label="自增id" />-->
        <el-table-column prop="sourceTable" label="来源物理表" />
        <el-table-column prop="sourceViewTable" label="来源视图表" />
        <el-table-column prop="sourceDatabase" label="来源数据库" />
        <el-table-column prop="sourceDbType" label="来源表类型" />
        <el-table-column prop="sourceInstance" label="来源实例地址" />
        <el-table-column prop="sourceOriginalInstance" label="原始来源实例地址" />
        <el-table-column prop="targetTable" label="目标表" />
        <el-table-column prop="targetViewTable" label="目标视图表" />
        <el-table-column prop="targetDatabase" label="目标数据库" />
        <el-table-column prop="targetDbType" label="目标表类型" />
        <el-table-column prop="targetInstance" label="目标实例地址" />
        <el-table-column prop="targetOriginalInstance" label="原始目标实例地址" />
        <el-table-column prop="jobName" label="作业名称" />
        <el-table-column prop="projectName" label="空间名称" />
        <el-table-column prop="creator" label="作业归属人" />
        <el-table-column prop="modifyTime" label="修改时间" />
        <el-table-column v-if="checkPer(['admin','flinkSqlLineage:edit','flinkSqlLineage:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudFlinkSqlLineage from '@/api/flinkSqlLineage'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, sourceTable: null, sourceViewTable: null, sourceDatabase: null, sourceDbType: null, targetTable: null, targetViewTable: null, targetDatabase: null, targetDbType: null, jobName: null, createTime: null, modifyTime: null, creator: null }
export default {
  name: 'FlinkSqlLineage',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '数据血缘', url: 'api/flink/sql/lineage', idField: 'id', sort: 'id,desc', crudMethod: { ...crudFlinkSqlLineage }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'flinkSqlLineage:add'],
        edit: ['admin', 'flinkSqlLineage:edit'],
        del: ['admin', 'flinkSqlLineage:del']
      },
      rules: {
        sourceTable: [
          { required: true, message: '来源物理表不能为空', trigger: 'blur' }
        ],
        sourceViewTable: [
          { required: true, message: '来源视图表不能为空', trigger: 'blur' }
        ],
        sourceDatabase: [
          { required: true, message: '来源数据库不能为空', trigger: 'blur' }
        ],
        sourceDbType: [
          { required: true, message: '来源数据库类型不能为空', trigger: 'blur' }
        ],
        targetTable: [
          { required: true, message: '目标表不能为空', trigger: 'blur' }
        ],
        targetViewTable: [
          { required: true, message: '目标视图表不能为空', trigger: 'blur' }
        ],
        targetDatabase: [
          { required: true, message: '目标数据库不能为空', trigger: 'blur' }
        ],
        targetDbType: [
          { required: true, message: '目标数据库类型不能为空', trigger: 'blur' }
        ],
        jobName: [
          { required: true, message: '作业名称不能为空', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' }
        ],
        modifyTime: [
          { required: true, message: '修改时间不能为空', trigger: 'blur' }
        ],
        creator: [
          { required: true, message: '作业归属人不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
