<template>
  <div class="dashboard-container">
    <div class="chart-wrapper">
      <el-row>
        <h3>项目介绍</h3>
        <ul class="project-introduction">
          <li>此项目为实时模板平台--可以通过纯FlinkSql创建实时任务，也可以通过拖拽的方式自由创建Flink实时任务，具体使用方式见参考文档</li>
        </ul>
      </el-row>
      <el-row>
        <h3>参考文档</h3>
        <ul class="project-document">
          <li><el-link type="primary" href="https://poizon.feishu.cn/wiki/wikcnFMaBrcqV66AmPtfEhf0Cyb" target="_blank"><span style="font-size: medium">模板平台使用文档</span></el-link></li>
          <li><el-link type="primary" href="https://blog.shizhuang-inc.com/article/NTgwMg==" target="_blank"><span style="font-size: medium">FlinkSql模板任务</span></el-link></li>
          <li><el-link type="primary" href="https://blog.shizhuang-inc.com/article/NjMyNQ==" target="_blank"><span style="font-size: medium">Flink拖拽模板任务</span></el-link></li>
        </ul>
      </el-row>
      <el-row>
        <h3>相关模版文档</h3>
        <ul class="project-document">
          <li><el-link type="primary" href="https://poizon.feishu.cn/docx/H4eydSzSJoxQO5xrIBKcqyEVnDc" target="_blank"><span style="font-size: medium">数据同步模版操作指南</span></el-link></li>
          <li><el-link type="primary" href="https://poizon.feishu.cn/docx/NzPYdr0CEoiQvaxSrO5cFZcXnYd" target="_blank"><span style="font-size: medium">算法在线推理模版操作指南</span></el-link></li>
        </ul>
      </el-row>
      <el-row>
        <h3>模板任务默认做p4级别保障,若为其他级别,上线前请告知模板平台管理人员去分类告警群。</h3>
        <ul class="project-problem">
          <li>DTS数据接入任务：陈禹豪</li>
          <li>FlinkSql模板任务：昭只(林智平)</li>
          <li>Flink拖拽模板/实时同步模板任务：Stephen(王林)</li>
        </ul>
      </el-row>
    </div>
  </div>
</template>

<script>

export default {
  name: 'Dashboard',
  components: {
  },
  data() {
    return {
    }
  },
  methods: {
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .dashboard-container {
    padding: 10px;
    background-color: rgb(240, 242, 245);
    /*position: relative;*/
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    /*margin-bottom: 32px;*/
    /*border-radius: 8px;*/
    height:80vh;
    border-radius: 10px
  }

  .project-introduction li {
    margin-bottom: 10px;
  }

  .project-document li {
    margin-bottom: 10px;
  }

  .project-problem li {
    margin-bottom: 10px;
  }
</style>
