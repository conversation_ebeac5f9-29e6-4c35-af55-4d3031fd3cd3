<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">作业名</label>
        <el-input v-model="query.jobName" clearable placeholder="作业名" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">作业状态</label>
        <el-select
          v-model="query.status"
          filterable
          placeholder="请选择"
          clearable
          style="width: 185px;"
          @blur="selectBlur"
          @clear="selectClear"
          @change="selectChange"
        >
          <el-option
            v-for="item in dict.新作业状态"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>
        <label class="el-form-item-label">创建人</label>
        <el-input v-model="query.createUserName" clearable placeholder="创建人" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <rrOperation :crud="crud"/>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission"/>
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0"
                 :title="crud.status.title" width="999px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="作业名" prop="jobName">
            <el-input v-model="form.jobName"  :disabled="form.id" placeholder="" style="width: 800px;"/>
          </el-form-item>
          <el-form-item label="作业描述" prop="jobName">
            <el-input v-model="form.jobDesc"   placeholder="" style="width: 800px;"/>
          </el-form-item>
          <el-form-item label="输入表" prop="sourceTableIds">
            <el-select v-model="form.sourceTableIds" filterable multiple placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.新全量表"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="sql逻辑" prop="sql">
            <el-input v-model="form.sql" :rows="20" type="textarea" style="width: 800px;"/>
          </el-form-item>

          <el-form-item label="缓存时间" prop="cacheTTL">
            <el-select v-model="form.cacheTTL" filterable placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.统计周期"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="输出表主键字段" >
            <el-input v-model="form.sinkSchemaPrimaryKey" style="width: 800px;"/>
          </el-form-item>

          <el-form-item label="启动时间" >
            <el-input v-model="form.relativeStartTs" placeholder="填day-x或hour-x或minute-1 (day-1表示从昨天0点启动，hour-1表示当前时间前一个小时启动，不填写则从当前时间启动)" style="width: 800px;"/>
          </el-form-item>

          <el-form-item label="作业参数" >
            <el-input v-model="form.optionConfigs"  :rows="3" type="textarea" placeholder="高级配置,一般不填" style="width: 800px;"/>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
          <el-button v-permission="['admin','reDataSourceMetadata:sync']" style="margin-left: -2px" type="success"
                     @click="syncs(form.sourceTableIds)">表字段同步
          </el-button>
        </div>
      </el-dialog>


      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;"
                @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55"/>
        <el-table-column prop="id" label="id"/>
        <el-table-column prop="jobName" label="作业名"/>
        <el-table-column prop="jobDesc" label="作业描述"/>
        <el-table-column prop="cacheTTL" label="缓存时间">
          <template slot-scope="scope">
            {{ dict.label.统计周期[scope.row.cacheTTL] }}
          </template>
        </el-table-column>
<!--        <el-table-column prop="status" label="作业状态">-->
<!--        </el-table-column>-->
        <el-table-column prop="createTime" label="创建时间"/>
        <el-table-column prop="ownerName" label="创建人"/>

        <el-table-column v-if="checkPer(['admin','teJobInfo:edit','teJobInfo:del'])" label="操作" width="250px"
                         align="right">
          <template slot-scope="scope">
            <el-button v-permission="['admin','teJobInfo:check']" style="margin-left: -2px" type="primary" size="mini"
                       @click="check(scope.row.id)">校验
            </el-button>
            <el-button v-permission="['admin','teJobInfo:deploy']" style="margin-left: -2px" type="primary" size="mini"
                       @click="deploy(scope.row.id)">发布
            </el-button>
            <el-button v-permission="['admin','teJobInfo:stop']" style="margin-left: -2px" type="success" size="mini"
                       @click="stop(scope.row.id)">下线
            </el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','teJobInfo:edit','teJobInfo:del'])" label="" width="150px"
                         align="left">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination/>
    </div>
  </div>
</template>

<script>
import crudnewtopn from '@/api/newtopn'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import crudReDataSourceMetadata from '@/api/reDataSourceMetadata'

const defaultForm = {
  id: null,
  jobName: null,
  moduleName: 'DynamicRuleTemplate',
  sinkTableIds: null,
  sinkSchemaPrimaryKey: null,
  sourceTableIds: null,
  sql: null,
  cacheTTL: null,
  relativeStartTs:null,
  optionConfigs:null
}
export default {
  name: 'newsql',
  components: {pagination, crudOperation, rrOperation, udOperation},
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['关联类型', '新全量表', '统计周期', '二期作业状态','新作业状态'],
  cruds() {
    return CRUD({
      title: 'sql作业模板',
      url: 'api/teJobInfo',
      idField: 'id',
      sort: 'id,desc',
      templateType: 'DynamicRuleTemplate',
      crudMethod: {...crudnewtopn}
    })
  },
  data() {
    return {
      permission: {
        add: ['admin', 'teJobInfo:add'],
        edit: ['admin', 'teJobInfo:edit'],
        del: ['admin', 'teJobInfo:del']
      },
      rules: {
        jobName: [
          {required: true, message: '作业名不能为空', trigger: 'blur'}
        ],
        sourceTableIds: [
          {required: true, message: '输入表不能为空', trigger: 'blur'}
        ],
        cacheTTL: [
          {required: true, message: '数据缓存时间不能为空', trigger: 'blur'}
        ],
        sinkSchemaPrimaryKey: [
          {required: true, message: '输出表主键不能为空', trigger: 'blur'}
        ]

      },
      queryTypeOptions: [
        {key: 'jobName', display_name: '作业名称'}
      ]
    }
  },
  methods: {
    syncs(ids) {
      crudReDataSourceMetadata.syncs(ids).then(res => {
        console.log('-------------------------------')
        console.log(ids)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    // 执行
    check(id) {
      crudnewtopn.check(id).then(res => {
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    deploy(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.deploy(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    del(data) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.del(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    stop(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.stop(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },

    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
