import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/teJobRuleExpress',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/teJobRuleExpress/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/teJobRuleExpress',
    method: 'put',
    data
  })
}

export function checkData(data) {
  return request({
    url: 'api/teJobRuleExpress/check',
    method: 'post',
    data
  })
}

export default { add, edit, del,checkData }
