<template>
  <div class="app-container">
    <el-row :gutter="5" style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px" class="element-header-tool">
      <el-col :span="3">
        <div>
          <el-button size="small" type="primary" icon="el-icon-edit" @click="addAttribute">添加属性</el-button>
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-input
            v-model="attributeNameBlurry"
            placeholder="属性名模糊查询"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-button size="small" type="success" icon="el-icon-search" @click="doSearch">搜索</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col>
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          highlight-current-row
          :header-cell-style="{textAlign: 'left'}"
          size="medium"
          style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
        >
          <el-table-column
            prop="name"
            label="属性名称"
            fixed
            sortable
            align="left"
            width="260"
          />
          <el-table-column
            prop="description"
            label="属性描述"
            align="left"
            width="260"
          />
          <el-table-column
            prop="connectNum"
            label="关联数量"
            sortable
            align="left"
            width="130"
          />
          <el-table-column
            prop="disConnectNum"
            label="未关联数量"
            sortable
            align="left"
            width="130"
          />
          <el-table-column
            prop="creator"
            label="创建人"
            sortable
            align="left"
            width="120"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            prop="operator"
            label="操作人"
            sortable
            align="left"
            width="120"
          />
          <el-table-column
            prop="updateTime"
            label="操作时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            label="相关操作"
            width="700"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                plain
                class="el-icon-edit-outline"
                @click="editAttribute(scope.row)"
              >修改</el-button>
              <el-button
                size="mini"
                type="info"
                plain
                :disabled="scope.row.connectNum === 0"
                class="el-icon-view"
                @click="showConnect(scope.row.id)"
              >已关联topic</el-button>
              <el-button
                size="mini"
                type="primary"
                plain
                :disabled="scope.row.disConnectNum === 0"
                class="el-icon-rank"
                @click="showDisConnect(scope.row.id)"
              >未关联topic</el-button>
              <el-button
                size="mini"
                type="danger"
                plain
                class="el-icon-edit-outline"
                @click="deleteAttribute(scope.row.id)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px">
      <el-col>
        <el-pagination
          background
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[10, 15, 30, 50]"
          class="page_tt"
          :total="page_info.total_num"
          :page-size="page_info.page_size"
          :current-page="page_info.current_page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
        />
      </el-col>
    </el-row>

    <attribute-edit style="position: absolute" :is-show="isShowEdit" :edit-json="editJson" @close="closeEdit" />

    <attribute-connect :is-show="isShowConnect" :attribute-id="attributeId" @close="closeConnect" />

    <attribute-dis-connect :is-show="isShowDisConnect" :attribute-id="attributeId" @close="closeDisConnect" />

  </div>
</template>
<script>
import AttributeEdit from './AttributeEdit'
import AttributeConnect from './AttributeConnect'
import AttributeDisConnect from './AttributeDisConnect'
import { queryAttributePageable, deleteAttribute } from '@/api/attribute'

export default {
  name: 'PublicAttributes',
  components: {
    AttributeDisConnect,
    AttributeEdit,
    AttributeConnect
  },
  data() {
    return {
      attributeNameBlurry: '',
      tableData: [],
      page_info: {
        total_num: 0,
        current_page: 1,
        page_size: 15
      },
      isShowEdit: false,
      isShowConnect: false,
      isShowDisConnect: false,
      editJson: { 'id': 0, 'name': '', 'description': '' },
      loading: false,
      attributeId: 0
    }
  },
  computed: {
  },
  mounted() {
  },
  created() {
    this.doSearch()
  },
  methods: {
    addAttribute() {
      this.editJson = { 'id': 0, 'name': '', 'description': '' }
      this.isShowEdit = true
    },
    deleteAttribute(id) {
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAttribute(id).then(res => {
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.doSearch()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    doSearch() {
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
      pathParams.push({ 'paramName': 'name', 'paramValue': this.attributeNameBlurry })

      queryAttributePageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })
    },
    fillTableData(data) {
      const tempTableData = []
      data.forEach(item => {
        tempTableData.push({ 'id': item.id, 'name': item.name, 'description': item.description, 'creator': item.creator, 'operator': item.operator, 'createTime': item.createTime, 'updateTime': item.updateTime, 'connectNum': item.connectNum, 'disConnectNum': item.disConnectNum })
      })
      this.tableData = tempTableData
    },
    handleSizeChange(val) {
      this.page_info.page_size = val
      this.doSearch()
    },
    handleCurrentChange(val) {
      this.page_info.current_page = val
      this.doSearch()
    },
    editAttribute(row) {
      this.editJson = { 'id': row.id, 'name': row.name, 'description': row.description }
      this.isShowEdit = true
    },
    closeEdit(val) {
      this.isShowEdit = false
      if (val === '1') {
        this.doSearch()
      }
    },
    closeConnect() {
      this.isShowConnect = false
    },
    closeDisConnect() {
      this.isShowDisConnect = false
      this.doSearch()
    },
    showConnect(id) {
      this.attributeId = id
      this.isShowConnect = true
    },
    showDisConnect(id) {
      this.attributeId = id
      this.isShowDisConnect = true
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .element-job-status-bar{
    position: absolute;
    top: 16px;
    right: 10px;
  }
  .element-job-status-bar .status-key{
    color: #dd0689
  }
  .element-job-status-bar .status-value{
    color: blueviolet
  }
</style>
