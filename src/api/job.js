import request from '@/utils/request'

// 获取数据源和对应的topic信息
export function getSourceAndTheirTable() {
  return request({
    url: 'api/dataSource/sources',
    method: 'get'
  })
}

// 根据topicId获取对应的字段信息
export function getTableFields(tableId) {
  return request({
    url: 'api/dataSource/field/' + tableId,
    method: 'get'
  })
}

// 根据topicId获取对应的数据源类型
export function getDataSourceTypeByTableId(tableId) {
  return request({
    url: 'api/dataSource/dataSourceTypeName/' + tableId,
    method: 'get'
  })
}

// 获取所有的数据源名称和数据源类型名称的映射
export function getSourceTypeNameMapping() {
  return request({
    url: 'api/dataSource/typeNameMapping',
    method: 'get'
  })
}

// 创建作业
export function saveJob(param) {
  return request({
    url: 'api/job/createJob',
    method: 'post',
    data: param
  })
}

// 批量创建作业
export function createJobBatch(param) {
  return request({
    url: 'api/job/createBatch',
    method: 'post',
    data: param
  })
}


// 作业分页查询
export function queryJobPageable(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/job/queryJobPageable' + pathParam + '&sort=updateTime%2Cdesc',
    method: 'get'
  })
}

// 修改作业
export function updateJob(param) {
  return request({
    url: 'api/job/updateJob',
    method: 'post',
    data: param
  })
}

// 删除作业
export function deleteJob(jobId) {
  const ids = []
  ids.push(jobId)
  return request({
    url: 'api/job/deleteJob',
    method: 'delete',
    data: ids
  })
}

// 上线作业（上线并运行）
export function deployJob(jobId) {
  return request({
    url: 'api/job/deploy/' + jobId,
    method: 'put'
  })
}

// 上线作业（上线并运行）- 使用最新checkpoint
export function deployJobWithLatestCheckpoint(jobId) {
  return request({
    url: 'api/job/deployWithLatestCheckpoint/' + jobId,
    method: 'put'
  })
}


// 下线作业（停止）
export function stopJob(jobId) {
  return request({
    url: 'api/job/stop/' + jobId,
    method: 'put'
  })
}

// 复制作业
export function copyJob(jobId, newJobName) {
  return request({
    url: 'api/job/copy/' + jobId + '/' + newJobName,
    method: 'put'
  })
}

// 查询作业各种状态对应的数量
export function getJobStatusNum() {
  return request({
    url: 'api/job/jobStatusNum',
    method: 'get'
  })
}

// 校验作业
export function validateJob(jobId) {
  return request({
    url: 'api/job/validate/' + jobId,
    method: 'get'
  })
}

// binlog同步到odps的任务生成odps解析sql
export function generateOdpsAnaSql(jobId) {
  return request({
    url: 'api/job/generateOdpsAnaSql/' + jobId,
    method: 'get'
  })
}

// dqc根据jobId获取作业相关信息
export function get_job_info_by_dqc(jobId) {
  return request({
    url: 'api/job/dqc/' + jobId,
    method: 'get'
  })
}

// 获取dqc的执行结果
export function get_job_dqc_res(param) {
  return request({
    url: 'api/job/dqc/execute',
    method: 'post',
    data: param
  })
}

// 获取dqc执行结果
export function get_dqc_res(jobName,sessionId) {
  return request({
    url: 'api/job/dqc/result/' + jobName + '/' + sessionId,
    method: 'get'
  })
}

// 通过sql作业数据源表查询接口
export function getTableInfoCommonSql(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/job/sourceCommonSql' + pathParam,
    method: 'get'
  })
}

// 获取libra作业的数据源信息
export function getLibraJobSourceAndDimTableInfo(jobName) {
  return request({
    url: 'api/job/libraJobDetail/' + jobName,
    method: 'get'
  })
}

// 数据演练分页查询
export function queryPreDrillPageable(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/job/preDrill/queryJobPageable' + pathParam + '&sort=modifyTime%2Cdesc',
    method: 'get'
  })
}

// 保存数据演练
export function createPreDrillJob(param) {
  return request({
    url: 'api/job/preDrill/createJob',
    method: 'post',
    data: param
  })
}

// 更新数据演练
export function updatePreDrillJob(param) {
  return request({
    url: 'api/job/preDrill/updateJob',
    method: 'post',
    data: param
  })
}

// 删除数据演练
export function deletePreDrillJob(jobId) {
  const ids = []
  ids.push(jobId)
  return request({
    url: 'api/job/preDrill/deleteJob',
    method: 'delete',
    data: ids
  })
}

// 模糊查询libra 作业
export function blurry_search_libra_job(jobName) {
  return request({
    url: 'api/job/blurry/' + jobName,
    method: 'get'
  })
}

// 查询表详细信息
export function get_table_detail_Info_by_ids(ids) {
  return request({
    url: 'api/job/sourceDetailInfo',
    method: 'post',
    data: ids
  })
}

// 根据业务code返回一个可用topicId
export function get_business_tableId(businessCode) {
  return request({
    url: 'api/job/label/businessTableId/' + businessCode,
    method: 'get'
  })
}

// 采样表数据
export function get_table_message(ids) {
  return request({
    url: 'api/job/tableMessage',
    method: 'post',
    data: ids
  })
}

// 获取作业运行的webUrl
export function get_job_webUrl(jobId) {
  return request({
    url: 'api/job/webUrl/' + jobId,
    method: 'get'
  })
}

// 上线作业（上线并运行）
export function job_is_has_latest_checkpoint(jobId) {
  return request({
    url: 'api/job/isHasLatestCheckpoint/' + jobId,
    method: 'get'
  })
}

// 获取作业的子作业
export function get_sub_jobs(jobId) {
  return request({
    url: 'api/job/getSubJobs/' + jobId,
    method: 'get'
  })
}

// 校验数仓表
export function check_odps_table(tableName) {
  return request({
    url: 'api/job/check_odps_table/' + tableName,
    method: 'get'
  })
}

// 创建Odps表
export function createOdpsTable(param) {
  return request({
    url: 'api/job/create_odps_table',
    method: 'post',
    data: param
  })
}

// 获取算法算子镜像信息
export function getAlgoTransformationImageInfo() {
  return request({
    url: 'api/job/algo_transformation_image_info',
    method: 'get'
  })
}

// 获取算法算子镜像血缘
export function getAlgoTransformationImageLineage(image, tag) {
  return request({
    url: 'api/job/algo_image_lineage/' + image + '/' + tag,
    method: 'get'
  })
}

// 获取表血缘信息-模糊查询
export function searchTableLineageInfo(tableName) {
  return request({
    url: 'api/table/tableLineage/' + tableName,
    method: 'get'
  })
}

// 获取表完整上下游血缘
export function tablePipline(param) {
  return request({
    url: 'api/table/tablePipline',
    method: 'post',
    data: param
  })
}

export function stephen_param(param) {
  return request({
    url: 'api/stephen/param',
    method: 'post',
    data: param
  })
}

export function stephen_company() {
  return request({
    url: 'api/stephen/company',
    method: 'get'
  })
}

export function stephen_company2() {
  return request({
    url: 'api/datasource/company',
    method: 'get'
  })
}

export function stephen_close_state() {
  return request({
    url: 'api/stephen/close_state',
    method: 'get'
  })
}

export function get_author() {
  return request({
    url: 'api/stephen/jobs',
    method: 'get'
  })
}

export function create_job(param) {
  return request({
    url: 'api/stephen/create_job',
    method: 'post',
    data: param
  })
}
