<template>
  <div class="app-container">
    <el-dialog
      title="数据源配置"
      :visible.sync="tag"
      width="57%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-source-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>选择数据源表:</span>
            </div>
          </el-col>
          <el-col :span="19">
            <div class="col-content element-source">
              <el-cascader
                v-model="selectData"
                placeholder="试试搜索 kafka"
                :options="options"
                clearable
                filterable
                size="medium"
                style="width: 100%"
                @change="storeAllFields"
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span>预览全部字段:</span>
            </div>
          </el-col>
          <el-col :span="15">
            <div class="col-content element-source">
              <el-input
                v-model="fieldsCheck"
                type="textarea"
                :rows="1"
                autosize
                readonly
                resize="none"
                placeholder="可以查询topic对应的字段有哪些 here"
              />
            </div>
          </el-col>
          <el-col :span="4">
            <div class="col-content element-field-button">
              <el-button type="info" @click="generateFields">{{ fieldsCheckShow?'一键预览':'取消预览' }}</el-button>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>指定读取时间:</span>
            </div>
          </el-col>
          <el-col :span="19">
            <div class="col-content">
              <el-date-picker
                v-model="consumerDatetime"
                type="datetime"
                value-format="timestamp"
                default-value="2022-08-14"
                placeholder="选择日期时间"
                @blur="correctDateTime"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import { getSourceAndTheirTable, getTableFields, getSourceTypeNameMapping } from '@/api/job'
import { getDataSourceTypeName, getTableNameByTableId, timestampToTime } from '../js/common'

export default {
  name: 'RealtimeSource',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectData: [],
      options: [],
      consumerDatetime: '',
      choseColumn: '',
      fieldsCheck: '',
      fieldsCheckShow: true,
      showOperation: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'SourceTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'SourceTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.selectData = JSON.parse(JSON.parse(sessionStorage.getItem('editJsonContent')).selectData)
            this.choseColumn = JSON.parse(sessionStorage.getItem('editJsonContent')).choseColumn
            this.consumerDatetime = Date.parse(new Date().toString())
            this.showOperation = JSON.parse(JSON.parse(sessionStorage.getItem('editJsonContent')).showOperation)
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
    console.log('---RealtimeSource---mounted----executed----')
    const sourceOptionArray = []
    getSourceAndTheirTable().then(res => {
      if (res.code === '200') {
        const tableIdAndTableNameArr = []
        res.data.forEach(resItem => {
          if (resItem.sourceType === 2) {
            const sourceOptionItem = {}
            sourceOptionItem.value = resItem.sourceName
            sourceOptionItem.label = resItem.sourceName
            sourceOptionItem.children = resItem.sourceTable.map(item => ({ value: item.tableId, label: item.tableName }))
            sourceOptionArray.push(sourceOptionItem)

            resItem.sourceTable.forEach(tableItem => {
              tableIdAndTableNameArr.push({ 'tableId': tableItem.tableId, 'tableName': tableItem.tableName })
            })
          }
        })
        this.options = sourceOptionArray
        console.log(sourceOptionArray)
        sessionStorage.setItem('tableIdAndTableNameArrSource', JSON.stringify(tableIdAndTableNameArr))
      }
    })

    getSourceTypeNameMapping().then(res => {
      if (res.code === '200') {
        sessionStorage.setItem('sourceTypeNameMapping', JSON.stringify(res.data))
      }
    })
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'selectData': '', 'choseColumn': '', 'transformation': '', 'showOperation': '' }

      const transformation = { 'connectConfig': { 'connector': '', 'tableId': '', 'scan.startup.mode': '', 'scan.startup.timestamp-millis': '' }}
      transformation.connectConfig.connector = getDataSourceTypeName(this.selectData[0])
      transformation.connectConfig.tableId = this.selectData[1]
      transformation.connectConfig['scan.startup.mode'] = 'timestamp'
      console.log(this.consumerDatetime)
      transformation.connectConfig['scan.startup.timestamp-millis'] = this.consumerDatetime === null ? '' : this.consumerDatetime.toString()

      resultJson.selectData = JSON.stringify(this.selectData)
      resultJson.choseColumn = this.choseColumn
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      const sourceTable = this.selectData[0] === undefined ? '' : this.selectData[0] + '/' + getTableNameByTableId(this.selectData[1], 'SourceTransformation')
      this.showOperation.push('【选择的topic】' + sourceTable)
      this.showOperation.push('【topic的字段有】' + this.choseColumn)
      const consumerTime = this.consumerDatetime === '' || this.consumerDatetime === null ? '' : timestampToTime(this.consumerDatetime)
      this.showOperation.push('【topic的启动位点】' + consumerTime)
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)

      this.clearData()
    },
    clearData() {
      this.selectData = []
      this.choseColumn = ''
      this.showOperation = []
      this.consumerDatetime = ''
      this.fieldsCheckShow = true
      this.fieldsCheck = ''
    },
    isFullEdit() {
      if (this.selectData.length === 0 || this.consumerDatetime === '') {
        return '0'
      } else {
        return '1'
      }
    },
    correctDateTime() {
      if (this.consumerDatetime !== '') {
        const nowDateTime = Date.parse(new Date().toString())
        if (this.consumerDatetime > nowDateTime) {
          this.consumerDatetime = nowDateTime
        }
      }
    },
    storeAllFields() {
      if (this.selectData.length !== 0) {
        getTableFields(this.selectData[1]).then(res => {
          if (res.code === '200') {
            const fieldsArr = []
            res.data.forEach(item => {
              fieldsArr.push(item.fieldName + '    ' + item.dataType.toLowerCase())
            })
            this.choseColumn = fieldsArr.join(',')
          }
        })
      }
    },
    generateFields() {
      if (this.fieldsCheckShow === true) {
        if (this.selectData.length !== 0) {
          getTableFields(this.selectData[1]).then(res => {
            if (res.code === '200') {
              const fieldsArr = []
              res.data.forEach(item => {
                fieldsArr.push(item.fieldName + '    ' + item.dataType.toLowerCase())
              })
              this.fieldsCheck = fieldsArr.join(',\n')
            }
          })
          this.fieldsCheckShow = !this.fieldsCheckShow
        } else {
          this.$message({
            type: 'info',
            message: '先选择数据源'
          })
        }
      } else {
        this.fieldsCheck = ''
        this.fieldsCheckShow = !this.fieldsCheckShow
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-source-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

  .element-source{
    line-height: 30px;
  }

  .element-field-button{
    line-height: 30px;
    margin-top: 1px;
  }

</style>
