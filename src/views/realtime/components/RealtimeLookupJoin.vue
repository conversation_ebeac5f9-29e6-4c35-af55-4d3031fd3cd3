<template>
  <div class="app-container">
    <el-dialog
      title="维表join操作配置"
      :visible.sync="tag"
      width="57%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-lookup-join-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>关联类型:</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="col-content element-value" style="text-align: left">
              <el-radio v-model="joinType" label="left" border>left</el-radio>
              <el-radio v-model="joinType" label="inner" border>inner</el-radio>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>维表选择:</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="col-content element-source">
              <el-cascader
                v-model="selectData"
                placeholder="试试搜索：rds"
                :options="options"
                clearable
                filterable
                size="medium"
                style="width: 100%"
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span>预览全部字段:</span>
            </div>
          </el-col>
          <el-col :span="13">
            <div class="col-content element-source">
              <el-input
                v-model="fieldsCheck"
                type="textarea"
                :rows="1"
                autosize
                readonly
                resize="none"
                placeholder="可以查询维表对应的字段有哪些 here"
              />
            </div>
          </el-col>
          <el-col :span="4">
            <div class="col-content element-field-button">
              <el-button type="info" @click="generateFields">{{ fieldsCheckShow?'一键预览':'取消预览' }}</el-button>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>关联条件:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="joinConditionExpression"
                placeholder="example=> tl.user_id = tr.userid  注意别名: 左流用tl，维表用tr"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>选择字段:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="selectExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> tl.*, tr.country  注意别名: 左流用tl，维表用tr，如果全选可以用*"
                clearable
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getSourceAndTheirTable, getSourceTypeNameMapping, getTableFields } from '@/api/job'
import { getDataSourceTypeName, getTableNameByTableId } from '../js/common'

export default {
  name: 'RealtimeLookupJoin',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectExpression: '',
      joinType: 'left',
      selectData: [],
      fieldsCheck: '',
      fieldsCheckShow: true,
      joinConditionExpression: '',
      isDeduplicateByJoinKey: 'true',
      showOperation: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'LookupJoinTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'LookupJoinTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.joinType = JSON.parse(sessionStorage.getItem('editJsonContent')).joinType
            this.selectExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).selectExpression
            this.joinConditionExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).joinConditionExpression
            this.selectData = JSON.parse(JSON.parse(sessionStorage.getItem('editJsonContent')).selectData)
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
    console.log('---RealtimeLookupJoin---mounted----executed----')
    const sourceOptionArray = []
    getSourceAndTheirTable().then(res => {
      if (res.code === '200') {
        const tableIdAndTableNameArr = []
        res.data.forEach(resItem => {
          if (resItem.sourceType === 1) {
            const sourceOptionItem = {}
            sourceOptionItem.value = resItem.sourceName
            sourceOptionItem.label = resItem.sourceName
            sourceOptionItem.children = resItem.sourceTable.map(item => ({ value: item.tableId, label: item.tableName }))
            sourceOptionArray.push(sourceOptionItem)

            resItem.sourceTable.forEach(tableItem => {
              tableIdAndTableNameArr.push({ 'tableId': tableItem.tableId, 'tableName': tableItem.tableName })
            })
          }
        })
        this.options = sourceOptionArray
        console.log(sourceOptionArray)
        sessionStorage.setItem('tableIdAndTableNameArrLookupJoin', JSON.stringify(tableIdAndTableNameArr))
      }
    })

    getSourceTypeNameMapping().then(res => {
      if (res.code === '200') {
        sessionStorage.setItem('sourceTypeNameMapping', JSON.stringify(res.data))
      }
    })
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'selectExpression': '', 'joinConditionExpression': '', 'joinType': '', 'selectData': '', 'transformation': '', 'showOperation': '' }

      const transformation = { 'selectExpression': '', 'joinConditionExpression': '', 'joinType': '', 'leftViewAlias': 'tl', 'rightViewAlias': 'tr', 'connectConfig': { 'connector': '', 'tableId': '' }}
      transformation.selectExpression = this.selectExpression.trim().replace(/\n/g, '')
      transformation.joinConditionExpression = this.joinConditionExpression.trim()
      transformation.joinType = this.joinType
      transformation.connectConfig.connector = getDataSourceTypeName(this.selectData[0])
      transformation.connectConfig.tableId = this.selectData[1]

      resultJson.selectExpression = this.selectExpression.trim()
      resultJson.joinConditionExpression = this.joinConditionExpression.trim()
      resultJson.joinType = this.joinType
      resultJson.selectData = JSON.stringify(this.selectData)
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      this.showOperation.push('【关联类型】' + this.joinType)
      this.showOperation.push('【关联条件】' + this.joinConditionExpression.trim())
      this.showOperation.push('【选择字段】' + this.selectExpression.trim().replace(/\n/g, ''))
      const dimTable = this.selectData[0] === undefined ? '' : this.selectData[0] + '/' + getTableNameByTableId(this.selectData[1], 'LookupJoinTransformation')
      this.showOperation.push('【关联维表】' + dimTable)
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)
      this.clearData()
    },
    clearData() {
      this.joinType = 'left'
      this.selectExpression = ''
      this.joinConditionExpression = ''
      this.selectData = []
      this.fieldsCheckShow = true
      this.fieldsCheck = ''
    },
    isFullEdit() {
      if (this.selectExpression.trim() === '' || this.joinConditionExpression.trim() === '' || this.selectData.length === 0) {
        return '0'
      } else {
        return '1'
      }
    },
    generateFields() {
      if (this.fieldsCheckShow === true) {
        if (this.selectData.length !== 0) {
          getTableFields(this.selectData[1]).then(res => {
            if (res.code === '200') {
              const fieldsArr = []
              res.data.forEach(item => {
                fieldsArr.push(item.fieldName + '    ' + item.dataType.toLowerCase())
              })
              this.fieldsCheck = fieldsArr.join(',\n')
            }
          })
          this.fieldsCheckShow = !this.fieldsCheckShow
        } else {
          this.$message({
            type: 'info',
            message: '先选择维表'
          })
        }
      } else {
        this.fieldsCheck = ''
        this.fieldsCheckShow = !this.fieldsCheckShow
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-lookup-join-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

</style>

