<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">数据源名称</label>
        <el-input
          v-model="query.dataSourceName"
          clearable
          style="width: 300px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <label class="el-form-item-label">数据源类型</label>
        <el-select
          v-model="query.dataSourceType"
          filterable
          placeholder="请选择"
          clearable
          style="width: 300px;"
          @blur="selectBlur"
          @clear="selectClear"
          @change="selectChange"
        >
          <el-option
            v-for="item in dict.数据源类型"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>
        <label class="el-form-item-label">使用位置</label>
        <el-select
          v-model="query.usePlace"
          filterable
          placeholder="请选择"
          clearable
          style="width: 300px;"
          @blur="selectBlur"
          @clear="selectClear"
          @change="selectChange"
        >
          <el-option
            v-for="item in dict.数据源使用位置"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />

      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        width="500px"
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          size="small"
          label-width="80px"
        >
          <el-form-item label="数据源 类型" prop="dataSourceType">

            <el-select
              v-model="form.dataSourceType"
              :disabled="form.id"
              filterable
              placeholder="请选择"
              style="width: 370px;"
            >
              <el-option
                v-for="item in dict.数据源类型"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />

            </el-select>
          </el-form-item>
          <el-form-item
            label="数据源 名称"
            prop="dataSourceName"
          >
            <el-input
              v-model="form.dataSourceName"
              style="width: 370px;"
            />
          </el-form-item>
          <el-form-item
            label="数据源 描述"
            prop="dataSourceDesc"
          >
            <el-input
              v-model="form.dataSourceDesc"
              style="width: 370px;"
            />
          </el-form-item>
          <el-form-item
            label="链接地址"
            prop="ip"
          >
            <el-input
              v-model="form.ip"
              style="width: 370px;"
            />
          </el-form-item>

          <el-form-item
            v-if="form.dataSourceType==4?true:false"
            label="tunnelIp"
            prop="tunnelIp"
          >
            <el-input
              v-model="form.tunnelIp"
              style="width: 370px;"
            />
          </el-form-item>
          <el-form-item
            v-if="form.dataSourceType!=2&&form.dataSourceType!=9?true:false"
            label="库"
            prop="dbName"
          >
            <el-input v-model="form.dbName" style="width: 370px;" />
          </el-form-item>

          <el-form-item
            v-if="form.dataSourceType!=2&&form.dataSourceType!=9?true:false"
            label="账号"
            prop="userName"
          >
            <el-input v-model="form.userName" style="width: 370px;" />
          </el-form-item>

          <el-form-item
            v-if="form.dataSourceType!=2&&form.dataSourceType!=9?true:false"
            label="密码"
            prop="password"
          >

            <el-input
              v-model="form.password"
              show-password
              style="width: 370px;"
            />
          </el-form-item>
          <el-form-item
            label="使用位置"
            prop="usePlace"
          >
            <el-select
              v-model="form.usePlace"
              filterable
              placeholder="请选择"
              style="width: 370px;"
            >
              <el-option
                v-for="item in dict.数据源使用位置"
                :key="item.id"
                :disabled="form.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>


          <el-form-item
            label="kafka集群数据类型"
            prop="binlogSource"
          >
            <el-input
              v-model="form.binlogSource"
              style="width: 370px;"
            />
          </el-form-item>

          <el-form-item label="负责人">
            <el-input
              v-model="form.owner"
              style="width: 370px;"
            />
          </el-form-item>
        </el-form>

        <div slot="footer">
          hbase数据源校验需要等待10s
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">
            取消
          </el-button>
          <el-button
            :loading="crud.status.cu === 2"
            type="primary"
            @click="crud.submitCU"
          >
            确认
          </el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="crud.data"
        size="small"
        style="width: 100%;"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column
          prop="id"
          label="自增id"
        />
        <el-table-column
          prop="dataSourceType"
          label="数据源类型"
        >
          <template slot-scope="scope">
            {{ dict.label.数据源类型[scope.row.dataSourceType] }}
          </template>
        </el-table-column>
        <el-table-column
          prop="dataSourceName"
          label="数据源名称"
        />
        <el-table-column
          prop="dataSourceDesc"
          label="数据源描述"
        />
        <el-table-column
          prop="ip"
          label="链接地址"
        />
        <el-table-column
          prop="dbName"
          label="库名"
        />
        <el-table-column
          prop="userName"
          label="账号"
        />
        <el-table-column
          prop="usePlace"
          label="使用位置"
        >
          <template slot-scope="scope">
            {{ dict.label.数据源使用位置[scope.row.usePlace] }}
          </template>
        </el-table-column>

        <el-table-column
          prop="owner"
          label="负责人"
        />
        <el-table-column
          prop="modifyTime"
          label="修改时间"
        />

        <el-table-column
          v-if="checkPer(['admin','reDataSource:edit','reDataSource:del'])"
          label="操作"
          width="150px"
          align="center"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudReDataSource from '@/api/reDataSource'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, createTime: null, tunnelIp:null,modifyTime: null, dataSourceType: null, dataSourceName: null, dataSourceDesc: null, tableNum: null, owner: null, ip: null, userName: null, password: null, createBy: null, dept: null, usePlace: null }

export default {
  name: 'ReDataSource',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['数据源类型', '数据源使用位置'],
  cruds() {
    return CRUD({ title: '数据源管理', url: 'api/reDataSource', idField: 'id', sort: 'id,desc', crudMethod: { ...crudReDataSource }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'reDataSource:add'],
        edit: ['admin', 'reDataSource:edit'],
        del: ['admin', 'reDataSource:del']
      },
      rules: {
        dataSourceType: [
          { required: true, message: '数据源类型不能为空', trigger: 'blur' }
        ],
        dataSourceName: [
          { required: true, message: '数据源名称不能为空', trigger: 'blur' }
        ],
        dataSourceDesc: [
          { required: true, message: '数据源描述不能为空', trigger: 'blur' }
        ],
        ip: [
          { required: true, message: '链接地址不能为空', trigger: 'blur' }
        ],
        dbName: [
          { required: true, message: '库不能为空', trigger: 'blur' }
        ],
        userName: [
          { required: true, message: '账号不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ],
        usePlace: [
          { required: true, message: '使用位置不能为空', trigger: 'blur' }
        ]
      }}
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
