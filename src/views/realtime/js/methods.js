import '@antv/x6-vue-shape'
import { Addon } from '@antv/x6'
export const startDragToGraph = (graph, type, text, e) => {
  const node = graph.createNode({
    width: 120,
    height: 50,
    attrs: {
      label: {
        text: text,
        type: type,
        extend: { 'type': type },
        fill: '#f8ca0e',
        cursor: 'pointer',
        fontSize: 18,
        textWrap: {
          width: -10,
          height: -10,
          ellipsis: true
        }
      },
      body: {
        stroke: '#000000',
        strokeWidth: 1,
        fill: '#306795FF',
        cursor: 'pointer',
        rx: 4, // 水平方向的圆角半径
        ry: 4, // 垂直方向的圆,

        filter: {
          name: 'dropShadow',
          args: {
            dx: 2,
            dy: 2,
            blur: 3
          }
        }
      },
      data: {
        algId: 'wer'
      }
    },
    ports: ports
  })
  const dnd = new Addon.Dnd({ target: graph })
  dnd.start(node, e)
}

const ports = {
  groups: {
    left: {
      position: 'left',
      attrs: {
        circle: {
          r: 6,
          magnet: true,
          stroke: '#2D8CF0',
          strokeWidth: 2,
          fill: '#fff'
        }
      }
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          r: 6,
          magnet: true,
          stroke: '#2D8CF0',
          strokeWidth: 2,
          fill: '#fff'
        }
      }
    }
  },
  items: [
    {
      id: 'port3',
      group: 'left'
    },
    {
      id: 'port4',
      group: 'right'
    }
  ]
}
