<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">作业名</label>
        <el-input v-model="query.jobName" clearable placeholder="作业名" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">作业状态</label>
        <el-select
          v-model="query.status"
          filterable
          placeholder="请选择"
          clearable
          style="width: 185px;"
          @blur="selectBlur"
          @clear="selectClear"
          @change="selectChange"
        >
          <el-option
            v-for="item in dict.新作业状态"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>
        <label class="el-form-item-label">创建人</label>
        <el-input v-model="query.createUserName" clearable placeholder="创建人" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <rrOperation :crud="crud"/>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission"/>



      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;"
                @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55"/>
        <el-table-column prop="id" label="id"/>
        <el-table-column prop="jobName" label="作业名"/>
        <el-table-column prop="jobDesc" label="作业描述"/>
        <el-table-column prop="status" label="作业状态">
        </el-table-column>
        <el-table-column prop="type" label="模版类型">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间"/>
        <el-table-column prop="updateTime" label="更新时间"/>
        <el-table-column prop="ownerName" label="创建人"/>

      </el-table>
      <!--分页组件-->
      <pagination/>
    </div>
    <Dqc :is-show="isDqcShow" :job-id="dqcJobId" :job-name="dqcJobName" :source-table-name-list="sourceTableNameList" :dim-table-name-list="dimTableNameList" :is-has-dim-table="isHasDimTable" @close="closeDqcDialog" ></Dqc>
  </div>
</template>

<script>
import crudnewtopn from '@/api/newtopn'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import crudReDataSourceMetadata from '@/api/reDataSourceMetadata'
import { get_job_info_by_dqc, getTableInfoCommonSql } from '@/api/job'
import Dqc from '../../realtime/components/Dqc'

const defaultForm = {
  id: null,
  jobName: null,
  moduleName: null,
  sinkTableIds: null,
  sourceTableIds: null,
  sinkSchemaPrimaryKey: null,
  sql: null,
  cacheTTL: null,
  relativeStartTs:null,
  optionConfigs:null,
  jobDesc:null
}
export default {
  name: 'newsql',
  components: {pagination, crudOperation, rrOperation, udOperation, Dqc},
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['关联类型', '新全量表', '统计周期', '二期作业状态','新作业状态'],
  cruds() {
    return CRUD({
      title: 'sql作业模板',
      url: 'api/teJobInfo/all',
      idField: 'id',
      sort: 'id,desc',
      crudMethod: {...crudnewtopn}
    })
  },
  data() {
    return {
      permission: {
      },
      rules: {
        jobName: [
          {required: true, message: '作业名不能为空', trigger: 'blur'}
        ],
        sinkTableIds: [
          {required: true, message: '输出表不能为空', trigger: 'blur'}
        ],
        sourceTableIds: [
          {required: true, message: '输入表不能为空', trigger: 'blur'}
        ],
        sql: [
          {required: true, message: 'sql逻辑不能为空', trigger: 'blur'}
        ],
        cacheTTL: [
          {required: true, message: '数据缓存时间不能为空', trigger: 'blur'}
        ],
        sinkSchemaPrimaryKey: [
          {required: true, message: '输出表主键字段不能为空', trigger: 'blur'}
        ]
      },
      queryTypeOptions: [
        {key: 'jobName', display_name: '作业名称'}
      ],
      isDqcShow: false,
      dqcJobId: 0,
      dqcJobName: '',
      sourceTableNameList: [],
      dimTableNameList: [],
      isHasDimTable: '0',


      options: [],
      value: [],
      list: [],
      list2: [],
      iloading: false
    }
  },
  methods: {
    syncs(ids) {
      crudReDataSourceMetadata.syncs(ids).then(res => {
        console.log('-------------------------------')
        console.log(ids)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    // 执行
    check(id) {
      crudnewtopn.check(id).then(res => {
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    collect(id,jobName) {
      crudReDataSourceMetadata.collect(id,jobName).then(res => {
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    deploy(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.deploy(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    del(data) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.del(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    stop(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.stop(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    toDqc(id,jobName){
      get_job_info_by_dqc(id).then(res=>{
        if (res.code === '200') {
          this.sourceTableNameList = res.data.sourceTableList
          this.dimTableNameList = res.data.dimTableList
          this.isHasDimTable = res.data.dimTableList.length === 0 ? '0' : '1'
          this.isDqcShow = true
          this.dqcJobId = id
          this.dqcJobName = jobName
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    closeDqcDialog(tag){
      this.isDqcShow = false
    },
    remoteMethod(query) {
      if (query !== '') {
        this.iloading = true;
        const pathParams = []
        pathParams.push({ 'paramName': 'tableName', 'paramValue': query })
        getTableInfoCommonSql(pathParams).then(res=>{
          if(res.code === '200'){
            this.list = res.data.map(item => {
              return { value: item.tableId, label: item.tableName+'(表类型:'+item.dataSourceTypeName+',所属数据源:'+item.dataSourceName+',所属库:'+item.dbName+')' };
            });
            this.iloading = false;
            this.options = this.list.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1;
            });
            console.log(this.options)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      } else {
        this.options = [];
      }
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
