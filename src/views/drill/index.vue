<template>
  <div class="app-container">
    <el-row :gutter="10" style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px" class="element-header-tool">
      <el-col :span="2.1">
        <div>
          <el-button size="small" type="primary" icon="el-icon-edit" @click="createJob">新增数据演练</el-button>
        </div>
      </el-col>
      <el-col :span="4">
        <div>
          <el-input
            v-model="taskNameBlurry"
            placeholder="libra平台演练作业名"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="4">
        <div>
          <el-input
            v-model="createNameBlurry"
            placeholder="创建人"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="1.1">
        <div>
          <el-button size="small" type="success" icon="el-icon-search" @click="doSearch">搜索</el-button>
        </div>
      </el-col>
      <el-col :span="1.1">
        <div>
          <el-button size="small" type="warning" icon="el-icon-refresh-left" @click="clearBlurry">重置</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col>
        <el-table
          :data="tableData"
          stripe
          highlight-current-row
          :header-cell-style="{textAlign: 'left'}"
          size="medium"
          style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
        >
          <el-table-column
            prop="taskName"
            label="libra平台演练作业名"
            fixed
            sortable
            align="left"
            width="350"
          />
            <el-table-column
              prop="drillDescribe"
              label="演练描述"
              align="left"
              width="300"
            />
          <el-table-column
            prop="createName"
            label="创建人"
            sortable
            align="left"
            width="120"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            prop="modifyName"
            label="修改人"
            sortable
            align="left"
            width="120"
          />
          <el-table-column
            prop="modifyTime"
            label="修改时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            label="相关操作"
            width="700"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                plain
                class="el-icon-edit-outline"
                @click="updateJob(scope.row)"
              >编辑演练结果</el-button>
              <el-button
                size="mini"
                type="danger"
                plain
                class="el-icon-delete"
                @click="handleDelete(scope.row)"
              >删除演练结果</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>

    </el-row>
    <el-row style="margin-top: 20px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px">
      <el-col>
        <el-pagination
          background
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[10, 15, 30, 50]"
          class="page_tt"
          :total="page_info.total_num"
          :page-size="page_info.page_size"
          :current-page="page_info.current_page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
        />
      </el-col>
    </el-row>

    <drill-panel style="position: absolute" :is-show="isPreDrillEditShow" :edit-json="preDrillData" @close="closePreDrillEditDialog" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import DrillPanel from './DrillPanel'
import { queryPreDrillPageable, deletePreDrillJob } from '@/api/job'

export default {
    name: 'DrillPanelIndex',
    components: {
      DrillPanel
    },
    props: {
    },
    data() {
      return {
        isDrawerShow: false,
        tableData: [],
        page_info: {
          total_num: 0,
          current_page: 1,
          page_size: 10
        },
        loading: false,
        taskNameBlurry: '',
        createNameBlurry: '',
        isPreDrillEditShow: false,
        preDrillData: { 'id': 0, 'taskName': '', 'drillDescribe': '', 'data': {}, 'result': {} },
        copyJobForm: {
          jobName: ''
        },
        copyJobDialogFormVisible: false,
        isValidateButtonLoading: false,
        validateButtonLoadingJobId: 0,
      }
    },
    computed: {
      ...mapGetters([
        'user'
      ])
    },
    watch: {
    },
    mounted() {
    },
    created() {
      this.doSearch()
    },
    methods: {
      createJob(){
        this.preDrillData = { 'id': 0, 'taskName': '', 'drillDescribe': '', 'data': {}, 'result': {} }
        this.isPreDrillEditShow = true
      },
      updateJob(row) {
        if (this.user.username !== row.modifyName) {
          this.$confirm('是否确认解锁编辑?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.preDrillData.id = row.id
            this.preDrillData.taskName = row.taskName
            this.preDrillData.drillDescribe = row.drillDescribe
            this.preDrillData.data = JSON.parse(row.data)
            this.preDrillData.result = JSON.parse(row.result)

            this.isPreDrillEditShow = true
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消编辑'
            })
          })
        } else {
          this.preDrillData.id = row.id
          this.preDrillData.taskName = row.taskName
          this.preDrillData.drillDescribe = row.drillDescribe
          this.preDrillData.data = JSON.parse(row.data)
          this.preDrillData.result = JSON.parse(row.result)

          this.isPreDrillEditShow = true
        }
      },
      closePreDrillEditDialog(tag){
        this.isPreDrillEditShow = false
        if(tag === '1'){
          this.doSearch()
        }
      },
      doSearch(){

        this.loading = true
        const pathParams = []

        pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
        pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
        pathParams.push({ 'paramName': 'createName', 'paramValue': this.createNameBlurry })
        pathParams.push({ 'paramName': 'taskName', 'paramValue': this.taskNameBlurry })

        queryPreDrillPageable(pathParams).then(res => {
          if (res.code === '200') {
            this.page_info.total_num = res.data.totalElements
            this.fillTableData(res.data.content)
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
          this.loading = false
        })
      },
      fillTableData(data) {
        const tempTableData = []
        data.forEach(item => {
          tempTableData.push({ 'id': item.id, 'taskName': item.taskName, 'createName': item.createName, 'drillDescribe': item.drillDescribe, 'createTime': item.createTime, 'modifyTime': item.modifyTime, 'modifyName': item.modifyName, 'data': item.data, 'result': item.result })
        })
        this.tableData = tempTableData
      },
      clearBlurry() {
        this.taskNameBlurry = ''
        this.createNameBlurry = ''
      },
      handleSizeChange(val) {
        this.page_info.page_size = val
        this.doSearch()
      },
      handleCurrentChange(val) {
        this.page_info.current_page = val
        this.doSearch()
      },
      cancel(formName) {
        this.copyJobDialogFormVisible = false
        this.$refs[formName].resetFields()
      },
      handleDelete(row) {
        this.$confirm('是否确认删除 ' + row.jobName + ' ?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deletePreDrillJob(row.id).then(res => {
            if (res.status === 200) {
              this.$message({
                type: 'success',
                message: '删除进入审批流程!'
              })
            }
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.doSearch()
            }
            if (res.code === '500') {
              this.$message({
                type: 'error',
                message: res.mes
              })
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      },



    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>
