import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/reDataSourceMetadata',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/reDataSourceMetadata/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/reDataSourceMetadata',
    method: 'put',
    data
  })
}


export function sync(id) {
  return request({
    url: 'api/reDataSourceMetadata/sync/' + id,
    method: 'put'
  })
}

export function syncs(ids) {
  return request({
    url: 'api/reDataSourceMetadata/sync/tables?tableIds=' + ids,
    method: 'get'
  })
}

export function synctwotable(id1, id2) {
  return request({
    url: 'api/reDataSourceMetadata/sync/twotable?tableId1=' + id1 + '&tableId2=' + id2,
    method: 'get'
  })
}


export function collect(id,jobName) {
  return request({
    url: 'api/reDataSourceMetadata/collect/tables?tableId=' + id + '&jobName=' + jobName,
    method: 'get'
  })
}

export default {add, edit, del, sync,syncs, synctwotable,collect}
