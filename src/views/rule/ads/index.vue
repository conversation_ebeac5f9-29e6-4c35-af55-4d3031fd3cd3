<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">作业名</label>
        <el-input v-model="query.jobName" clearable placeholder="作业名" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <rrOperation :crud="crud"/>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission"/>
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0"
                 :title="crud.status.title" width="999px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="作业名" prop="jobName">
            <el-input v-model="form.jobName"  :disabled="form.id" placeholder="" style="width: 800px;"/>
          </el-form-item>
          <el-form-item label="作业描述" prop="jobName">
            <el-input v-model="form.jobDesc"   placeholder="" style="width: 800px;"/>
          </el-form-item>
          <el-form-item label="数据集名称" prop="dataSetName">
            <el-input v-model="form.dataSetName" :rows="1" type="textarea" placeholder="" style="width: 800px;"/>
          </el-form-item>

          <el-form-item label="输出表" prop="sinkTableId">
            <el-select v-model="form.sinkTableId" filterable placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.全量表"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="启动时间" >
            <el-input v-model="form.relativeStartTs" placeholder="填day-x或hour-x或minute-1 (day-1表示从昨天0点启动，hour-1表示当前时间前一个小时启动，不填写则从当前时间启动)" style="width: 800px;"/>
          </el-form-item>
          <el-form-item label="作业参数" >
            <el-input v-model="form.optionConfigs" :rows="3" type="textarea" placeholder="高级配置,一般不填" style="width: 800px;"/>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>


      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;"
                @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55"/>
        <el-table-column prop="id" label="id"/>
        <el-table-column prop="jobName" label="作业名"/>
        <el-table-column prop="jobDesc" label="作业描述"/>
        <el-table-column prop="sinkTableId" label="输出表">
          <template slot-scope="scope">
            {{ dict.label.全量表[scope.row.sinkTableId] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="作业状态">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间"/>

        <el-table-column prop="ownerName" label="创建人"/>

        <el-table-column v-if="checkPer(['admin','adsteJobInfo:edit','adsteJobInfo:del'])" label="操作" width="250px"
                         align="right">
          <template slot-scope="scope">
            <el-button v-permission="['admin','adsteJobInfo:check']" style="margin-left: -2px" type="primary" size="mini"
                       @click="check(scope.row.id)">校验
            </el-button>
            <el-button v-permission="['admin','adsteJobInfo:deploy']" style="margin-left: -2px" type="primary" size="mini"
                       @click="deploy(scope.row.id)">发布
            </el-button>
            <el-button v-permission="['admin','adsteJobInfo:stop']" style="margin-left: -2px" type="success" size="mini"
                       @click="stop(scope.row.id)">下线
            </el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','adsteJobInfo:edit','adsteJobInfo:del'])" label="" width="150px"
                         align="left">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination/>
    </div>
  </div>
</template>

<script>
import crudnewtopn from '@/api/newtopn'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = {
  id: null,
  jobName: null,
  moduleName: 'RealtimeIndexModule',
  sinkTableId: null,
  dag_json: null,
  sourceTableId: null,
  combinedTransSql: null,
  selectExpr: null,
  cacheTTL: null,
  groupByExpr: null,
  relativeStartTs:null,
  optionConfigs:null,
  jobDesc:null
}
export default {
  name: 'ads',
  components: {pagination, crudOperation, rrOperation, udOperation},
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [ '全量表', '二期作业状态'],
  cruds() {
    return CRUD({
      title: 'ads作业模板',
      url: 'api/teJobInfo',
      idField: 'id',
      sort: 'id,desc',
      templateType: 'RealtimeIndexModule',
      crudMethod: {...crudnewtopn}
    })
  },
  data() {
    return {
      permission: {
        add: ['admin', 'adsteJobInfo:add'],
        edit: ['admin', 'adsteJobInfo:edit'],
        del: ['admin', 'adsteJobInfo:del']
      },
      rules: {
        jobName: [
          {required: true, message: '作业名不能为空', trigger: 'blur'}
        ],
        sinkTableId: [
          {required: true, message: '输出表不能为空', trigger: 'blur'}
        ],
        sourceTableId: [
          {required: true, message: '输入表不能为空', trigger: 'blur'}
        ]
      },
      queryTypeOptions: [
        {key: 'jobName', display_name: '作业名称'}
      ]
    }
  },

  methods: {
    // 执行
    check(id) {
      crudnewtopn.check(id).then(res => {
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log(err.response.data.message)
      })
    },
    deploy(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.deploy(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    del(data) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.del(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    stop(id) {
      console.log('AFASDFASDFASDFSDF')
      crudnewtopn.stop(id).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    // 执行
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
