import request from '@/utils/request'

export function deploy(ruleId) {
  return request({
    url: 'api/operatorJob/deploy/' + ruleId,
    method: 'put'
  })
}

export function stop(ruleId) {
  return request({
    url: 'api/operatorJob/stop/' + ruleId,
    method: 'put'
  })
}
export function create(ruleId) {
  return request({
    url: 'api/operatorJob/create/' + ruleId,
    method: 'put'
  })
}


export default {deploy, stop,create}
