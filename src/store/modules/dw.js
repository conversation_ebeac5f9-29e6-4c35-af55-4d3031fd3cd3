const state = {
  username: 'step<PERSON>-lin'
}

// mutations只能同步修改状态值
const mutations = {
  UPDATE_USERNAME: (state, val) => {
    state.username = val
  }
}

// actions可以异步修改状态值,调用对应的mutations方法
const actions = {
  updateUsername({ commit }, val) {
    setTimeout(() => {
      commit('UPDATE_USERNAME', val)
    }, 10000)
  }
}

// 可以根据状态进行计算获取新的结果，类似于计算属性---也可以统一写到getters.js文件中
const getters = {
  getUsernameChange(state) {
    return state.username + '-hi-'
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
