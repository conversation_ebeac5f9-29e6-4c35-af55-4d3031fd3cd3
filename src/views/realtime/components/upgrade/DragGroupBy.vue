<template>
  <div class="app-container">
    <el-dialog
      title="聚合操作配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px;margin-top: -20px">
        <el-form ref="ruleFormDragGroupBy" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item label="选择字段" prop="selectExpression">
            <el-input
              v-model="ruleForm.selectExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> username,age,count(1) as cnt"
              clearable
            />
          </el-form-item>
          <el-form-item label="聚合维度" prop="groupByExpression">
            <el-input
              v-model="ruleForm.groupByExpression"
              :rows="1"
              autosize
              placeholder="example=> username,age"
              clearable
            />
          </el-form-item>
          <el-form-item label="having分组筛选">
            <el-input
              v-model="ruleForm.havingExpression"
              :rows="1"
              autosize
              placeholder="example=> count(1) <= 10"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DragGroupBy',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleForm: {
        selectExpression: '',
        groupByExpression: '',
        havingExpression: ''
      },
      rules: {
        selectExpression: [
          { required: true, message: '请输入选择字段', trigger: 'blur' }
        ],
        groupByExpression: [
          { required: true, message: '请选择聚合维度', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'GroupByTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        const tempExtend = JSON.parse(this.extend)
        this.ruleForm.selectExpression = tempExtend.selectExpressionOrigin === undefined ? '' : tempExtend.selectExpressionOrigin
        this.ruleForm.groupByExpression = tempExtend.groupByExpression === undefined ? '' : tempExtend.groupByExpression
        this.ruleForm.havingExpression = tempExtend.havingExpression === undefined ? '' : tempExtend.havingExpression
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragGroupBy'].validate((valid) => {
          const transformation = { 'selectExpression': '', 'groupByExpression': '', 'havingExpression': '', 'selectExpressionOrigin': '' }
          transformation.selectExpression = this.ruleForm.selectExpression.trim().replace(/\n/g, '')
          transformation.groupByExpression = this.ruleForm.groupByExpression.trim()
          transformation.havingExpression = this.ruleForm.havingExpression.trim()
          transformation.selectExpressionOrigin = this.ruleForm.selectExpression.trim()
          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragGroupBy'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.$refs['ruleFormDragGroupBy'].resetFields()
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

