<template>
  <div class="app-container">
    <el-dialog
      title="双流join操作配置"
      :visible.sync="tag"
      width="57%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-join-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>关联类型:</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="col-content element-value" style="text-align: left">
              <el-radio v-model="joinType" label="left" border>left</el-radio>
              <el-radio v-model="joinType" label="inner" border>inner</el-radio>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>左流节点编号:</span>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="col-content element-value">
              <el-input-number v-model="leftNumber" :min="99" size="small" style="width: 188px" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="col-content element-remark">
              <el-tooltip placement="bottom">
                <div slot="content">
                  双击填写好配置的左流节点，提示内容里面会有节点编号！
                </div>
                <div class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>右流节点编号:</span>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="col-content element-value">
              <el-input-number v-model="rightNumber" :min="99" size="small" style="width: 188px" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="col-content element-remark">
              <el-tooltip placement="bottom">
                <div slot="content">
                  双击填写好配置的右流节点，提示内容里面会有节点编号！
                </div>
                <div class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>关联条件:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="joinConditionExpression"
                placeholder="example=> tl.user_id = tr.userid  注意别名: 左流用tl，右流用tr"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="5">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>选择字段:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="selectExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> tl.*, tr.country  注意别名: 左流用tl，右流用tr，如果全选可以用*"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="12">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>两边是否使用关联条件对应的最新一条数据关联:</span>
            </div>
          </el-col>
          <el-col :span="6">
            <el-radio v-model="isDeduplicateByJoinKey" label="true" border>是</el-radio>
            <el-radio v-model="isDeduplicateByJoinKey" label="false" border>否</el-radio>
          </el-col>
          <el-col :span="6">
            <div class="col-content element-remark">
              <el-tooltip placement="bottom">
                <div slot="content">
                  如果选择是，那么可以减少数据膨胀 否则容易产生笛卡尔积！<br>
                  一般情况下都是选择是，当然也有选择否的，具体情况具体分析！
                </div>
                <div class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RealtimeJoin',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectExpression: '',
      joinType: 'left',
      leftNumber: 99,
      rightNumber: 99,
      joinConditionExpression: '',
      isDeduplicateByJoinKey: 'true',
      showOperation: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'JoinTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'JoinTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.joinType = JSON.parse(sessionStorage.getItem('editJsonContent')).joinType
            this.leftNumber = JSON.parse(sessionStorage.getItem('editJsonContent')).leftNumber
            this.rightNumber = JSON.parse(sessionStorage.getItem('editJsonContent')).rightNumber
            this.selectExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).selectExpression
            this.joinConditionExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).joinConditionExpression
            this.isDeduplicateByJoinKey = JSON.parse(sessionStorage.getItem('editJsonContent')).isDeduplicateByJoinKey
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'selectExpression': '', 'joinConditionExpression': '', 'joinType': '', 'leftNumber': 1, 'rightNumber': 1, 'isDeduplicateByJoinKey': '', 'transformation': '', 'showOperation': '' }

      const transformation = { 'selectExpression': '', 'joinConditionExpression': '', 'joinType': '', 'isDeduplicateByJoinKey': false, 'leftViewAlias': 'tl', 'rightViewAlias': 'tr' }
      transformation.selectExpression = this.selectExpression.trim().replace(/\n/g, '')
      transformation.joinConditionExpression = this.joinConditionExpression.trim()
      transformation.joinType = this.joinType
      transformation.isDeduplicateByJoinKey = this.isDeduplicateByJoinKey !== 'false'

      resultJson.selectExpression = this.selectExpression.trim()
      resultJson.joinConditionExpression = this.joinConditionExpression.trim()
      resultJson.joinType = this.joinType
      resultJson.leftNumber = this.leftNumber
      resultJson.rightNumber = this.rightNumber
      resultJson.isDeduplicateByJoinKey = this.isDeduplicateByJoinKey
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      this.showOperation.push('【关联类型】' + this.joinType)
      this.showOperation.push('【左流节点编号】' + this.leftNumber)
      this.showOperation.push('【右流节点编号】' + this.rightNumber)
      this.showOperation.push('【关联条件】' + this.joinConditionExpression.trim())
      this.showOperation.push('【选择字段】' + this.selectExpression.trim().replace(/\n/g, ''))
      const isDeduplicateByJoinKeyTag = this.isDeduplicateByJoinKey === 'false' ? '否' : '是'
      this.showOperation.push('【两边是否使用关联条件对应的最新一条数据关联】' + isDeduplicateByJoinKeyTag)
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)
      this.clearData()
    },
    clearData() {
      this.joinType = 'left'
      this.selectExpression = ''
      this.joinConditionExpression = ''
      this.leftNumber = 99
      this.rightNumber = 99
      this.isDeduplicateByJoinKey = 'true'
    },
    isFullEdit() {
      if (this.selectExpression.trim() === '' || this.joinConditionExpression.trim() === '' || this.leftNumber === 99 || this.rightNumber === 99) {
        return '0'
      } else {
        return '1'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-join-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

  .element-remark{
    line-height: 30px;
    margin-top: 1px;
  }

</style>

