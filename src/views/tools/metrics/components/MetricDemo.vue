<template>
  <div class="app-container">
    <el-dialog
      title="JSON格式数据查看"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="tag"
      width="50%"
      top="10vh"
    >
      <el-row style="margin-top: -35px;margin-bottom: -40px">
        <json-viewer :value="metricContentFormat" :expand-depth="expandDepth" :copyable="copyConfig" theme="jv-light" />
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import JsonViewer from 'vue-json-viewer'

export default {
  name: 'MetricDemo',
  components: {
    JsonViewer
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    metricContent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      copyConfig: { copyText: '复制', copiedText: '复制成功' },
      expandDepth: 10
    }
  },
  computed: {
    tag() {
      return this.isShow
    },
    metricContentFormat() {
      return JSON.parse(this.metricContent)
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        console.log(this.metricContent)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
