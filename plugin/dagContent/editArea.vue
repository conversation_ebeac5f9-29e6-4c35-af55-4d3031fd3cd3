<template>
  <g v-if="isEditAreaShow.value">
    <foreignObject width="100%" height="100%" style="position: relative" @click="click_menu_cover($event)">
      <body xmlns="http://www.w3.org/1999/xhtml" :style="get_menu_style()">
        <div class="menu_contain">
          <!--          <span @click="changePort('in_ports')">添加收入端口</span>-->
          <!--          <span @click="changePort('out_ports')">添加输出端口</span>-->
          <span @click="alertEditContent">编辑节点内容</span>
          <span @click="delEdges">删除节点</span>
          <span v-for="item in isEditAreaShow.rightClickEvent" :key="item.label" @click="handlePersonalThs(item.eventName)">{{ item.label }}</span>
        </div>
      </body>
    </foreignObject>
  </g>
</template>

<script>
export default {
  props: {
    isEditAreaShow: {
      type: Object,
      default: () => {
        return {
          value: false,
          x: -9999,
          y: -9999,
          id: null,
          type: null
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    click_menu_cover(e) {
      this.$emit('close_click_nodes')
      e.preventDefault()
      e.cancelBubble = true
      e.stopPropagation()
    },
    get_menu_style() {
      const left = this.isEditAreaShow.x
      const top = this.isEditAreaShow.y
      return {
        position: 'absolute',
        left: left + 'px',
        top: top + 'px'
      }
    },
    delEdges() {
      const params = {
        model_id: sessionStorage['newGraph'],
        id: this.isEditAreaShow.id
      }
      this.$confirm('此操作将永久删除该节点, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.$emit('delNode', params)
        this.$emit('close_click_nodes')
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    changePort(action) {
      this.$emit('changePort', action, this.isEditAreaShow.id)
    },
    handlePersonalThs(eventName) {
      this.$emit('nodesPersonalEvent', eventName, this.isEditAreaShow.id)
    },
    alertEditContent() {
      this.$emit('set_json_content', this.isEditAreaShow.id)
    }
  }
}
</script>

<style scoped>
.connector {
  stroke: hsla(0, 0%, 50%, 0.6);
  stroke-width: 2px;
  fill: none;
}
.menu_cover {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
.connector-hl {
  stroke: hsla(0, 0%, 50%, 0.4);
  stroke-width: 5px;
  fill: none;
}
.menu_contain {
  width: 150px;
  border: 1px solid rgba(1, 1, 1, 0.3);
  background: rgba(227, 244, 255, 0.9);
  border-radius: 5px;
  padding: 3px;
  text-align: center;
}
.menu_contain span{
  width: 100%;
  display: inline-block;
  border: 1px solid rgba(1, 1, 1, 0.3);
  border-radius: 5px;
  margin-bottom: 1.5px;
}
.menu_contain span:hover {
  background-color: #bbbbbb;
  cursor: pointer;
}
</style>
