<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <el-input v-model="query.projectName" clearable size="small" placeholder="请输入项目名称" style="width: 300px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <el-input v-model="query.commitId" clearable size="small" placeholder="请输入commitId" style="width: 300px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation />
      </div>
<!--      <label class="el-form-item-label">项目名称</label>-->
<!--      <el-input-->
<!--        v-model="query.projectName"-->
<!--        clearable-->
<!--        placeholder="输入项目名称"-->
<!--        style="width: 300px;height: 23px;margin-right: 50px"-->
<!--        class="filter-item"-->
<!--        @keyup.enter.native="crud.toQuery"-->
<!--      />-->

<!--      <label class="el-form-item-label">commitId</label>-->
<!--      <el-input-->
<!--        v-model="query.commitId"-->
<!--        clearable-->
<!--        placeholder="输入commitId"-->
<!--        style="width: 300px;height: 23px;margin-right: 50px"-->
<!--        class="filter-item"-->
<!--        @keyup.enter.native="crud.toQuery"-->
<!--      />-->
<!--      <rrOperation :crud="crud" />-->

      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="550px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
<!--          <el-form-item label="id">-->
<!--            <el-input v-model="form.id" style="width: 370px;" />-->
<!--          </el-form-item>-->
          <el-form-item label="项目id" prop="projectId">
            <el-input v-model="form.projectId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="commitId" prop="commitId">
            <el-input v-model="form.commitId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="提交信息" prop="commitMessage">
            <el-input v-model="form.commitMessage" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="json配置" prop="configData">
            <el-input v-model="form.configData" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="额外配置(预留)" prop="ext">
            <el-input v-model="form.ext" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="ci时间" prop="ciTime">
            <el-input v-model="form.ciTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建日期" prop="createTime">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新日期" prop="modifyTime">
            <el-input v-model="form.modifyTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
<!--        <el-table-column prop="id" label="id" />-->
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="commitId" label="commitId" width="300" />
        <el-table-column prop="commitMessage" label="提交信息" />
<!--        <el-table-column prop="projectId" label="gitlab的项目id" />-->
        <el-table-column prop="configData" label="json配置" />
<!--        <el-table-column prop="ext" label="额外配置(预留)" />-->
        <el-table-column prop="ciTime" label="ci时间" />
        <el-table-column prop="createTime" label="创建日期" />
        <el-table-column prop="modifyTime" label="更新日期" />
        <el-table-column v-if="checkPer(['admin','reasoningCiInfo:edit','reasoningCiInfo:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
        <el-table-column label="" width="100px" align="right">
          <template slot-scope="scope">
            <el-button type="success" size="mini" @click="showLineage(scope.row.projectName,scope.row.commitId)">血缘查询</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
    <el-dialog title="相关任务" :visible.sync="dialogTableVisible" :close-on-click-modal="false" width="60%" top="10vh">
      <el-table :data="gridData" stripe style="width: 100%">
        <el-table-column prop="jobId" label="作业id" width="100" sortable />
        <el-table-column prop="jobName" label="作业名称" width="300" sortable />
        <el-table-column prop="createUserName" label="创建人" width="100" sortable />
        <el-table-column
          prop="status"
          label="运行状态"
          sortable
          align="left"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag type="info" :style="setStatusFontColor(scope.row.jobStatus)" size="medium">{{ getJobStatusDesc(scope.row.jobStatus) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="libraOwers" label="负责人" width="200" sortable />
        <el-table-column
          prop="jobCreateTime"
          label="作业创建时间"
          sortable
          align="left"
          width="200"
        />
        <el-table-column
          prop="jobUpdateTime"
          label="作业更新时间"
          sortable
          align="left"
          width="200"
        />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import crudreasoningCiInfo from '@/api/reasoningCiInfo'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { getAlgoTransformationImageLineage } from '@/api/job'
import { getJobStatusDesc } from '@/views/realtime/js/common'

const defaultForm = { id: null, commitId: null, commitMessage: null, projectId: null, projectName: null, configData: null, ext: null, ciTime: null, createTime: null, modifyTime: null }
export default {
  name: 'ReasoningCiInfo',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'ciconfig', url: 'api/reasoningCiInfo', idField: 'id', sort: 'id,desc', crudMethod: { ...crudreasoningCiInfo }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'reasoningCiInfo:add'],
        edit: ['admin', 'reasoningCiInfo:edit'],
        del: ['admin', 'reasoningCiInfo:del']
      },
      dialogTableVisible: false,
      gridData: [{
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }],
      rules: {
        commitId: [
          { required: true, message: 'gitlab的commitid不能为空', trigger: 'blur' }
        ],
        commitMessage: [
          { required: true, message: 'gitlab 的提交信息不能为空', trigger: 'blur' }
        ],
        projectId: [
          { required: true, message: 'gitlab的项目id不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: 'gitlab的项目名称不能为空', trigger: 'blur' }
        ],
        configData: [
          { required: true, message: 'json配置不能为空', trigger: 'blur' }
        ],
        ext: [
          { required: true, message: '额外配置(预留)不能为空', trigger: 'blur' }
        ],
        ciTime: [
          { required: true, message: 'ci时间不能为空', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '创建日期不能为空', trigger: 'blur' }
        ],
        modifyTime: [
          { required: true, message: '更新日期不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    showLineage(projectName, commitId) {
      this.getAlgoTransformationImageLineage(projectName, commitId)
    },
    getAlgoTransformationImageLineage(image, tag) {
      getAlgoTransformationImageLineage(image, tag).then(res => {
        this.dialogTableVisible = true
        if (res.code === '200') {
          this.fillGridData(res.data)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    fillGridData(list) {
      this.gridData = list
    },
    setStatusFontColor(jobStatus) {
      switch (jobStatus) {
        case 0:
          return 'color: #930dd3'
        case 1:
          return 'color: #12AAAD'
        case 3:
          return 'color: red'
        case 6:
          return 'color: #221b0a'
        default:
          return 'color: #bcbdae'
      }
    },
    getJobStatusDesc(statusCode) {
      return getJobStatusDesc(statusCode)
    },
  }
}
</script>

<style scoped>

</style>
