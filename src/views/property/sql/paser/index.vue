<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="自增id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="表数量" prop="tableNum">
            <el-input v-model="form.tableNum" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="目标表数量" prop="targetTableNum">
            <el-input v-model="form.targetTableNum" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="作业名称" prop="jobName">
            <el-input v-model="form.jobName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="修改时间" prop="modifyTime">
            <el-input v-model="form.modifyTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="作业归属人" prop="creator">
            <el-input v-model="form.creator" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="空间名称" prop="projectName">
            <el-input v-model="form.projectName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="作业id" prop="jobId">
            <el-input v-model="form.jobId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="解析状态" prop="parserStatus">
            <el-input v-model="form.parserStatus" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="自增id" />
        <el-table-column prop="tableNum" label="表数量" />
        <el-table-column prop="targetTableNum" label="目标表数量" />
        <el-table-column prop="jobName" label="作业名称" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="modifyTime" label="修改时间" />
        <el-table-column prop="creator" label="作业归属人" />
        <el-table-column prop="projectName" label="空间名称" />
        <el-table-column prop="jobId" label="作业id" />
        <el-table-column prop="parserStatus" label="解析状态" />
        <el-table-column v-if="checkPer(['admin','flinkSqlParserStatus:edit','flinkSqlParserStatus:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudFlinkSqlParserStatus from '@/api/flinkSqlParserStatus'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, tableNum: null, targetTableNum: null, jobName: null, createTime: null, modifyTime: null, creator: null, projectName: null, jobId: null, parserStatus: null }
export default {
  name: 'FlinkSqlParserStatus',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '血缘解析状态', url: 'api/flinkSqlParserStatus', idField: 'id', sort: 'id,desc', crudMethod: { ...crudFlinkSqlParserStatus }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'flinkSqlParserStatus:add'],
        edit: ['admin', 'flinkSqlParserStatus:edit'],
        del: ['admin', 'flinkSqlParserStatus:del']
      },
      rules: {
        tableNum: [
          { required: true, message: '表数量不能为空', trigger: 'blur' }
        ],
        targetTableNum: [
          { required: true, message: '目标表数量不能为空', trigger: 'blur' }
        ],
        jobName: [
          { required: true, message: '作业名称不能为空', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' }
        ],
        modifyTime: [
          { required: true, message: '修改时间不能为空', trigger: 'blur' }
        ],
        creator: [
          { required: true, message: '作业归属人不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '空间名称不能为空', trigger: 'blur' }
        ],
        jobId: [
          { required: true, message: '作业id不能为空', trigger: 'blur' }
        ],
        parserStatus: [
          { required: true, message: '解析状态不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
