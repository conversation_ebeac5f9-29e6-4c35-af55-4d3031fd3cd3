import request from '@/utils/request'

// 公共属性分页查询
export function queryAttributePageable(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/publicAttribute/queryAttributePageable' + pathParam + '&sort=updateTime%2Cdesc',
    method: 'get'
  })
}

// 创建公共属性
export function createAttribute(param) {
  return request({
    url: 'api/publicAttribute/create',
    method: 'post',
    data: param
  })
}

// 修改公共属性
export function updateAttribute(param) {
  return request({
    url: 'api/publicAttribute/update',
    method: 'post',
    data: param
  })
}

// 删除公共属性
export function deleteAttribute(jobId) {
  const ids = []
  ids.push(jobId)
  return request({
    url: 'api/publicAttribute/delete',
    method: 'delete',
    data: ids
  })
}

// 查询公共属性已经关联的topic
export function getConnectTopics(attributeId) {
  return request({
    url: 'api/publicAttribute/relation/table?attributeId=' + attributeId + '&isRelationAttribute=1',
    method: 'get'
  })
}

// 查询公共属性还未关联的topic
export function getDisConnectTopics(attributeId) {
  return request({
    url: 'api/publicAttribute/relation/table?attributeId=' + attributeId + '&isRelationAttribute=0',
    method: 'get'
  })
}

// 批量更新topic属性描述
export function updateConnectBatch(param) {
  return request({
    url: 'api/publicAttribute/relation/batchUpdateTable',
    method: 'post',
    data: param
  })
}
