<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">topic</label>
        <el-input v-model="query.topic" clearable placeholder="topic" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">cluster</label>
        <el-input v-model="query.cluster" clearable placeholder="cluster" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">status</label>
        <el-input v-model="query.status" clearable placeholder="status" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="自增id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="topic" prop="topic">
            <el-input v-model="form.topic" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="description" prop="description">
            <el-input v-model="form.description" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="cluster" prop="cluster">
            <el-input v-model="form.cluster" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="status" prop="status">
            <el-input v-model="form.status" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="partition" prop="partition">
            <el-input v-model="form.partition" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="repli" prop="repli">
            <el-input v-model="form.repli" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="username" prop="username">
            <el-input v-model="form.username" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="created" prop="created">
            <el-input v-model="form.created" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="modify" prop="modify">
            <el-input v-model="form.modify" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="namespace" prop="namespace">
            <el-input v-model="form.namespace" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建者" prop="creator">
            <el-input v-model="form.creator" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="修改时间" prop="modifyTime">
            <el-input v-model="form.modifyTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="修改者" prop="modifier">
            <el-input v-model="form.modifier" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="显示的topic名称" prop="topicDisp">
            <el-input v-model="form.topicDisp" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="占用磁盘空间" prop="diskSize">
            <el-input v-model="form.diskSize" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="磁盘保存时间ms" prop="retentionMs">
            <el-input v-model="form.retentionMs" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="自增id" />
        <el-table-column prop="topic" label="topic" />
        <el-table-column prop="description" label="description" />
        <el-table-column prop="cluster" label="cluster" />
        <el-table-column prop="status" label="status" />
        <el-table-column prop="partition" label="partition" />
        <el-table-column prop="repli" label="repli" />
        <el-table-column prop="username" label="username" />
        <el-table-column prop="created" label="created" />
        <el-table-column prop="modify" label="modify" />
        <el-table-column prop="namespace" label="namespace" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="creator" label="创建者" />
        <el-table-column prop="modifyTime" label="修改时间" />
        <el-table-column prop="modifier" label="修改者" />
        <el-table-column prop="topicDisp" label="显示的topic名称" />
        <el-table-column prop="diskSize" label="占用磁盘空间" />
        <el-table-column prop="retentionMs" label="磁盘保存时间ms" />
        <el-table-column v-if="checkPer(['admin','keTopic:edit','keTopic:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudKeTopic from '@/api/keTopic'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, topic: null, description: null, cluster: null, status: null, partition: null, repli: null, username: null, created: null, modify: null, namespace: null, createTime: null, creator: null, modifyTime: null, modifier: null, topicDisp: null, diskSize: null, retentionMs: null }
export default {
  name: 'KeTopic',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'dmq管理', url: 'api/keTopic', idField: 'id', sort: 'id,desc', crudMethod: { ...crudKeTopic }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'keTopic:add'],
        edit: ['admin', 'keTopic:edit'],
        del: ['admin', 'keTopic:del']
      },
      rules: {
        topic: [
          { required: true, message: 'topic不能为空', trigger: 'blur' }
        ],
        description: [
          { required: true, message: 'description不能为空', trigger: 'blur' }
        ],
        cluster: [
          { required: true, message: 'cluster不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: 'status不能为空', trigger: 'blur' }
        ],
        partition: [
          { required: true, message: 'partition不能为空', trigger: 'blur' }
        ],
        repli: [
          { required: true, message: 'repli不能为空', trigger: 'blur' }
        ],
        username: [
          { required: true, message: 'username不能为空', trigger: 'blur' }
        ],
        created: [
          { required: true, message: 'created不能为空', trigger: 'blur' }
        ],
        modify: [
          { required: true, message: 'modify不能为空', trigger: 'blur' }
        ],
        namespace: [
          { required: true, message: 'namespace不能为空', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' }
        ],
        creator: [
          { required: true, message: '创建者不能为空', trigger: 'blur' }
        ],
        modifyTime: [
          { required: true, message: '修改时间不能为空', trigger: 'blur' }
        ],
        modifier: [
          { required: true, message: '修改者不能为空', trigger: 'blur' }
        ],
        topicDisp: [
          { required: true, message: '显示的topic名称不能为空', trigger: 'blur' }
        ],
        diskSize: [
          { required: true, message: '占用磁盘空间不能为空', trigger: 'blur' }
        ],
        retentionMs: [
          { required: true, message: '磁盘保存时间ms不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'topic', display_name: 'topic' },
        { key: 'cluster', display_name: 'cluster' },
        { key: 'status', display_name: 'status' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
