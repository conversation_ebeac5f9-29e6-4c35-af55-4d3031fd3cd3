<template>
  <div class="app-container">
    <el-dialog
      :title="jobName"
      :visible="tag"
      width="70%"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >

      <div class="main-div">
        <el-row :gutter="6" style="margin-top: -25px;margin-bottom: 3px">
          <el-col :span="24/sourceTableNameList.length" v-for="sourceTableName in sourceTableNameList">
            <el-card>
              <div slot="header">
                <span style="font-size: medium"><b>数据源表: {{sourceTableName}}</b> </span>
                <el-button  @click="addItem(sourceTableName)" style="float: right;padding: 3px 0;font-size: medium" type="text">新增一条数据</el-button>
              </div>
              <div v-for="(item,index) in tableContentItemList" :key="index">
                <div v-if="item.tableName === sourceTableName">
                  <el-row :gutter="5" style="margin-bottom: 2px;">
                    <el-col :span="20">
                      <el-input
                        type="textarea"
                        :rows="1"
                        @blur="touchBlur(item.inputValue)"
                        placeholder="请输入一条json格式记录"
                        v-model="item.inputValue"
                      />
                    </el-col>
                    <el-col :span="4">
                      <el-button @click="deleteItem(item, index)" type="text" style="color: #C03639;font-size: medium">删除</el-button>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="6">
          <el-col :span="24/dimTableNameList.length" v-for="dimTableName in dimTableNameList" v-if="isHasDimTable==='1'">
            <el-card>
              <div slot="header">
                <span style="font-size: medium"><b>维度表: {{dimTableName}}</b> </span>
                <el-button  @click="addItem(dimTableName)" style="float: right;padding: 3px 0;font-size: medium" type="text">新增一条数据</el-button>
              </div>
              <div v-for="(item,index) in tableContentItemList" :key="index">
                <div v-if="item.tableName === dimTableName">
                  <el-row :gutter="5" style="margin-bottom: 2px;">
                    <el-col :span="20">
                      <el-input
                        type="textarea"
                        :rows="1"
                        placeholder="请输入一条json格式记录"
                        @blur="touchBlur(item.inputValue)"
                        v-model="item.inputValue"
                      />
                    </el-col>
                    <el-col :span="4">
                      <el-button @click="deleteItem(item, index)" type="text" style="color: #C03639;font-size: medium">删除</el-button>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-divider style="background-color: rgb(240, 242, 245)"><el-button :disabled="executeDqcButtonIsDisabled" type="primary" plain @click="dqcRun" :loading="executeDqcButtonIsLoading === true && executeDqcValidate === true">执行数据演练~</el-button></el-divider>
        <el-row style="margin-bottom: -25px">
          <el-col :span="24">
            <el-skeleton v-if="skeletonIsShow === '1'" :rows="6" animated />
            <div class="grid-content bg-purple-light" v-if="jsonViewIsShow === '1'">
              <json-viewer style="background-color: rgb(240, 242, 245)" :value="dqcResultJson" :expand-depth="expandDepth" :copyable="copyConfig" theme="jv-light" />
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onClose('1')">退 出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { get_job_dqc_res, get_dqc_res } from '@/api/job'

  export default {
    name: 'DQC',
    components: {
    },
    props: {
      isShow: {
        type: Boolean,
        default: false
      },
      jobId:{
        type: Number,
        default: 0
      },
      jobName:{
        type: String,
        default: 0
      },
      sourceTableNameList:{
        type: Array,
        default: []
      },
      dimTableNameList:{
        type: Array,
        default: []
      },
      isHasDimTable:{
        type: String,
        default: '0'
      }
    },
    data() {
      return {
        tableContentItemList: [],
        dqcResultJson: [],
        expandDepth: 10,
        copyConfig: { copyText: '复制', copiedText: '复制成功' },
        skeletonIsShow: '0',
        jsonViewIsShow: '0',
        dqcRequestBody: {'isDataDrill':true,'jobId':0,'sourceData':[]},
        executeDqcButtonIsDisabled: false,
        executeDqcButtonIsLoading: false,
        executeDqcValidate: false,
        timer: null
      }
    },
    computed: {
      tag() {
        return this.isShow
      },
    },
    watch: {
    },
    mounted() {
    },
    created() {
    },
    methods: {
      onClose(tag) {
        this.clearData()
        this.$emit('close', tag)
      },
      clearData(){
        this.tableContentItemList = []
        this.dqcResultJson = []
        this.dqcRequestBody = {'isDataDrill':true,'jobId':0,'sourceData':[]}
        this.skeletonIsShow = '0'
        this.jsonViewIsShow = '0'
        this.executeDqcButtonIsDisabled = false
      },
      addItem(sourceTableName) {
        this.tableContentItemList.push({
          tableName: sourceTableName,
          inputValue: ""
        });
      },
      deleteItem(item, index) {
        this.tableContentItemList.splice(index, 1);
      },
      validateInputTableDataList(){
        try{
          if(this.tableContentItemList.length === 0){
            throw new Error('请先填写数据记录在进行数据演练操作 ！！！')
          }
        }catch (e) {
          this.$message.error(e.message)
          return '0'
        }
        try{
          this.tableContentItemList.forEach(item=>{
            if(item.inputValue === ''){
              throw new Error('请将空白输入框填写完毕再进行数据演练操作 ！！！')
            }
          })
        }catch (e) {
          this.$message.error(e.message)
          return '0'
        }
        try{
          this.tableContentItemList.forEach(item=>{
            if(item.inputValue !== ''){
              if(item.inputValue === '{}'){
                throw new Error('请输入完成的json ！！！')
              }else{
                JSON.parse(item.inputValue)
              }
            }
          })
        }catch (e) {
          this.$message.error("请正确填写JSON格式的数据在进行数据演练操作 ！！！")
          return '0'
        }
        return '1'
      },
      dqcRun(){
        this.executeDqcButtonIsLoading = true
        // 校验数据
        const validateCode = this.validateInputTableDataList()
        if(validateCode === '1'){ //校验通过
          this.executeDqcValidate = true

          // 拼接数据
          const jsonToRequest = this.prepareInputTableRecord()

          //请求dqc执行
          get_job_dqc_res(jsonToRequest).then(res=>{
            this.executeDqcButtonIsLoading = false
            this.executeDqcValidate = true
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '数据演练开始执行~'
              })
              this.executeDqcButtonIsDisabled = true
              this.skeletonIsShow = '1'
              this.jsonViewIsShow = '0'

              const currentJobName = res.data.jobName
              const currentSessionId = res.data.sessionId

              this.timer = setInterval(()=>{
              get_dqc_res(currentJobName,currentSessionId).then(res=>{
                if(res.code === '200'){
                  if(res.data.code === 300){
                    console.log(res)
                  }
                  if(res.data.code === 200){
                    this.skeletonIsShow = '0'
                    this.jsonViewIsShow = '1'
                    this.executeDqcButtonIsDisabled = false
                    this.dqcResultJson = JSON.parse(res.data.actualData)
                    clearInterval(this.timer)
                  }
                }
                if (res.code === '500') {
                  this.$message({
                    type: 'error',
                    message: res.mes
                  })
                }
              })
              },3000);
            }
          })
        }
      },
      prepareInputTableRecord(){
        this.dqcRequestBody.sourceData = []
        this.sourceTableNameList.forEach(ss=>{
          this.tableContentItemList.forEach(item=>{
            if(item.tableName === ss){
              if(this.isItemInList(ss) === '0'){
                const ttInput = []
                ttInput.push(JSON.parse(item.inputValue))
                this.dqcRequestBody.sourceData.push({'dataName':ss,'data':ttInput})
              }else{
                const idx = this.getItemIndex(ss)
                const formerValue = this.dqcRequestBody.sourceData[idx - 0].data
                const tempInput = []
                tempInput.push(JSON.parse(item.inputValue))
                const tempData = formerValue.concat(tempInput)
                const tempFullData = {'dataName':'', 'data':[]}
                tempFullData.dataName = ss
                tempFullData.data = tempData
                this.dqcRequestBody.sourceData.splice(idx - 0,1)
                this.dqcRequestBody.sourceData.push(tempFullData)
              }
            }
          })
        })

        this.dimTableNameList.forEach(ss=>{
          this.tableContentItemList.forEach(item=>{
            if(item.tableName === ss){
              if(this.isItemInList(ss) === '0'){
                const ttInput = []
                ttInput.push(JSON.parse(item.inputValue))
                this.dqcRequestBody.sourceData.push({'dataName':ss,'data':ttInput})
              }else{
                const idx = this.getItemIndex(ss)
                const formerValue = this.dqcRequestBody.sourceData[idx - 0].data
                const tempInput = []
                tempInput.push(JSON.parse(item.inputValue))
                const tempData = formerValue.concat(tempInput)
                const tempFullData = {'dataName':'', 'data':[]}
                tempFullData.dataName = ss
                tempFullData.data = tempData
                this.dqcRequestBody.sourceData.splice(idx - 0,1)
                this.dqcRequestBody.sourceData.push(tempFullData)
              }
            }
          })
        })

        this.dqcRequestBody.jobId = this.jobId
        return JSON.stringify(this.dqcRequestBody)
      },
      isItemInList(tableName){
        try{
          this.dqcRequestBody.sourceData.forEach((item,index)=>{
            if(tableName === item.dataName){
              throw new Error('1')
            }
          })
          throw new Error('0')
        }catch (e) {
          return e.message
        }
      },
      getItemIndex(tableName){
        try{
          this.dqcRequestBody.sourceData.forEach((item,index)=>{
            if(tableName === item.dataName){
              throw new Error(index + '')
            }
          })
        }catch (e) {
          return e.message
        }
      },
      touchBlur(inputValue){
        try {
          if(inputValue !== ''){
            if(inputValue === '{}'){
              throw new Error('请填写完整的JSON格式数据')
            }else{
              JSON.parse(inputValue)
            }
          }
        } catch (e) {
          this.$message(inputValue + ' 不是完整JSON格式数据，请重新填写 ！！！')
        }
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .el-row {
    margin-bottom: 1px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-col {
    border-radius: 4px;
  }

  .main-div {
    background: rgb(240, 242, 245);
  }

  .el-divider__text {
    background: rgb(240, 242, 245);
  }

  .grid-content {
    border-radius: 2px;
    min-height: 180px;
  }

  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

</style>
