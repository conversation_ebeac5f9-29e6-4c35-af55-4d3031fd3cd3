import request from '@/utils/request'


// 任务分页查询
export function queryJobPageable(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/dts/queryJobPageable' + pathParam,
    method: 'get'
  })
}

export function queryJobPageableSgp(param) {
  let pathParam = ''

  param.forEach((item, index) => {
    if (item.paramValue !== '') {
      if (index === 0) {
        pathParam = pathParam + '?' + item.paramName + '=' + item.paramValue
      }
      if (index !== 0) {
        pathParam = pathParam + '&' + item.paramName + '=' + item.paramValue
      }
    }
  })

  return request({
    url: 'api/dts/sgp/queryJobPageable' + pathParam,
    method: 'get'
  })
}

// 校验tableRegex
export function checkTableRegex(tableRegex) {
  return request({
    url: 'api/dts/checkTableRegex',
    method: 'post',
    data: tableRegex
  })
}

export function checkTableRegexSgp(tableRegex) {
  return request({
    url: 'api/dts/sgp/checkTableRegex',
    method: 'post',
    data: tableRegex
  })
}

// 创建任务
export function createJob(params) {
  return request({
    url: 'api/dts/create',
    method: 'post',
    data: params
  })
}

export function createJobSgp(params) {
  return request({
    url: 'api/dts/sgp/create',
    method: 'post',
    data: params
  })
}

// 启动任务
export function startJob(taskId, runningStatus) {
  return request({
    url: 'api/dts/start/' + taskId + '/' + runningStatus,
    method: 'get'
  })
}

export function startJobSgp(taskId, runningStatus) {
  return request({
    url: 'api/dts/sgp/start/' + taskId + '/' + runningStatus,
    method: 'get'
  })
}

// 批量创建dts
export function createJobBatch(param) {
  return request({
    url: 'api/dts/createBatch',
    method: 'post',
    data: param
  })
}
