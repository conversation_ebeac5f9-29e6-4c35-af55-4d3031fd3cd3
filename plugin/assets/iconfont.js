 /* eslint-disable */
(function(window){var svgSprite='<svg><symbol id="icon-quanping" viewBox="0 0 1025 1024"><path d="M1014.331575 1013.87659C1009.09925 1020.075757 1001.421383 1024 991.98045 1024L703.975118 1024C686.287587 1024 671.955568 1009.667981 671.955568 991.98045 671.955568 974.292919 686.287587 960.017773 703.975118 960.017773L914.348681 960.984615 569.185893 615.821827 614.4 570.550847 959.9609 916.111747 959.9609 704.031991C959.9609 686.34446 974.292919 672.012441 991.98045 672.012441 1009.611108 672.012441 1023.943127 686.34446 1023.943127 704.031991L1024 991.07048C1024 1000.056429 1020.246376 1008.075535 1014.331575 1013.87659ZM991.98045 352.044432C974.292919 352.044432 959.9609 337.712413 959.9609 320.024882L960.984615 109.651319 615.764954 454.814107 570.550847 409.543127 916.054874 64.0391 703.975118 64.0391C686.287587 64.0391 671.955568 49.707081 671.955568 32.01955 671.955568 14.332019 686.287587 0 703.975118 0L991.07048 0C999.999556 0 1008.075535 3.753624 1013.87659 9.668425 1020.018884 14.90075 1023.943127 22.521744 1023.943127 32.01955L1023.943127 320.024882C1023.943127 337.712413 1009.611108 352.044432 991.98045 352.044432ZM409.543127 453.449153 63.982227 107.888253 63.982227 320.024882C63.982227 337.712413 49.650208 352.044432 32.01955 352.044432 14.332019 352.044432 0 337.712413 0 320.024882L0 32.92952C0 24.000444 3.696751 15.924465 9.611552 10.12341 14.843877 3.924243 22.521744 0 32.01955 0L319.968009 0C337.65554 0 351.987559 14.332019 351.987559 32.01955 351.987559 49.707081 337.65554 64.0391 319.968009 64.0391L109.594446 63.015385 454.814107 408.235046 409.543127 453.449153ZM32.01955 672.012441C49.650208 672.012441 63.982227 686.34446 63.982227 704.031991L63.015385 914.405554 408.178173 569.185893 453.449153 614.456873 107.888253 960.017773 319.968009 960.017773C337.65554 960.017773 351.987559 974.349792 351.987559 991.98045 351.987559 1009.667981 337.65554 1024 319.968009 1024L32.92952 1024C23.943571 1024 15.924465 1020.303249 10.12341 1014.388448 3.924243 1009.09925 0 1001.478256 0 991.98045L0 704.031991C0 686.34446 14.332019 672.012441 32.01955 672.012441Z"  ></path></symbol><symbol id="icon-fangda" viewBox="0 0 1024 1024"><path d="M801.895 481.895h-259.79v-259.79C542.105 205.479 528.627 192 512 192s-30.105 13.479-30.105 30.105v259.789h-259.79C205.479 481.895 192 495.373 192 512c0 16.627 13.479 30.105 30.105 30.105h259.789v259.789C481.895 818.521 495.373 832 512 832s30.105-13.479 30.105-30.105v-259.79h259.789C818.521 542.105 832 528.627 832 512c0-16.627-13.479-30.105-30.105-30.105z"  ></path></symbol><symbol id="icon-suoxiao" viewBox="0 0 1024 1024"><path d="M801.895 542.105h-579.79C205.479 542.105 192 528.627 192 512c0-16.627 13.479-30.105 30.105-30.105h579.789C818.521 481.895 832 495.373 832 512c0 16.627-13.479 30.105-30.105 30.105z"  ></path></symbol><symbol id="icon-icon-test" viewBox="0 0 1024 1024"><path d="M945.4 485.8h-53.9c-12.9-189-164.4-340.4-353.3-353.3v-42c0-14.4-11.8-26.2-26.2-26.2-14.4 0-26.2 11.8-26.2 26.2v42c-188.9 12.9-340.4 164.4-353.3 353.3H78.6c-14.4 0-26.2 11.8-26.2 26.2s11.8 26.2 26.2 26.2h53.9c12.9 188.9 164.4 340.4 353.3 353.3v42c0 14.4 11.8 26.2 26.2 26.2 14.4 0 26.2-11.8 26.2-26.2v-42c188.9-12.9 340.4-164.4 353.3-353.3h53.9c14.4 0 26.2-11.8 26.2-26.2s-11.8-26.2-26.2-26.2zM538.2 838.9V671.1c0-14.4-11.8-26.2-26.2-26.2-14.4 0-26.2 11.8-26.2 26.2v167.8c-160-12.7-287.9-140.7-300.7-300.7h156c14.4 0 26.2-11.8 26.2-26.2s-11.8-26.2-26.2-26.2h-156c12.7-160 140.7-287.9 300.7-300.7v167.8c0 14.4 11.8 26.2 26.2 26.2 14.4 0 26.2-11.8 26.2-26.2V185.1c160 12.7 287.9 140.7 300.7 300.7H683c-14.4 0-26.2 11.8-26.2 26.2s11.8 26.2 26.2 26.2h156c-12.8 160-140.8 288-300.8 300.7z" fill="#666666" ></path></symbol><symbol id="icon-duoxuankuang" viewBox="0 0 1024 1024"><path d="M140.8 140.8v742.4h742.4V140.8zM76.8 128a51.2 51.2 0 0 1 51.2-51.2h768a51.2 51.2 0 0 1 51.2 51.2v768a51.2 51.2 0 0 1-51.2 51.2h-768a51.2 51.2 0 0 1-51.2-51.2z" fill="#B9B9CF" ></path></symbol></svg>';var script=function(){var scripts=document.getElementsByTagName("script");return scripts[scripts.length-1]}();var shouldInjectCss=script.getAttribute("data-injectcss");var ready=function(fn){if(document.addEventListener){if(~["complete","loaded","interactive"].indexOf(document.readyState)){setTimeout(fn,0)}else{var loadFn=function(){document.removeEventListener("DOMContentLoaded",loadFn,false);fn()};document.addEventListener("DOMContentLoaded",loadFn,false)}}else if(document.attachEvent){IEContentLoaded(window,fn)}function IEContentLoaded(w,fn){var d=w.document,done=false,init=function(){if(!done){done=true;fn()}};var polling=function(){try{d.documentElement.doScroll("left")}catch(e){setTimeout(polling,50);return}init()};polling();d.onreadystatechange=function(){if(d.readyState=="complete"){d.onreadystatechange=null;init()}}}};var before=function(el,target){target.parentNode.insertBefore(el,target)};var prepend=function(el,target){if(target.firstChild){before(el,target.firstChild)}else{target.appendChild(el)}};function appendSvg(){var div,svg;div=document.createElement("div");div.innerHTML=svgSprite;svgSprite=null;svg=div.getElementsByTagName("svg")[0];if(svg){svg.setAttribute("aria-hidden","true");svg.style.position="absolute";svg.style.width=0;svg.style.height=0;svg.style.overflow="hidden";prepend(svg,document.body)}}if(shouldInjectCss&&!window.__iconfont__svg__cssinject__){window.__iconfont__svg__cssinject__=true;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}ready(appendSvg)})(window)