<template>
  <div class="app-container">
    <el-dialog
      title="聚合操作配置"
      :visible.sync="tag"
      width="50%"
      :before-close="onClose"
      :close-on-click-modal="false"
      :show-close="false"
      top="10vh"
    >
      <div class="realtime-groupBy-dialog-content">
        <el-row :gutter="12" style="margin-bottom: 30px;margin-top: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>选择字段:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="selectExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> username,age,count(1) as cnt"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px;">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span style="color: red">* </span><span>聚合维度:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="groupByExpression"
                placeholder="example=> username,age"
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span>分组前条件[where]:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="filterExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> coalesce(username) <> '' and age >= 18 "
                clearable
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="12" style="margin-bottom: 30px">
          <el-col :span="6">
            <div class="col-content element-desc">
              <span>分组后条件[having]:</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="col-content element-value">
              <el-input
                v-model="havingExpression"
                type="textarea"
                :rows="1"
                autosize
                placeholder="example=> count(1) <= 10"
                clearable
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RealtimeGroupBy',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectExpression: '',
      groupByExpression: '',
      filterExpression: '',
      havingExpression: '',
      showOperation: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'GroupByTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        if (sessionStorage.getItem('editType') === 'GroupByTransformation') {
          if (sessionStorage.getItem('editJsonContent') !== '""') {
            this.selectExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).selectExpression
            this.groupByExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).groupByExpression
            this.filterExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).filterExpression
            this.havingExpression = JSON.parse(sessionStorage.getItem('editJsonContent')).havingExpression
          }
        }
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(tag) {
      const resultJson = { 'selectExpression': '', 'groupByExpression': '', 'filterExpression': '', 'havingExpression': '', 'transformation': '', 'showOperation': '' }

      const transformation = { 'selectExpression': '', 'groupByExpression': '', 'filterExpression': '', 'havingExpression': '' }
      transformation.selectExpression = this.selectExpression.trim().replace(/\n/g, '')
      transformation.groupByExpression = this.groupByExpression.trim()
      transformation.filterExpression = this.filterExpression.trim().replace(/\n/g, ' ')
      transformation.havingExpression = this.havingExpression.trim().replace(/\n/g, ' ')

      resultJson.selectExpression = this.selectExpression.trim()
      resultJson.groupByExpression = this.groupByExpression.trim()
      resultJson.filterExpression = this.filterExpression.trim()
      resultJson.havingExpression = this.havingExpression.trim()
      resultJson.transformation = JSON.stringify(transformation)

      this.showOperation = []
      this.showOperation.push('【选择字段】' + this.selectExpression.trim().replace(/\n/g, ''))
      this.showOperation.push('【聚合维度】' + this.groupByExpression.trim())
      this.showOperation.push('【分组前条件[where]】' + this.filterExpression.trim().replace(/\n/g, ' '))
      this.showOperation.push('【分组后条件[having]】' + this.havingExpression.trim().replace(/\n/g, ' '))
      resultJson.showOperation = JSON.stringify(this.showOperation)

      this.$emit('close', resultJson, this.isFullEdit(), tag)
      this.clearData()
    },
    clearData() {
      this.selectExpression = ''
      this.groupByExpression = ''
      this.filterExpression = ''
      this.havingExpression = ''
    },
    isFullEdit() {
      if (this.selectExpression.trim() === '' || this.groupByExpression.trim() === '') {
        return '0'
      } else {
        return '1'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .realtime-groupBy-dialog-content{
    background-color: rgb(240, 242, 245);
    width: auto;
    margin-top: -25px;
    margin-bottom: -25px;
    padding: 32px;
  }

  .col-content{
    min-height: 30px;
  }

  .element-desc{
    text-align: right;
    margin-top: auto;
    font-size: medium;
    line-height: 30px;
  }

</style>

