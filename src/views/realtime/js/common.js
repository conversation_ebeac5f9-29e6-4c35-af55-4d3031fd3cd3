// 根据数据源名称获取数据源类型名称
export function getDataSourceTypeName(dataSourceName) {
  try {
    JSON.parse(sessionStorage.getItem('sourceTypeNameMapping')).forEach(item => {
      if (item.dataSourceName === dataSourceName) {
        throw (item.dataSourceTypeName)
      }
    })
  } catch (e) {
    return e
  }
}

// 根据表id获取表名称
export function getTableNameByTableId(tableId, componentType) {
  if (componentType === 'SourceTransformation') {
    const tableIdAndTableNameArr = JSON.parse(sessionStorage.getItem('tableIdAndTableNameArrSource'))
    try {
      tableIdAndTableNameArr.forEach(item => {
        if (item.tableId === tableId) {
          throw (item.tableName)
        }
      })
    } catch (e) {
      return e
    }
  }

  if (componentType === 'SinkTransformation') {
    const tableIdAndTableNameArr = JSON.parse(sessionStorage.getItem('tableIdAndTableNameArrSink'))
    try {
      tableIdAndTableNameArr.forEach(item => {
        if (item.tableId === tableId) {
          throw (item.tableName)
        }
      })
    } catch (e) {
      return e
    }
  }

  if (componentType === 'LookupJoinTransformation') {
    const tableIdAndTableNameArr = JSON.parse(sessionStorage.getItem('tableIdAndTableNameArrLookupJoin'))
    try {
      tableIdAndTableNameArr.forEach(item => {
        if (item.tableId === tableId) {
          throw (item.tableName)
        }
      })
    } catch (e) {
      return e
    }
  }
}

// 将13位的时间戳转换为 yyyy-MM-dd hh:mm:ss 日期格式
export function timestampToTime(timestamp) {
  const date = new Date(timestamp)
  const y = date.getFullYear()
  let MM = date.getMonth() + 1
  MM = MM < 10 ? ('0' + MM) : MM
  let d = date.getDate()
  d = d < 10 ? ('0' + d) : d
  let h = date.getHours()
  h = h < 10 ? ('0' + h) : h
  let m = date.getMinutes()
  m = m < 10 ? ('0' + m) : m
  let s = date.getSeconds()
  s = s < 10 ? ('0' + s) : s
  return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
}

// 作业状态码和状态描述枚举
export function getJobStatusDesc(statusCode) {
  switch (statusCode) {
    case -1:
      return 'libra已删除'
    case 0:
      return '初始化'
    case 1:
      return '已创建'
    case 2:
      return '启动中'
    case 3:
      return '运行中'
    case 4:
      return '启动失败'
    case 5:
      return '下线中'
    case 6:
      return '已下线'
    case 7:
      return '运行异常'
    case 8:
      return '运行结束'
    case 9:
      return 'initializing'
    case 10:
      return 'created'
    case 11:
      return 'failing'
    case 12:
      return 'failed'
    case 13:
      return 'cancelling'
    case 14:
      return 'canceled'
    case 15:
      return 'finished'
    case 16:
      return 'restarting'
    case 17:
      return 'suspended'
    case 18:
      return '状态获取失败'
    case 19:
      return 'DEPLOY不存在'
    case 20:
      return 'K8S查询失败'
    case 21:
      return 'JM不存在'
    default:
      return '未知状态'
  }
}
