<template>
  <div class="app-container">

    <el-row :gutter="10" style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px" class="element-header-tool">
      <el-col :span="2.1">
        <div>
          <el-button size="small" type="primary" icon="el-icon-edit" @click="createJob">创建任务</el-button>
        </div>
      </el-col>
      <el-col :span="4">
        <div>
          <el-input
            v-model="topicBlurry"
            placeholder="topic"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="1.1">
        <div>
          <el-button size="small" type="success" icon="el-icon-search" @click="doSearch">搜索</el-button>
        </div>
      </el-col>
      <el-col :span="1.1">
        <div>
          <el-button size="small" type="warning" icon="el-icon-refresh-left" @click="clearBlurry">重置</el-button>
        </div>
      </el-col>
      <el-col :span="1.1" style="margin-left: 60px;height: 30px;align-content: center">
        <div>
          <el-link href="https://poizon.feishu.cn/docx/AMwTdmNEeohH33xwma0cs8JanKg" type="primary" target="_blank">Excel模版链接</el-link>
        </div>
      </el-col>
      <el-col :span="1.1">
        <el-upload
          ref="upload"
          class="upload-excel"
          action=""
          :show-file-list="true"
          :auto-upload="false"
          :limit="1"
          :on-exceed="alterFunction"
          :on-change="handleFileUpload"
          :before-upload="beforeUpload"
        >
          <el-button type="info">上传 Excel 文件</el-button>
        </el-upload>
      </el-col>
      <el-col :span="1.1">
        <div>
          <el-button size="small" type="primary" icon="el-icon-thumb" :loading="isCreateBatchLoading" @click="upload">执行批量操作</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col>
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          highlight-current-row
          :header-cell-style="{textAlign: 'left'}"
          size="medium"
          style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
        >
          <el-table-column
            prop="itemName"
            label="任务名称"
            fixed
            align="left"
            width="400"
          />
          <el-table-column
            prop="itemNo"
            label="任务编号"
            sortable
            align="left"
            width="300"
          />
          <el-table-column
            prop="sourceName"
            label="数据源实例"
            align="left"
            width="200"
          />
          <el-table-column
            prop="topic"
            label="topic"
            align="left"
            width="400"
          />
          <el-table-column
            prop="partition"
            label="分区数"
            align="left"
            width="100"
          />
          <el-table-column
            prop="tableRegex"
            label="生成表正则"
            align="left"
            width="400"
          />
          <el-table-column
            prop="creator"
            label="创建人"
            align="left"
            width="160"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            align="left"
            width="160"
          />
          <el-table-column
            prop="modifier"
            label="修改人"
            align="left"
            width="160"
          />
          <el-table-column
            prop="modifyTime"
            label="修改时间"
            align="left"
            width="160"
          />
          <el-table-column
            label="相关操作"
            width="100"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                class="el-icon-video-play"
                :loading="scope.row.startButtonLoading"
                @click="startJob(scope.row)"
              >启动</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>

    </el-row>

    <el-row style="margin-top: 20px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px">
      <el-col>
        <el-pagination
          background
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[10, 15, 30, 50]"
          class="page_tt"
          :total="page_info.total_num"
          :page-size="page_info.page_size"
          :current-page="page_info.current_page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
        />
      </el-col>
    </el-row>

    <dts-job-edit style="position: absolute" :is-show="isItemEditShow" :edit-json="fullItemJson" @close="closeDtsEditDialog" />

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import DtsJobEdit from './DtsJobEdit'
import { queryJobPageable, startJob, createJobBatch } from '@/api/dts'
import XLSX from 'xlsx'
export default {
  name: 'DtsIndex',
  components: {
    DtsJobEdit
  },
  props: {
  },
  data() {
    return {
      isDrawerShow: false,
      tableData:[],
      // tableData: [{"id":1157,"taskId":370,"itemNo":"MQ_SUB_item_230315_1353_tlUY5c","itemName":"du_sns_merc库merc_order_share_snapshot表binlog订阅","tableRegex":"du_sns_merc\\\\.merc_order_share_snapshot","topic":"ods_du_sns_merc_merc_order_share_snapshot","partition":1,"partitionKey":"$pk$","ignoreColumns":null,"creator":"wanglin","modifier":"wanglin","modifyTime":"2022-03-23 10:05:19","createTime":"2022-03-23 10:05:19","runningStatus":"running","jobId":"42d66c29b3761c7c4a0194135ee4fcac"}],
      page_info: {
        total_num: 0,
        current_page: 1,
        page_size: 10
      },
      loading: false,
      topicBlurry: '',
      isItemEditShow: false,
      fullItemJson: {"id":0,"taskId":0,"sourceName":'',"itemNo":"","itemName":"","tableRegex":"","topic":"","partition":'',"partitionKey":"$pk$"},
      isCreateBatchLoading: false

    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
  },
  watch: {
  },
  mounted() {
  },
  created() {
    this.doSearch()
  },
  methods: {
    createJob(){
      this.fullItemJson = {"id":0,"taskId":0,"sourceName":'',"itemNo":"","itemName":"","tableRegex":"","topic":"","partition":'',"partitionKey":"$pk$"}
      this.isItemEditShow = true
    },
    updateJob(row) {
      this.fullItemJson.id = row.id
      this.fullItemJson.taskId = row.taskId
      this.fullItemJson.taskNo = row.taskNo
      this.fullItemJson.itemName = row.itemName
      this.fullItemJson.tableRegex = row.tableRegex
      this.fullItemJson.sourceName = row.sourceName
      this.fullItemJson.topic = row.topic
      this.fullItemJson.partition = row.partition
      this.fullItemJson.partitionKey = row.partitionKey

      this.isItemEditShow = true
    },
    closeDtsEditDialog(tag){
      this.isItemEditShow = false
      if(tag === '1'){
        this.doSearch()
      }
    },
    doSearch(){
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
      pathParams.push({ 'paramName': 'topic', 'paramValue': this.topicBlurry })

      queryJobPageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })
    },
    fillTableData(data) {
      const tempTableData = []
      data.forEach(item => {
        tempTableData.push({ 'id': item.id, 'taskId': item.taskId, 'sourceName': item.sourceName,'itemNo': item.itemNo, 'itemName': item.itemName, 'tableRegex': item.tableRegex, 'topic': item.topic, 'partition': item.partition, 'partitionKey': item.partitionKey, 'ignoreColumns': item.ignoreColumns, 'creator': item.creator, 'modifier': item.modifier, 'modifyTime': item.modifyTime,'createTime':item.createTime, 'runningStatus': item.runningStatus, 'auditStatus':item.auditStatus, 'jobId':item.jobId, 'startButtonLoading':false })
      })
      this.tableData = tempTableData
    },
    clearBlurry() {
      this.topicBlurry = ''
    },
    handleSizeChange(val) {
      this.page_info.page_size = val
      this.doSearch()
    },
    handleCurrentChange(val) {
      this.page_info.current_page = val
      this.doSearch()
    },
    startJob(row) {
      row.startButtonLoading = true
      startJob(row.taskId,row.runningStatus).then(res=>{
        if (res.code === '200') {
          setTimeout(()=>{
            row.startButtonLoading = false
            this.$message({
              type: 'success',
              message: '启动任务发起成功~'
            })
            this.doSearch()
          }, 5000)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
          row.startButtonLoading = false
        }
      })
    },
    upload() {
      if (sessionStorage.getItem('sheetJsonDts') === null || sessionStorage.getItem('sheetJsonDts') === '') {
        this.$message({
          message: '请先上传文件再执行批处理！',
          type: 'warning'
        })
      } else {
        const pathParams = []
        pathParams.push({ 'excelData': sessionStorage.getItem('sheetJsonDts') })

        this.isCreateBatchLoading = true
        createJobBatch(pathParams).then(res => {
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '批量创建并启动成功~'
            })
            this.doSearch()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
          this.isCreateBatchLoading = false
        })
      }

      this.$refs.upload.clearFiles()
      sessionStorage.setItem('sheetJsonDts', '')
    },
    handleFileUpload(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const data = e.target.result
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const sheet = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName])
        // 在这里处理解析后的 Excel 数据（sheet）
        sessionStorage.setItem('sheetJsonDts', JSON.stringify(sheet))
      }
      reader.readAsArrayBuffer(file.raw)
    },
    beforeUpload(file) {
      // 校验文件类型，大小等，返回 true 表示允许上传
      return file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    },
    alterFunction() {
      this.$message({
        message: '单次只能上传一个文件！',
        type: 'warning'
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
</style>
