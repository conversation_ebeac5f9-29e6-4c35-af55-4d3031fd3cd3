<template>
  <div>
    <div class="container_warp">

      <!--  主要的画布  -->
      <div id="containerChart" />

      <!--  右侧的高级配置区域  -->
      <RightDrawer :is-show="superConfig" :super-config-param="superConfigParam" @close="receiveSuperConfig" />

      <!--  左上角的编辑按钮区域  -->
      <div class="operating">
        <div class="btn-group">
          <div class="component_btn" @mousedown="startDrag('SourceTransformation','数据源',$event)">
            数据源
          </div>
          <div class="component_btn" @mousedown="startDrag('CalcTransformation','转化过滤',$event)">
            转化过滤
          </div>
          <div class="component_btn" @mousedown="startDrag('SinkTransformation','结果表',$event)">
            结果表
          </div>
          <div class="component_btn" @mousedown="startDrag('GroupByTransformation','聚合',$event)">
            聚合
          </div>
          <div class="component_btn" @mousedown="startDrag('TopnTransformation','TopN',$event)">
            TopN
          </div>
          <div class="component_btn" @mousedown="startDrag('LookupJoinTransformation','维表join',$event)">
            维表join
          </div>
          <div class="component_btn" @mousedown="startDrag('JoinTransformation','双流join',$event)">
            双流join
          </div>
          <div class="component_btn" @mousedown="startDrag('UnionTransformation','UNION ALL',$event)">
            UNION ALL
          </div>
        </div>

        <div class="btn-group">
          <div class="btn_edit_config" :style="{backgroundColor: calcColor}" @click="showConfig()">
            作业高级配置
          </div>
        </div>

        <div class="btn-group">
          <div class="btn_job_info" style="background-color: #adceb3">
            作业名称：{{this.ruleForm.jobName}}
          </div>
        </div>
      </div>

      <!--  右上角的取消/保存按钮区域  -->
      <div class="operating_right">
        <div class="btn-group">
          <div class="btn_config_cancel" @click="closeDrawShow('0')">
            取消
          </div>
          <div class="btn_config_save" @click="closeDrawShow('1')">
            保存
          </div>
        </div>
      </div>
    </div>

    <!--  保存作业dialog配置区域  -->
    <el-dialog
      title="任务信息填写"
      :visible.sync="isSaveDialogShow"
      width="45%"
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="auto">
        <el-form-item label="任务名称:" prop="jobName">
          <div style="display: flex;">
            <el-input v-model="ruleForm.jobName" size="medium" placeholder="请输入任务名称" clearable :disabled="ruleForm.jobId !== 0" />
          </div>
        </el-form-item>
        <el-form-item label="任务描述:" prop="jobDesc">
          <el-input v-model="ruleForm.jobDesc" size="medium" placeholder="请输入任务描述" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="isSaveJobLoading" type="primary" @click="submitForm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>

    <!--  cell右键的选项区域  -->
    <div
      v-if="showMenu"
      class="context-menu"
      :style="{ top: `${contextMenuY}px`, left: `${contextMenuX}px`, width: '200px' }"
    >
      <div
        v-if="type === 'node'"
        :style="{ backgroundColor: editHighlighted ? 'rgba(10, 168, 236, 0.5)' : 'transparent' }"
        class="menu-item"
        @mouseover="highlightItem('edit')"
        @mouseleave="resetBackground"
        @click="editNode"
      >
        <span class="menu-text">编辑节点内容</span>
      </div>
      <div
        v-if="type === 'node'"
        :style="{ backgroundColor: copyHighlighted ? 'rgba(10, 168, 236, 0.5)' : 'transparent' }"
        class="menu-item"
        @mouseover="highlightItem('copy')"
        @mouseleave="resetBackground"
        @click="copyToClipboard(nodeIdCopy)"
      >
        <span class="menu-text">复制节点ID</span>
      </div>
      <div
        :style="{ backgroundColor: deleteHighlighted ? 'rgba(10, 168, 236, 0.5)' : 'transparent' }"
        class="menu-item"
        @mouseover="highlightItem('delete')"
        @mouseleave="resetBackground"
        @click="deleteNode"
      >
        <span class="menu-text">删除节点 / 边</span>
      </div>
    </div>

    <!--  引入的子组件  -->
    <drag-source style="position: absolute" :is-show="dialogVisibleDragSource" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />
    <drag-sink style="position: absolute" :is-show="dialogVisibleDragSink" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />
    <drag-calc style="position: absolute" :is-show="dialogVisibleDragCalc" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />
    <drag-group-by style="position: absolute" :is-show="dialogVisibleDragGroupBy" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />
    <drag-union style="position: absolute" :is-show="dialogVisibleDragUnion" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />
    <drag-top-n style="position: absolute" :is-show="dialogVisibleTopN" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />
    <drag-lookup-join style="position: absolute" :is-show="dialogVisibleLookupJoin" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />
    <drag-join style="position: absolute" :is-show="dialogVisibleJoin" :type="dragComponentType" :extend="dragComponentExtend" @close="handleDragComponent" />

  </div>
</template>

<script>
import '@antv/x6-vue-shape'
import { Graph, Shape, FunctionExt } from '@antv/x6'
import RightDrawer from './RightDrawer.vue'
import DragSink from './components/upgrade/DragSink.vue'
import DragSource from './components/upgrade/DragSource.vue'
import DragCalc from './components/upgrade/DragCalc.vue'
import DragGroupBy from './components/upgrade/DragGroupBy.vue'
import DragUnion from './components/upgrade/DragUnion.vue'
import DragTopN from './components/upgrade/DragTopN.vue'
import DragLookupJoin from './components/upgrade/DragLookupJoin.vue'
import DragJoin from './components/upgrade/DragJoin.vue'
import insertCss from 'insert-css'
import { startDragToGraph } from './js/methods'
import { saveJob, updateJob } from '@/api/job'
import Vue from 'vue'
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

export default {
  components: {
    RightDrawer,
    DragSource,
    DragSink,
    DragCalc,
    DragGroupBy,
    DragUnion,
    DragTopN,
    DragLookupJoin,
    DragJoin
  },
  props: {
    dragShow: {
      type: Boolean,
      default: false
    },
    fullJobInfo: {
      type: Object
    }
  },
  data() {
    return {

      showMenu: false,
      contextMenuY: 0,
      contextMenuX: 0,
      editHighlighted: false,
      copyHighlighted: false,
      deleteHighlighted: false,

      nodeIdCopy: '',
      superConfigParam: '',
      receiveSuperConfigParam: '',
      receiveSuperConfigParamIsEdit: false,

      superConfig: false,
      dialogVisible: false,
      dragComponentType: '',
      dragComponentExtend: '',
      selectCell: {},

      isSaveDialogShow: false,

      isSaveJobLoading: false,
      ruleForm: {
        jobId: 0,
        jobName: '',
        jobDesc: ''
      },
      rules: {
        jobName: [
          { required: true, message: '请填写任务名称', trigger: 'blur' }
        ],
        jobDesc: [
          { required: true, message: '请填写任务描述', trigger: 'blur' }
        ]
      },

      graph: '',
      value1: true,
      type: '',
      choseCellId: '',
      connectEdgeType: { // 连线方式
        connector: 'smooth',
        router: {
          name: ''
        }
      },
      grid: { // 网格设置
        size: 15, // 网格大小 10px
        visible: true, // 渲染网格背景
        type: 'fixedDot',
        args: {
          color: '#a0a0a0', // 网点颜色
          thickness: 2 // 网点大小
        }
      }
    }
  },
  computed: {
    calcColor() {
      if (this.superConfig) {
        return 'rgba(245, 12, 12, 0.5)'
      } else {
        return 'rgba(126,60,60,0.2)'
      }
    },
    dialogVisibleDragSource() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'SourceTransformation'
      } else {
        return false
      }
    },
    dialogVisibleDragSink() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'SinkTransformation'
      } else {
        return false
      }
    },
    dialogVisibleDragCalc() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'CalcTransformation'
      } else {
        return false
      }
    },
    dialogVisibleDragGroupBy() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'GroupByTransformation'
      } else {
        return false
      }
    },
    dialogVisibleDragUnion() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'UnionTransformation'
      } else {
        return false
      }
    },
    dialogVisibleTopN() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'TopnTransformation'
      } else {
        return false
      }
    },
    dialogVisibleLookupJoin() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'LookupJoinTransformation'
      } else {
        return false
      }
    },
    dialogVisibleJoin() {
      if (this.dialogVisible) {
        return this.dragComponentType === 'JoinTransformation'
      } else {
        return false
      }
    },
    superConfigFinal() {
      if (this.receiveSuperConfigParamIsEdit) {
        return this.receiveSuperConfigParam
      } else {
        return this.superConfigParam
      }
    }
  },
  watch: {
    dragShow: {
      handler(newValue, oldValue) {
        if (newValue === true) {
          this.ruleForm.jobId = this.fullJobInfo.jobId
          this.ruleForm.jobName = this.fullJobInfo.jobName
          this.ruleForm.jobDesc = this.fullJobInfo.jobDesc

          this.superConfigParam = JSON.parse(this.fullJobInfo.dagJson).optionConfigs

          if (this.graph !== '') {
            this.graph.dispose()
            this.initX6()
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initX6()
  },
  methods: {
    initX6() {
      const _that = this
      this.graph = new Graph({
        container: document.getElementById('containerChart'),
        width: 3000,
        height: '100%',
        grid: _that.grid,
        resizing: { // 调整节点宽高
          enabled: false,
          orthogonal: false
        },
        selecting: true, // 可选
        snapline: false,
        // 鼠标拖动画布
        panning: true,
        // 鼠标缩放画布
        mousewheel: {
          enabled: true,
          factor: 1.04
        },
        interacting: {
          edgeLabelMovable: true
        },
        connecting: { // 节点连接
          anchor: 'center',
          connectionPoint: 'rect',
          allowBlank: false,
          snap: true,
          createEdge() {
            return new Shape.Edge({
              attrs: {
                line: {
                  stroke: '#1890ff',
                  strokeWidth: 1,
                  targetMarker: {
                    name: 'classic',
                    size: 8
                  },
                  strokeDasharray: 0, // 虚线
                  style: {
                    animation: 'ant-line 30s infinite linear'
                  }
                }
              },
              label: {
                text: ''
              },
              connector: _that.connectEdgeType.connector,
              router: {
                name: _that.connectEdgeType.router.name || ''
              },
              zIndex: 0
            })
          }
        },
        highlighting: {
          magnetAvailable: {
            name: 'stroke',
            args: {
              padding: 4,
              attrs: {
                strokeWidth: 4,
                stroke: '#6a6c8a'
              }
            }
          }
        }
      })

      // 点击边的时候有动态效果
      insertCss(`
              @keyframes ant-line {
                to {
                    stroke-dashoffset: -1000
                }
              }
            `)

      this.graph.fromJSON(JSON.parse(this.fullJobInfo.webJson))

      this.graph.getCells().forEach(item => {
        item.attr('body/fill', '#306795FF')
      })
      // this.graph.history.redo()
      // this.graph.history.undo()
      // 鼠标移入节点
      this.graph.on('node:mouseenter', FunctionExt.debounce(() => {
        const container = document.getElementById('containerChart')
        const ports = container.querySelectorAll(
          '.x6-port-body'
        )
        this.showPorts(ports, true)
      }),
      500
      )
      // 鼠标移出节点
      this.graph.on('node:mouseleave', () => {
        const container = document.getElementById('containerChart')
        const ports = container.querySelectorAll(
          '.x6-port-body'
        )
        this.showPorts(ports, false)
      })
      // 空白位置点击
      this.graph.on('blank:click', () => {
        this.type = 'grid'
        this.superConfig = false
        this.showMenu = false
      })
      // 组件点击
      this.graph.on('cell:click', ({ cell }) => {
        this.type = cell.isNode() ? 'node' : 'edge'
        this.choseCellId = cell.id
        this.showMenu = false
        if (this.type === 'node') {
          cell.attr('body/fill', '#08aaef')
          this.nodeIdCopy = cell.id
          this.dragComponentType = cell.getAttrByPath('label/type')
          this.dragComponentExtend = JSON.stringify(cell.getAttrByPath('label/extend'))
        }
      })
      // 组件右键
      this.graph.on('cell:contextmenu', ({ e, x, y, cell, view }) => {
        if (this.type === 'node' || this.type === 'edge') {
          if (cell.id === this.choseCellId) {
            this.contextMenuY = e.pageY
            this.contextMenuX = e.pageX
            this.showMenu = true
          }
        }
      })
      // 选中组件切换
      this.graph.on('selection:changed', (args) => {
        args.added.forEach(cell => {
          this.selectCell = cell
          cell.attr('body/fill', '#08aaef')
          if (cell.isEdge()) {
            cell.isEdge() && cell.attr('line/strokeDasharray', 5) // 虚线蚂蚁线
            cell.addTools([
              {
                name: 'vertices',
                args: {
                  padding: 4,
                  attrs: {
                    strokeWidth: 0.1,
                    stroke: '#2d8cf0',
                    fill: '#ffffff'
                  }
                }
              }
            ])
          }
        })
        args.removed.forEach(cell => {
          cell.isEdge() && cell.attr('line/strokeDasharray', 0) // 正常线
          cell.removeTools()

          cell.isNode() && cell.attr('body/fill', '#306795FF')
        })
      })
    },
    // 展示连接点
    showPorts(ports, show) {
      for (let i = 0, len = ports.length; i < len; i = i + 1) {
        ports[i].style.visibility = show ? 'visible' : 'hidden'
      }
    },
    // 拖拽生成节点
    startDrag(type, text, e) {
      startDragToGraph(this.graph, type, text, e)
    },
    // 删除节点
    deleteNode() {
      const cell = this.graph.getSelectedCells()
      this.graph.removeCells(cell)
      this.type = 'grid'
      this.showMenu = false
      this.resetBackground()
    },
    // 编辑节点
    editNode() {
      if (this.type === 'node') {
        this.dialogVisible = true
        this.showMenu = false
        this.resetBackground()
      }
    },
    showConfig() {
      this.$nextTick(() => {
        this.superConfig = !this.superConfig
      })
    },
    generateConfig() {
      const cells = this.graph.getCells()

      const edgeCells = cells.filter(item => item.shape === 'edge')

      const nodeCells = cells.filter(item => item.shape === 'rect')

      const wrapperArray = []

      nodeCells.forEach(node => {
        const nodeId = node.id
        const inputIds = []
        if (node.attr('label/type') === 'JoinTransformation' || node.attr('label/type') === 'UnionTransformation') {
          inputIds.push(node.attr('label/extend').leftNumber)
          inputIds.push(node.attr('label/extend').rightNumber)
        } else {
          edgeCells.forEach(edge => {
            if (edge.target.cell === nodeId) {
              inputIds.push(edge.source.cell)
            }
          })
        }

        wrapperArray.push({ 'inputIds': inputIds, 'name': node.attr('label/type'), 'id': node.id, 'transformation': JSON.stringify(node.attr('label/extend')) })
      })

      const finalJson = JSON.stringify({ 'transformationWrappers': wrapperArray, 'optionConfigs': this.superConfigFinal })

      const jobPartInfo = { 'id': this.ruleForm.jobId, 'jobName': this.ruleForm.jobName.trim(), 'jobDesc': this.ruleForm.jobDesc.trim(), 'isMonitor': '1', 'jobLevel': '0', 'departmentLabel': '数据平台', 'webJson': '', 'dagJson': '', 'type': 'freeDrag', 'subType': 'freeDrag_v2' }

      jobPartInfo.dagJson = finalJson
      jobPartInfo.webJson = JSON.stringify(this.graph.toJSON({ diff: true }))

      return JSON.stringify(jobPartInfo)
    },
    // 关闭拖拽抽屉组件
    closeDrawShow(type) {
      if (type === '0') {
        this.superConfig = false
        this.$emit('close')
      } else {
        this.superConfig = false
        this.isSaveDialogShow = true
      }
    },
    // 算子组件dialog关闭回调
    handleDragComponent(componentExtend) {
      this.dialogVisible = false
      if (componentExtend !== undefined) {
        this.selectCell.attr('label/extend', JSON.parse(componentExtend))
        this.dragComponentExtend = JSON.stringify(this.selectCell.attr('label/extend'))
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const jobPartInfo = this.generateConfig()
          this.isSaveJobLoading = true
          if (this.ruleForm.jobId === 0) {
            saveJob(jobPartInfo).then(res => {
              if (res.code === '200') {
                this.$message({
                  type: 'success',
                  message: '创建成功!'
                })
                this.$emit('close')
                this.isSaveDialogShow = false
                this.isSaveJobLoading = false
              }
              if (res.code === '500') {
                this.isSaveDialogShow = false
                this.isSaveJobLoading = false
                this.$message({
                  type: 'error',
                  message: res.mes
                })
              }
            })
          } else {
            updateJob(jobPartInfo).then(res => {
              if (res.code === '200') {
                this.$message({
                  type: 'success',
                  message: '修改成功!'
                })
                this.$emit('close')
                this.isSaveDialogShow = false
                this.isSaveJobLoading = false
              }
              if (res.code === '500') {
                this.isSaveDialogShow = false
                this.isSaveJobLoading = false
                this.$message({
                  type: 'error',
                  message: res.mes
                })
              }
            })
          }
        } else {
          return false
        }
      })
    },
    highlightItem(item) {
      if (item === 'edit') {
        this.editHighlighted = true
      } else if (item === 'copy') {
        this.copyHighlighted = true
      } else {
        this.deleteHighlighted = true
      }
    },
    resetBackground() {
      this.editHighlighted = false
      this.copyHighlighted = false
      this.deleteHighlighted = false
    },
    copyToClipboard(text) {
      Vue.prototype.$copyText(text).then(() => {
        this.showMenu = false
        this.resetBackground()
      }, () => {
        Vue.prototype.$message({
          type: 'error',
          message: '复制失败，请联系管理员！'
        })
      })
    },
    receiveSuperConfig(superConfig) {
      if (superConfig !== undefined) {
        this.receiveSuperConfigParam = superConfig
        this.receiveSuperConfigParamIsEdit = true
      }
    }

  }
}
</script>

<style lang="scss">
@import 'css/index.scss';

</style>
