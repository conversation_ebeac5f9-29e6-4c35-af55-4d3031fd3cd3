<template>
  <div class="app-container">
    <el-dialog
      title="在线推理算子配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >
      <div style="margin-bottom: -20px;margin-top: -20px">
        <el-form ref="ruleFormDragAlgo" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item label="选择镜像信息" prop="imageInfo">
            <el-cascader
              v-model="ruleForm.imageInfo"
              style="width: 100%"
              clearable
              filterable
              placeholder="请选择镜像名称和提交版本id"
              :options="options"
              :props="{ expandTrigger: 'hover' }"
              @change="handleChange"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="环境变量">
            <el-input
              v-model="ruleForm.envJson"
              type="textarea"
              autosize
              :rows="2"
              placeholder="项目里面有默认值，如果有需要变动的字段请以Json的格式填写, eg: {'name':'xiaoming','age':12}"
              clearable
              @blur="envBlur(ruleForm.envJson)"
            />
          </el-form-item>
          <el-form-item label="过滤条件">
            <el-input
              v-model="ruleForm.filterExpression"
              type="textarea"
              :rows="1"
              autosize
              placeholder="example=> date='20220824' and ht='22'  如果没有过滤条件可以什么都不填"
              clearable
            />
          </el-form-item>
          <el-collapse>
            <el-collapse-item>
              <el-form-item label="pythonPath" prop="pythonPath">
                <el-input
                  v-model="ruleForm.pythonPath"
                  placeholder="使用默认值即可，也可以修改"
                  clearable
                />
              </el-form-item>
              <el-form-item label="projectPath" prop="projectPath">
                <el-input
                  v-model="ruleForm.projectPath"
                  placeholder="使用默认值即可，也可以修改"
                  clearable
                />
              </el-form-item>
              <el-form-item label="pyUDF" prop="pyUDF">
                <el-input
                  v-model="ruleForm.pyUDF"
                  placeholder="使用默认值即可，也可以修改"
                  clearable
                />
              </el-form-item>
              <el-form-item label="pemMode" prop="pemMode">
                <el-input
                  v-model="ruleForm.pemMode"
                  placeholder="使用默认值即可，也可以修改"
                  clearable
                />
              </el-form-item>
              <el-form-item label="otherPath" prop="otherPath">
                <el-input
                  v-model="ruleForm.otherPath"
                  placeholder="使用默认值即可，也可以修改"
                  clearable
                />
              </el-form-item>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAlgoTransformationImageInfo } from '@/api/job'
export default {
  name: 'DragAlgo',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleForm: {
        imageInfo: '',
        harbor_prefix: 'repoin.shizhuang-inc.net/algo-bigdata/',
        pythonPath: '/usr/bin/python3.8',
        projectPath: '/opt/flink/algo',
        pyUDF: 'algo_udf.FlinkUdf()',
        pemMode: 'multi_thread',
        otherPath: '/usr/lib/python3.8/site-packages',
        envJson: '',
        filterExpression: ''
      },
      rules: {
        imageInfo: [
          { required: true, message: '请输入选择字段', trigger: 'change' }
        ],
        pythonPath: [
          { required: true, message: '请输入pythonPath', trigger: 'blur' }
        ],
        projectPath: [
          { required: true, message: '请输入projectPath', trigger: 'blur' }
        ],
        pyUDF: [
          { required: true, message: '请输入pyUDF', trigger: 'blur' }
        ],
        pemMode: [
          { required: true, message: '请输入pemMode', trigger: 'blur' }
        ],
        otherPath: [
          { required: true, message: '请输入otherPath', trigger: 'blur' }
        ]
      },
      options: []
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'AlgoTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        const tempExtend = JSON.parse(this.extend)
        this.ruleForm.pythonPath = tempExtend.pythonPath === undefined ? '/usr/bin/python3.8' : tempExtend.pythonPath
        this.ruleForm.projectPath = tempExtend.projectPath === undefined ? '/opt/flink/algo' : tempExtend.projectPath
        this.ruleForm.pyUDF = tempExtend.pyUDF === undefined ? 'algo_udf.FlinkUdf()' : tempExtend.pyUDF
        this.ruleForm.pemMode = tempExtend.pemMode === undefined ? 'multi_thread' : tempExtend.pemMode
        this.ruleForm.otherPath = tempExtend.otherPath === undefined ? '/usr/lib/python3.8/site-packages' : tempExtend.otherPath
        this.ruleForm.harbor_prefix = tempExtend.harbor_prefix === undefined ? 'repoin.shizhuang-inc.net/algo-bigdata/' : tempExtend.harbor_prefix
        if (tempExtend.image !== undefined && tempExtend.tag !== undefined) {
          this.ruleForm.imageInfo = [tempExtend.image, tempExtend.tag]
        }
        this.ruleForm.envJson = tempExtend.envJson === undefined ? '' : tempExtend.envJson
        this.ruleForm.filterExpression = tempExtend.filterExpressionOrigin === undefined ? '' : tempExtend.filterExpressionOrigin

        getAlgoTransformationImageInfo().then(res => {
          if (res.code === '200') {
            this.options = res.data
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        const res = this.envBlurFinalCheck(this.ruleForm.envJson)
        if (res) {
          this.$refs['ruleFormDragAlgo'].validate((valid) => {
            const transformation = { 'pythonPath': '', 'projectPath': '', 'pyUDF': '', 'pemMode': '', 'otherPath': '', 'harbor_prefix': '', 'image': '', 'tag': '', 'envJson': '', 'filterExpression': '', 'filterExpressionOrigin': '' }
            transformation.pythonPath = this.ruleForm.pythonPath.trim()
            transformation.projectPath = this.ruleForm.projectPath.trim()
            transformation.pyUDF = this.ruleForm.pyUDF.trim()
            transformation.pemMode = this.ruleForm.pemMode.trim()
            transformation.otherPath = this.ruleForm.otherPath.trim()
            transformation.harbor_prefix = this.ruleForm.harbor_prefix.trim()
            transformation.image = this.ruleForm.imageInfo.at(0)
            transformation.tag = this.ruleForm.imageInfo.at(1)
            transformation.filterExpression = this.ruleForm.filterExpression.trim().replace(/\n/g, ' ')
            transformation.filterExpressionOrigin = this.ruleForm.filterExpression.trim()
            if (this.ruleForm.envJson.trim() === '' || this.ruleForm.envJson.trim() === '{}' || this.ruleForm.envJson.trim() === '[]') {
              transformation.envJson = ''
            } else {
              transformation.envJson = this.ruleForm.envJson.trim()
            }
            if (valid) {
              this.$emit('close', JSON.stringify(transformation))
              console.log(JSON.stringify(transformation))
              this.$refs['ruleFormDragAlgo'].resetFields()
            } else {
              return false
            }
          })
        } else {
          this.$message({
            type: 'warning',
            message: '环境变量 ' + this.ruleForm.envJson + ' 不是JSON格式数据，请重新填写!!!'
          })
        }
      } else {
        this.$emit('close')
        this.$refs['ruleFormDragAlgo'].resetFields()
      }
    },
    handleChange(value) {
      console.log(value)
      console.log(this.ruleForm.imageInfo)
    },
    envBlur(inputValue) {
      if (inputValue !== undefined && inputValue.trim() !== '') {
        try {
          JSON.parse(inputValue)
        } catch (e) {
          this.$message({
            type: 'warning',
            message: inputValue + ' 不是JSON格式数据，请重新填写!!!'
          })
        }
      }
    },
    envBlurFinalCheck(inputValue) {
      if (inputValue !== undefined && inputValue.trim() !== '') {
        try {
          JSON.parse(inputValue)
          return true
        } catch (e) {
          return false
        }
      } else {
        return true
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

</style>

