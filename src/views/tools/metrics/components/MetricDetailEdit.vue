<template>
  <div class="app-container">
    <el-dialog
      :title="titleShowTag"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="tag"
      :modal-append-to-body="true"
      :append-to-body="true"
      width="40%"
      top="10vh"
    >
      <el-form ref="ruleFormMetricDetail" :model="ruleForm" :rules="rules" label-width="100px" style="margin-top: -20px;margin-bottom: -30px">
        <el-form-item label="名称" prop="metricDetailName">
          <el-input v-model="ruleForm.metricDetailName" clearable :disabled="ruleForm.isDel === 0" placeholder="输入指标明细名称" />
        </el-form-item>
        <el-form-item label="中文名称" prop="metricDetailCnName">
          <el-input v-model="ruleForm.metricDetailCnName" clearable :disabled="ruleForm.isDel === 0" placeholder="输入指标明细中文名称" />
        </el-form-item>
        <el-form-item label="描述" prop="metricDetailDesc">
          <el-input v-model="ruleForm.metricDetailDesc" clearable placeholder="输入指标明细描述" />
        </el-form-item>
        <el-form-item label="指标类型" prop="metricType">
          <el-select v-model="ruleForm.metricType" clearable placeholder="选择指标类型">
            <el-option label="STRING" value="0" />
            <el-option label="INTEGER" value="1" />
            <el-option label="DOUBLE" value="2" />
            <el-option label="FLOAT" value="3" />
            <el-option label="LONG" value="4" />
            <el-option label="BOOLEAN" value="5" />
            <el-option label="DATE" value="6" />
          </el-select>
        </el-form-item>
        <el-form-item label="产出类型" prop="metricProductType">
          <el-select v-model="ruleForm.metricProductType" clearable placeholder="选择产出类型">
            <el-option label="SUM" value="0" />
            <el-option label="COUNT" value="1" />
            <el-option label="COUNT_DISTINCT" value="2" />
            <el-option label="MIN" value="4" />
            <el-option label="AVG" value="3" />
            <el-option label="MAX" value="5" />
            <el-option label="CONSTANT" value="6" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('ruleFormMetricDetail')">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleFormMetricDetail')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getAllCommonAttributes, updateMetricDetail, createMetricDetail } from '@/api/metrics'

export default {
  name: 'MetricDetailEdit',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    editJson: {
      type: Object,
      default: function() {
        return { 'id': 0, 'isDel': 1, 'metricGroupId': 0, 'metricDetailName': '', 'metricDetailCnName': '', 'metricDetailDesc': '', 'metricType': '', 'metricProductType': '' }
      }
    }
  },
  data() {
    return {
      ruleForm: {
        id: 0,
        isDel: 1,
        metricGroupId: 0,
        metricDetailName: '',
        metricDetailCnName: '',
        metricDetailDesc: '',
        metricType: '',
        metricProductType: ''
      },
      rules: {
        metricDetailName: [
          { required: true, message: '请输入指标明细名称', trigger: 'blur' }
        ],
        metricDetailCnName: [
          { required: true, message: '请输入指标明细中文名称', trigger: 'blur' }
        ],
        metricDetailDesc: [
          { required: true, message: '请输入指标明细描述', trigger: 'blur' }
        ],
        metricType: [
          { required: true, message: '请输入指标类型', trigger: 'change' }
        ],
        metricProductType: [
          { required: true, message: '请输入产出类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow
    },
    titleShowTag() {
      return this.ruleForm.id === 0 ? '新增指标明细' : '修改指标明细'
    },
    editTag() {
      return this.ruleForm.isHaveMetricDetail === 1
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      if (newValue === true) {
        console.log(this.editJson)
        this.ruleForm.id = this.editJson.id
        this.ruleForm.isDel = this.editJson.isDel
        this.ruleForm.metricGroupId = this.editJson.metricGroupId
        this.ruleForm.metricDetailName = this.editJson.metricDetailName
        this.ruleForm.metricDetailCnName = this.editJson.metricDetailCnName
        this.ruleForm.metricDetailDesc = this.editJson.metricDetailDesc
        this.ruleForm.metricType = this.editJson.metricType
        this.ruleForm.metricProductType = this.editJson.metricProductType
      }
    }
  },
  mounted() {
    getAllCommonAttributes().then(res => {
      const commonAttributesArr = []
      if (res.code === '200') {
        res.data.forEach(item => {
          commonAttributesArr.push({ 'commonAttributeName': item.name })
        })
        this.options = commonAttributesArr
      }
    })
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.id === 0) {
            this.createMetricDetail()
          } else {
            this.updateMetricDetail()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
      this.$refs[formName].resetFields()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$emit('close', '0')
    },
    createMetricDetail() {
      const metricDetail = { 'teMetricGroupMetadataInfoId': 0, 'metricName': '', 'metricCnName': '', 'metricDesc': '', 'metricType': 0, 'metricProductType': 0 }
      metricDetail.teMetricGroupMetadataInfoId = this.ruleForm.metricGroupId
      metricDetail.metricName = this.ruleForm.metricDetailName
      metricDetail.metricCnName = this.ruleForm.metricDetailCnName
      metricDetail.metricDesc = this.ruleForm.metricDetailDesc
      metricDetail.metricType = parseInt(this.ruleForm.metricType)
      metricDetail.metricProductType = parseInt(this.ruleForm.metricProductType)
      console.log(metricDetail)
      createMetricDetail(metricDetail).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '新增成功!'
          })
          this.$emit('close', '1')
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    updateMetricDetail() {
      const metricDetail = { 'id': 0, 'metricName': '', 'metricCnName': '', 'metricDesc': '', 'metricType': 0, 'metricProductType': 0 }
      metricDetail.id = this.ruleForm.id
      metricDetail.metricName = this.ruleForm.metricDetailName
      metricDetail.metricCnName = this.ruleForm.metricDetailCnName
      metricDetail.metricDesc = this.ruleForm.metricDetailDesc
      metricDetail.metricType = parseInt(this.ruleForm.metricType)
      metricDetail.metricProductType = parseInt(this.ruleForm.metricProductType)
      console.log(metricDetail)
      updateMetricDetail(metricDetail).then(res => {
        if (res.code === '200') {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.$emit('close', '1')
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    }
  }
}
</script>

<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    height: 30px;
    line-height: 28px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 200px;
    margin-left: 15px;
    vertical-align: bottom;
  }
</style>
