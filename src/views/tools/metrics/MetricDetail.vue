<template>
  <div class="app-container">
    <el-row :gutter="5" style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px" class="element-header-tool">
      <el-col :span="3">
        <div>
          <el-input
            v-model="metricDetailNameBlurry"
            placeholder="名称"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-input
            v-model="metricDetailCnNameBlurry"
            placeholder="中文名称"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-input
            v-model="metricDetailDescBlurry"
            placeholder="描述"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-input
            v-model="createNameBlurry"
            placeholder="创建人"
            clearable
            @keyup.enter.native="doSearch"
          />
        </div>
      </el-col>
      <el-col :span="3">
        <div>
          <el-button size="small" type="success" icon="el-icon-search" @click="doSearch">搜索</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col>
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          highlight-current-row
          :header-cell-style="{textAlign: 'left'}"
          size="medium"
          style="width: 100%;margin: auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)"
        >
          <el-table-column
            prop="metricName"
            label="名称"
            fixed
            sortable
            align="left"
            width="400"
          />
          <el-table-column
            prop="metricCnName"
            label="中文名称"
            sortable
            align="left"
            width="300"
          />
          <el-table-column
            prop="metricDesc"
            label="描述"
            sortable
            align="left"
            width="300"
          />
          <el-table-column
            prop="teMetricGroupMetadataInfoName"
            label="指标组"
            sortable
            align="left"
            width="500"
          />
          <el-table-column
            prop="metricType"
            label="指标类型"
            sortable
            align="left"
            width="160"
          >
            <template slot-scope="scope">
              <el-tag size="medium">{{ getMetricTypeName(scope.row.metricType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="metricProductType"
            label="产出类型"
            sortable
            align="left"
            width="160"
          >
            <template slot-scope="scope">
              <el-tag size="medium">{{ getMetricProductTypeName(scope.row.metricProductType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createName"
            label="创建人"
            sortable
            align="left"
            width="140"
          />
          <el-table-column
            prop="createTime"
            label="创建时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            prop="modifyName"
            label="操作人"
            sortable
            align="left"
            width="140"
          />
          <el-table-column
            prop="modifyTime"
            label="更新时间"
            sortable
            align="left"
            width="200"
          />
          <el-table-column
            label="相关操作"
            width="260"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                plain
                class="el-icon-edit-outline"
                @click="updateMetricDetail(scope.row)"
              >修改</el-button>
              <el-button
                size="mini"
                type="primary"
                plain
                :disabled="scope.row.isDel === 0"
                @click="onlineMetricDetail(scope.row.id,scope.row.metricName)"
              >{{ scope.row.isDel ===1 ? '去上线':'已上线'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px">
      <el-col>
        <el-pagination
          background
          layout="total, prev, pager, next, sizes,jumper"
          :page-sizes="[10, 15, 30, 50]"
          class="page_tt"
          :total="page_info.total_num"
          :page-size="page_info.page_size"
          :current-page="page_info.current_page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
        />
      </el-col>
    </el-row>

    <metric-detail-edit :is-show="isMetricDetailEditShow" :edit-json="fullMetricDetailJson" style="position: absolute" @close="closeMetricDetailEdit" />
  </div>
</template>
<script>
import { queryMetricDetailPageable, onlineMetricDetail } from '@/api/metrics'
import MetricDetailEdit from './components/MetricDetailEdit'

export default {
  name: 'MetricDetail',
  components: {
    MetricDetailEdit
  },
  props: {
    metricGroupId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      metricDetailNameBlurry: '',
      metricDetailCnNameBlurry: '',
      metricDetailDescBlurry: '',
      createNameBlurry: '',
      isMetricDetailEditShow: false,
      tableData: [
        {
          'id': 1,
          'metricName': 'pv',
          'metricCnName': '页面访问次数',
          'metricDesc': 'pv',
          'isDel': 0,
          'createName': 'liminghui',
          'createTime': '2022-11-04 10:50:25',
          'modifyName': 'liminghui',
          'modifyTime': '2022-11-04 10:50:25',
          'teMetricGroupMetadataInfoId': 10,
          'teMetricGroupMetadataInfoName': 'order_metrics',
          'metricProductType': 1,
          'metricType': 1
        },
        {
          'id': 2,
          'metricName': 'gmv',
          'metricCnName': '商品交易总额',
          'metricDesc': 'gmv',
          'isDel': 0,
          'createName': 'liminghui',
          'createTime': '2022-11-04 10:52:58',
          'modifyName': 'liminghui',
          'modifyTime': '2022-11-04 10:50:25',
          'teMetricGroupMetadataInfoId': 10,
          'teMetricGroupMetadataInfoName': 'order_metrics',
          'metricProductType': 0,
          'metricType': 2
        }
      ],
      page_info: {
        total_num: 0,
        current_page: 1,
        page_size: 10
      },
      loading: false,
      fullMetricDetailJson: { 'id': 0, 'isDel': 1, 'metricGroupId': 0, 'metricDetailName': '', 'metricDetailCnName': '', 'metricDetailDesc': '', 'metricType': '', 'metricProductType': '' }
    }
  },
  computed: {
  },
  mounted() {
  },
  created() {
    this.doSearch()
  },
  methods: {
    doSearch() {
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
      pathParams.push({ 'paramName': 'metricName', 'paramValue': this.metricDetailNameBlurry })
      pathParams.push({ 'paramName': 'metricCnName', 'paramValue': this.metricDetailCnNameBlurry })
      pathParams.push({ 'paramName': 'metricDesc', 'paramValue': this.metricDetailDescBlurry })
      pathParams.push({ 'paramName': 'createName', 'paramValue': this.createNameBlurry })
      if (this.metricGroupId !== 0) {
        pathParams.push({ 'paramName': 'teMetricGroupMetadataInfoId', 'paramValue': this.metricGroupId })
      }

      queryMetricDetailPageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })
    },
    doSearchForMetricGroup() {
      this.loading = true
      const pathParams = []

      pathParams.push({ 'paramName': 'size', 'paramValue': this.page_info.page_size })
      pathParams.push({ 'paramName': 'page', 'paramValue': this.page_info.current_page - 1 })
      pathParams.push({ 'paramName': 'metricName', 'paramValue': this.metricDetailNameBlurry })
      pathParams.push({ 'paramName': 'metricDesc', 'paramValue': this.metricDetailDescBlurry })
      pathParams.push({ 'paramName': 'createName', 'paramValue': this.createNameBlurry })
      if (this.metricGroupId !== 0) {
        pathParams.push({ 'paramName': 'teMetricGroupMetadataInfoId', 'paramValue': this.metricGroupId })
      }

      queryMetricDetailPageable(pathParams).then(res => {
        if (res.code === '200') {
          this.page_info.total_num = res.data.totalElements
          this.fillTableData(res.data.content)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
        this.loading = false
      })
    },
    fillTableData(data) {
      const tempTableData = []
      data.forEach(item => {
        tempTableData.push({ 'id': item.id, 'isDel': item.isDel, 'metricName': item.metricName, 'metricCnName': item.metricCnName, 'metricDesc': item.metricDesc, 'teMetricGroupMetadataInfoName': item.teMetricGroupMetadataInfoName, 'metricType': item.metricType, 'metricProductType': item.metricProductType, 'createTime': item.createTime, 'modifyName': item.modifyName, 'createName': item.createName, 'modifyTime': item.modifyTime })
      })
      this.tableData = tempTableData
    },
    handleSizeChange(val) {
      this.page_info.page_size = val
      this.doSearch()
    },
    handleCurrentChange(val) {
      this.page_info.current_page = val
      this.doSearch()
    },
    addMetricDetail() {
      this.fullMetricDetailJson = { 'id': 0, 'isDel': 1, 'metricGroupId': 0, 'metricDetailName': '', 'metricDetailCnName': '', 'metricDetailDesc': '', 'metricType': '', 'metricProductType': '' }
      this.isMetricDetailEditShow = true
    },
    updateMetricDetail(row) {
      this.fullMetricDetailJson.id = row.id
      this.fullMetricDetailJson.isDel = row.isDel
      this.fullMetricDetailJson.metricGroupId = row.teMetricGroupMetadataInfoId
      this.fullMetricDetailJson.metricDetailName = row.metricName
      this.fullMetricDetailJson.metricDetailCnName = row.metricCnName
      this.fullMetricDetailJson.metricDetailDesc = row.metricDesc
      this.fullMetricDetailJson.metricType = row.metricType + ''
      this.fullMetricDetailJson.metricProductType = row.metricProductType + ''
      this.isMetricDetailEditShow = true
    },
    closeMetricDetailEdit(tag) {
      this.isMetricDetailEditShow = false
      if (tag === '1') {
        this.doSearch()
      }
    },
    getMetricTypeName(metricType) {
      switch (metricType) {
        case 0:
          return 'STRING'
        case 1:
          return 'INTEGER'
        case 2:
          return 'DOUBLE'
        case 3:
          return 'FLOAT'
        case 4:
          return 'LONG'
        case 5:
          return 'BOOLEAN'
        case 6:
          return 'DATE'
        default:
          return 'NONE'
      }
    },
    getMetricProductTypeName(metricProductType) {
      switch (metricProductType) {
        case 0:
          return 'SUM'
        case 1:
          return 'COUNT'
        case 2:
          return 'COUNT_DISTINCT'
        case 3:
          return 'AVG'
        case 4:
          return 'MIN'
        case 5:
          return 'MAX'
        case 6:
          return 'CONSTANT'
        default:
          return 'NONE'
      }
    },
    onlineMetricDetail(metricDetailId, metricName) {
      this.$confirm('是否确认上线指标: ' + metricName + ' ？注意此操作不可逆！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        onlineMetricDetail(metricDetailId).then(res => {
          if (res.code === '200') {
            this.doSearch()
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消上线'
        })
      })
    },
    clearData() {
      this.metricDetailNameBlurry = ''
      this.metricDetailCnNameBlurry = ''
      this.metricDetailDescBlurry = ''
      this.createNameBlurry = ''
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
</style>
