<template>
  <div class="app-container">
    <el-divider />
    <el-button type="primary" @click="testName">testName</el-button>
    <el-divider />
    <el-button type="primary" @click="getCurrentUser">user</el-button>
    <el-divider />
    <el-button type="primary" @click="queryJobPageable">pathDemo</el-button>
    <el-divider />
    <el-button type="primary" @click="luyang">刘洋例子</el-button>
    <el-divider />
    <el-button type="primary" @click="getSourceAndTheirTable">获取数据源</el-button>
    <el-divider />
    <el-button type="primary" @click="create_job">创建作业</el-button>
    <el-divider />
    <el-button type="primary" @click="get_author_info">获取用户信息</el-button>
    <el-divider />
    <el-button type="primary" @click="setSessionStorage">给sessionStorage赋值</el-button>
    <el-button type="primary" @click="getSessionStorage">打印sessionStorage的值</el-button>
    <el-divider />
    <el-tree :data="treedata" :props="defaultProps" @node-click="handleNodeClick" />
    <el-divider />
    <el-button type="primary" class="postion_test" :style="{left: pos_x + 150 + 'px', top: pos_y + 100 + 'px' }">测试position</el-button>

    <el-divider />
    <el-button type="primary" @click="testAssign">测试object.assign</el-button>

    <el-divider />
    <el-button type="text" @click="showClick()">点击打开 Dialog</el-button>

    <el-divider />

    <el-divider />
    <el-alert
      v-if="close_state===1"
      title="this is the message that you must notice!"
      type="success"
      @close="close_done"
    />

    <el-divider />
    <el-carousel indicator-position="outside">
      <el-carousel-item v-for="item in 4" :key="item">
        <h3>{{ item }}</h3>
      </el-carousel-item>
    </el-carousel>

    <el-divider />
    <ul>
      <li v-for="(item,key) in members" :key="item.name">
        {{ item.name }}
        {{ key }}
      </li>
    </ul>

    <el-divider />
    <el-button :plain="true" @click="print_company">显示公司</el-button>

    <el-divider />
    <el-input v-model="input_01" placeholder="请输入第一个值" type="number" />
    <el-input v-model="input_result" placeholder="平方" />

    <el-divider />
    <el-input v-model="input_02" placeholder="请输入内容" />

    <el-divider />
    <el-button :plain="true" type="primary" @click="test_post">post_request</el-button>
    <el-input v-model="post_res" placeholder="返回结果" />

    <el-divider />
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="审批人">
        <el-input v-model="formInline.user" placeholder="审批人" />
      </el-form-item>
      <el-form-item label="活动区域">
        <el-select v-model="formInline.region" placeholder="活动区域">
          <el-option label="区域一" value="shanghai" />
          <el-option label="区域二" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">查询</el-button>
      </el-form-item>
    </el-form>

    <el-divider />
    <el-button @click="show3 = !show3">下拉</el-button>
    <el-collapse-transition>
      <div v-show="show3" class="show-3">
        <div class="transition-box">
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            :row-class-name="tableRowClassName"
          >
            <el-table-column
              prop="date"
              label="日期"
              width="180"
            />
            <el-table-column
              prop="name"
              label="姓名"
              width="180"
            />
            <el-table-column
              prop="address"
              label="地址"
            />
          </el-table>
          <div>
            <el-pagination
              background
              layout="total, prev, pager, next, sizes,jumper"
              class="page_tt"
              :total="page_info.total_num"
              :page-size="page_info.page_size"
              :current-page="page_info.current_page"
              @current-change="current_page_change"
              @size-change="size_change"
            />
          </div>
        </div>

      </div>
    </el-collapse-transition>

    <el-divider />
    <i class="el-icon-star-on" />
    <i class="el-icon-share" />
    <i class="el-icon-delete" />
    <el-button type="primary" :plain="true" icon="el-icon-search">搜索</el-button>

    <el-divider />

    <el-divider />
    <el-card class="box-card" shadow="always">
      hello
    </el-card>
    <el-divider />
    <el-row :gutter="12">
      <el-col :span="8">
        <el-card shadow="always">
          互联宝地
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="always">
          印纺
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="always">
          合生汇
        </el-card>
      </el-col>
    </el-row>
    <el-divider />
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>卡片名称</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="show_info">操作按钮</el-button>
      </div>
      <div v-for="o in 4" :key="o" class="text item">
        {{ '列表内容 ' + o }}
      </div>
    </el-card>
    <el-divider />
    <el-radio-group v-model="size">
      <el-radio label="">默认</el-radio>
      <el-radio label="medium">中等</el-radio>
      <el-radio label="small">小型</el-radio>
      <el-radio label="mini">超小</el-radio>
    </el-radio-group>

    <el-descriptions class="margin-top" title="带边框列表" :column="3" :size="size" border>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-user" />
          用户名
        </template>
        kooriookami
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-mobile-phone" />
          手机号
        </template>
        18100000000
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-location-outline" />
          居住地
        </template>
        苏州市
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-tickets" />
          备注
        </template>
        <el-tag size="small">学校</el-tag>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          <i class="el-icon-office-building" />
          联系地址
        </template>
        江苏省苏州市吴中区吴中大道 1188 号
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions class="margin-top" title="无边框列表" :column="3" :size="size">
      <el-descriptions-item label="用户名">kooriookami</el-descriptions-item>
      <el-descriptions-item label="手机号">18100000000</el-descriptions-item>
      <el-descriptions-item label="居住地">苏州市</el-descriptions-item>
      <el-descriptions-item label="备注">
        <el-tag size="small">学校</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="联系地址">江苏省苏州市吴中区吴中大道 1188 号</el-descriptions-item>
    </el-descriptions>
    <el-divider />
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item title="一致性 Consistency" name="1">
        <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
        <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div>
      </el-collapse-item>
      <el-collapse-item title="反馈 Feedback" name="2">
        <div>控制反馈：通过界面样式和交互动效让用户可以清晰的感知自己的操作；</div>
        <div>页面反馈：操作后，通过页面元素的变化清晰地展现当前状态。</div>
      </el-collapse-item>
      <el-collapse-item title="效率 Efficiency" name="3">
        <div>简化流程：设计简洁直观的操作流程；</div>
        <div>清晰明确：语言表达清晰且表意明确，让用户快速理解进而作出决策；</div>
        <div>帮助用户识别：界面简单直白，让用户快速识别而非回忆，减少用户记忆负担。</div>
      </el-collapse-item>
      <el-collapse-item title="可控 Controllability" name="4">
        <div>用户决策：根据场景可给予用户操作建议或安全提示，但不能代替用户进行决策；</div>
        <div>结果可控：用户可以自由的进行操作，包括撤销、回退和终止当前操作等。</div>
      </el-collapse-item>
    </el-collapse>
    <el-divider />
    <div class="radio">
      排序：
      <el-radio-group v-model="reverse">
        <el-radio :label="true">倒序</el-radio>
        <el-radio :label="false">正序</el-radio>
      </el-radio-group>
    </div>

    <el-timeline :reverse="reverse">
      <el-timeline-item
        v-for="(activity, index) in activities"
        :key="index"
        :timestamp="activity.timestamp"
      >
        {{ activity.content }}
      </el-timeline-item>
    </el-timeline>
    <el-divider />
    <el-button
      plain
      @click="open1"
    >
      成功
    </el-button>
    <el-button
      plain
      @click="open2"
    >
      警告
    </el-button>
    <el-button
      plain
      @click="open3"
    >
      消息
    </el-button>
    <el-button
      plain
      @click="open4"
    >
      错误
    </el-button>
    <el-divider />
    <el-empty description="空空如也" :image-size="200" />
    <el-divider />
    <el-calendar v-model="value" class="calendar" />
    <el-divider />
    <el-image
      style="width: 100px; height: 100px"
      :src="url"
      :preview-src-list="srcList"
    />
    <el-divider />
    <el-tabs type="border-card" class="card_tab">
      <el-tab-pane label="用户管理">用户管理</el-tab-pane>
      <el-tab-pane label="配置管理">配置管理</el-tab-pane>
      <el-tab-pane label="角色管理">角色管理</el-tab-pane>
      <el-tab-pane label="定时任务补偿">定时任务补偿</el-tab-pane>
    </el-tabs>
    <el-divider />
    <el-button type="text" @click="dialog = true">打开嵌套 Form 的 Drawer</el-button>
    <el-drawer
      ref="drawer"
      title="我嵌套了 Form !"
      :before-close="handleClose"
      :visible.sync="dialog"
      direction="ltr"
      custom-class="demo-drawer"
    >
      <div class="demo-drawer__content">
        <el-form :model="form">
          <el-form-item label="活动名称" :label-width="formLabelWidth">
            <el-input v-model="form.name" autocomplete="off" />
          </el-form-item>
          <el-form-item label="活动区域" :label-width="formLabelWidth">
            <el-select v-model="form.region" placeholder="请选择活动区域">
              <el-option label="区域一" value="shanghai" />
              <el-option label="区域二" value="beijing" />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button @click="cancelForm">取 消</el-button>
          <el-button type="primary" :loading="drawer_loading" @click="$refs.drawer.closeDrawer()">{{ drawer_loading ? '提交中 ...' : '确 定' }}</el-button>
        </div>
      </div>
    </el-drawer>
    <el-divider />
    <div style="height: 300px;">
      <el-steps direction="vertical" :active="2">
        <el-step title="步骤 1" description="this is step1" />
        <el-step title="步骤 2" description="this is step2" />
        <el-step title="步骤 3" description="this is step3" />
      </el-steps>
    </div>
    <el-divider />
    <el-input-number v-model="num" size="small" controls-position="right" :min="1" :max="10" @change="handleChange" />
    <el-divider />
    <el-button type="text" @click="dialogFormVisible = true">打开嵌套表单的 Dialog</el-button>
    <el-dialog title="创建作业" :visible.sync="dialogFormVisible" :close-on-click-modal="false" :destroy-on-close="true" top="15vh" width="50%" @closed="step1=true;step2=false;step3=false;active=1">
      <el-steps :active="active" finish-status="success">
        <el-step title="步骤 1" />
        <el-step title="步骤 2" />
        <el-step title="步骤 3" />
      </el-steps>
      <div class="dialog-set">
        <el-divider />
        <div v-show="step1" style="border-color: #34bfa3;border-style: dashed; border-width: 1px; border-radius: 4px;width: 100%;height: 240px;padding: 5px 5px;">
          <el-image v-for="url1 in urls" :key="url1" :src="url1" style="height: 100%;width: 50%;margin-left: 25%" />
        </div>
        <div v-show="step2" style="border-color: #34bfa3;border-style: dashed; border-width: 1px; border-radius: 4px;width: 100%;height: 240px;padding: 5px 5px;">
          <el-image v-for="url1 in urls2" :key="url1" :src="url1" style="height: 100%;width: 50%;margin-left: 25%" />
        </div>
        <div v-show="step3" style="border-color: #34bfa3;border-style: dashed; border-width: 1px; border-radius: 4px;width: 100%;height: 240px;padding: 5px 5px;">
          <el-image v-for="url1 in urls3" :key="url1" :src="url1" style="height: 100%;width: 50%;margin-left: 25%" />
        </div>
        <el-divider />
        <div v-show="step1">
          <el-row :gutter="1" type="flex" justify="end">
            <el-col :span="3" style="margin: 0 10px 0 0">
              <el-button size="medium" style="width: 100%">校验数据</el-button>
            </el-col>
            <el-col :span="3" style="margin: 0">
              <el-button size="medium" style="width: 100%" @click="step1=false;step2=true;active=2">下一步</el-button>
            </el-col>
          </el-row>
        </div>

        <div v-show="step2">
          <el-row :gutter="1" type="flex" justify="end">
            <el-col :span="3" style="margin: 0 10px 0 0">
              <el-button size="medium" style="width: 100%" @click="step1=true;step2=false;active=1">上一步</el-button>
            </el-col>
            <el-col :span="3" style="margin: 0">
              <el-button size="medium" style="width: 100%" @click="step2=false;step3=true;active=3">下一步</el-button>
            </el-col>
          </el-row>
        </div>

        <div v-show="step3">
          <el-row :gutter="1" type="flex" justify="end">
            <el-col :span="3" style="margin: 0 10px 0 0">
              <el-button size="medium" style="width: 100%" @click="step2=true;step3=false;active=2">上一步</el-button>
            </el-col>
            <el-col :span="3" style="margin: 0">
              <el-button size="medium" style="width: 100%" @click="dialogFormVisible = false">保存</el-button>
            </el-col>
          </el-row>
        </div>

      </div>

    </el-dialog>

    <el-divider />
    <el-button type="primary" :disabled="false" :circle="true" @click="bt_clk">是否显示富文本框</el-button>

    <mavon-editor v-if="mavon_editor_is_show" ref="md" :style="'height:' + height" @imgAdd="imgAdd" />

  </div>

</template>

<script>
import { upload } from '@/utils/upload'
import { mapGetters } from 'vuex'
import { mavonEditor } from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import { stephen_company, stephen_param, stephen_close_state, get_author, create_job, getSourceAndTheirTable, queryJobPageable } from '@/api/job'

export default {
  name: 'Stephen',
  components: {
    mavonEditor
  },
  data() {
    return {
      treedata: [{
        label: '一级 1',
        children: [{
          label: '二级 1-1',
          children: [{
            label: '三级 1-1-1'
          }]
        }]
      }, {
        label: '一级 2',
        children: [{
          label: '二级 2-1',
          children: [{
            label: '三级 2-1-1'
          }]
        }, {
          label: '二级 2-2',
          children: [{
            label: '三级 2-2-1'
          }]
        }]
      }, {
        label: '一级 3',
        children: [{
          label: '二级 3-1',
          children: [{
            label: '三级 3-1-1'
          }]
        }, {
          label: '二级 3-2',
          children: [{
            label: '三级 3-2-1'
          }]
        }]
      }],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      pos_x: 100,
      pos_y: 100,
      isShow: false,
      gender: 'female',
      active: 1,
      dialog_show: false,
      step1: true,
      step2: false,
      step3: false,
      height: document.documentElement.clientHeight - 200 + 'px',
      mavon_editor_is_show: false,
      members: [
        { name: '李恩健' },
        { name: '王龙江' },
        { name: '李明辉' }
      ],
      input_01: '',
      input_02: '',
      post_res: '',
      tableData: [{
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }],
      formInline: {
        user: '',
        region: ''
      },
      show3: true,
      page_info: {
        total_num: 100,
        current_page: 1,
        page_size: 10
      },
      size: '',
      activeNames: ['1'],
      reverse: true,
      activities: [{
        content: '活动按期开始',
        timestamp: '2018-04-15'
      }, {
        content: '通过审核',
        timestamp: '2018-04-13'
      }, {
        content: '创建成功',
        timestamp: '2018-04-11'
      }],
      close_state: 0,
      value: new Date(),
      url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      srcList: [
        'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
        'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg'
      ],
      loading: true,
      form: {
        name: '',
        region: '',
        date1: '',
        date2: '',
        delivery: false,
        type: [],
        resource: '',
        desc: ''
      },
      dialog: false,
      drawer_loading: false,
      timer: null,
      num: 1,
      dialogFormVisible: false,
      formLabelWidth: '120px',
      urls: [
        'https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg'
      ],
      urls2: [
        'https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg'
      ],
      urls3: [
        'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg'
      ]
    }
  },
  computed: {
    ...mapGetters([
      'user',
      'dwUsername'
    ]),
    input_result: function() {
      return this.input_01 === '' ? '' : parseInt(this.input_01) * parseInt(this.input_01)
    }
  },
  watch: {
    input_02: function(newValue, oldValue) {
      this.$message(oldValue + '变成了' + newValue)
    }
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200 + 'px'
    }
    this.get_close_state()
    // this.$store.dispatch('dw/updateUsername', 'John')
    this.$store.commit('dw/UPDATE_USERNAME', 'John')
  },
  methods: {
    setSessionStorage() {
      sessionStorage['lin'] = '林'
      sessionStorage.setItem('wang', '王')
    },
    getSessionStorage() {
      console.log(sessionStorage['lin'])
      console.log(sessionStorage.getItem('wang'))
      // sessionStorage.removeItem('wang')
      // sessionStorage.clear()
    },
    handleNodeClick(data) {
      console.log('---' + data)
    },
    testAssign() {
      const target = { name: 'stephen', age: 27 }
      const source = { state: 'happy', age: 22 }
      const result = Object.assign({}, target, source)
      console.log(result)
      console.log(target)
      console.log(source)
      console.log('--testAssign--')

      const rr = Object.assign({}, source)
      console.log(rr)
    },
    closeListener(data) {
      this.isShow = false
      console.log('--receData--' + data)
    },
    showClick() {
      this.isShow = true
    },
    handleClose2(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    imgAdd(pos, $file) {
      upload(this.imagesUploadApi, $file).then(res => {
        const data = res.data
        const url = this.baseApi + '/file/' + data.type + '/' + data.realName
        this.$refs.md.$img2Url(pos, url)
      })
    },
    bt_clk() {
      this.mavon_editor_is_show = !this.mavon_editor_is_show
    },
    print_company() {
      stephen_company().then(
        res => {
          this.loading = false
          this.$message(res.company)
        }
      )
    },
    test_post() {
      const a = { 'user': 'lin', 'star': '10' }
      stephen_param(a).then(
        res => {
          this.post_res = res.user + '---' + res.star
          this.$message(this.post_res)
        }
      )
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return 'warning-row'
      } else if (rowIndex === 3) {
        return 'success-row'
      }
      return ''
    },
    onSubmit() {
      console.log('submit!')
    },
    current_page_change(newPage) {
      this.loading = true
      this.page_info.current_page = newPage
      this.$message(this.page_info.current_page + '---' + this.page_info.page_size + '---' + this.page_info.total_num)
    },
    size_change(newSize) {
      this.page_info.page_size = newSize
      console.log(newSize)
      this.$message(newSize)
    },
    show_info() {
      this.$message('卡片点击')
    },
    open1() {
      this.$notify({
        title: '成功',
        message: '这是一条成功的提示消息',
        type: 'success'
      })
    },

    open2() {
      this.$notify({
        title: '警告',
        message: '这是一条警告的提示消息',
        type: 'warning'
      })
    },

    open3() {
      this.$notify.info({
        title: '消息',
        message: '这是一条消息的提示消息'
      })
    },

    open4() {
      this.$notify.error({
        title: '错误',
        message: '这是一条错误的提示消息'
      })
    },
    close_done() {
      console.log(this.close_state)
      this.close_state = 0
      console.log(this.close_state)
    },
    get_close_state() {
      stephen_close_state().then(res => {
        console.log(res.close_state)
        this.close_state = res.close_state
      })
    },
    handleClose(done) {
      if (this.drawer_loading) {
        return
      }
      this.$confirm('确定要提交表单吗？')
        .then(_ => {
          this.drawer_loading = true
          this.timer = setTimeout(() => {
            done()
            // 动画关闭需要一定的时间
            setTimeout(() => {
              this.drawer_loading = false
            }, 400)
          }, 2000)
        })
        .catch(_ => {})
    },
    cancelForm() {
      this.drawer_loading = false
      this.dialog = false
      clearTimeout(this.timer)
    },
    handleChange(value) {
      console.log(value)
    },
    receive_data(data) {
      console.log('---receive-data---')
      console.log(data)
      console.log(data.age)
      const json = JSON.stringify(data)
      console.log(json)
      const obj = JSON.parse(json)
      console.log(obj)
      console.log(obj.age)
    },
    get_author_info() {
      get_author().then(res => {
        res.forEach(item => {
          if (item.id === 24) {
            JSON.parse(item.dagJson).forEach(ii => {
              console.log(ii.name)
            })
          }
        })
      })
    },
    create_job() {
      const data = {
        'jobName': 'stephen_test_02',
        'jobDesc': 'test_job',
        'departmentLabel': '数据平台',
        'jobLevel': 0,
        'dag_json': [
          {
            'id': 0,
            'name': 'SourceTransformation',
            'inputIds': [

            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_91f5e22711f8447bb2ba4d1ac860aa23","globalMetaManager":null,"connectConfig":{"connector":"kafka","tableId":"340"},"schemaConfig":{"columns":[{"name":"userid","type":"BIGINT","nullable":true},{"name":"product","type":"STRING","nullable":true},{"name":"amount","type":"INT","nullable":true}],"primaryKey":null},"tableName":"source_fc58bd031e6d47498cf08c9909675186"}'
          },
          {
            'id': 1,
            'name': 'SourceTransformation',
            'inputIds': [

            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_80e077e75168412ea50bf1d50ebc4d59","globalMetaManager":null,"connectConfig":{"connector":"kafka","tableId":"890"},"schemaConfig":{"columns":[{"name":"product","type":"STRING","nullable":true},{"name":"price","type":"INT","nullable":true},{"name":"color","type":"STRING","nullable":true}],"primaryKey":null},"tableName":"source_a02c423e0ae5499b8ff806c42aa703f6"}'
          },
          {
            'id': 2,
            'name': 'LookupJoinTransformation',
            'inputIds': [
              0
            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_1f2e98c647714414b01d23d4f9a611d5","globalMetaManager":null,"input":null,"inputId":null,"joinKeyExpression":"l.userid = r.id","selectExpression":"l.product as product,r.username as username,r.age as age,l.amount as amount","connectConfig":{"connector":"jdbc","tableId":"1134"},"schemaConfig":{"columns":[{"name":"id","type":"BIGINT","nullable":true},{"name":"username","type":"STRING","nullable":true},{"name":"age","type":"BIGINT","nullable":true}],"primaryKey":["id"]},"isLeftJoin":true}'
          },
          {
            'id': 3,
            'name': 'GroupByTransformation',
            'inputIds': [
              2
            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_ac3e93fb67f443928125b5fbbd9e693b","globalMetaManager":null,"input":null,"inputId":null,"groupByExpression":"username,age","selectExpression":"username,age,sum(amount) as amount,max(product) as product"}'
          },
          {
            'id': 4,
            'name': 'JoinTransformation',
            'inputIds': [
              3,
              1
            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_46edbf25c8a148c9841e54a0e5f1fa07","globalMetaManager":null,"left":null,"right":null,"leftId":null,"rightId":null,"isLeftJoin":false,"joinConditionExpression":"l.product = r.product","selectExpression":"l.username as username,l.age as age,l.amount as amount,r.product as product,r.color as color"}'
          },
          {
            'id': 5,
            'name': 'TopnTransformation',
            'inputIds': [
              4
            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_81e81056ad484f30821d817dbe16e2a9","globalMetaManager":null,"input":null,"inputId":null,"partitionFieldExpression":"age","selectFieldExpression":"username,age,amount,product,color","orderByFieldExpression":"color ASC","n":1}'
          },
          {
            'id': 6,
            'name': 'CalcTransformation',
            'inputIds': [
              5
            ],
            'transformation': "{\"id\":null,\"name\":null,\"resultFuction\":null,\"resultTable\":\"table_19a30538720a4023b2173382f2605a4e\",\"globalMetaManager\":null,\"input\":null,\"inputId\":null,\"selectExpression\":\"username,age,amount,product,color\",\"whereExpression\":\"username='wlj'\"}"
          },
          {
            'id': 7,
            'name': 'SinkTransformation',
            'inputIds': [
              6
            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_d721a039328244c88e744c1c5729b651","globalMetaManager":null,"input":null,"inputId":null,"connectConfig":{"connector":"upsert-kafka","tableId":"872"},"schemaConfig":{"columns":[{"name":"username","type":"STRING","nullable":false},{"name":"age","type":"BIGINT","nullable":false},{"name":"amount","type":"INT","nullable":true},{"name":"product","type":"STRING","nullable":true},{"name":"color","type":"STRING","nullable":true}],"primaryKey":["username","age"]}}'
          },
          {
            'id': 8,
            'name': 'SinkTransformation',
            'inputIds': [
              6
            ],
            'transformation': '{"id":null,"name":null,"resultFuction":null,"resultTable":"table_7356c8b5e82d431cbccfe1c73e53962a","globalMetaManager":null,"input":null,"inputId":null,"connectConfig":{"connector":"jdbc","tableId":"1137"},"schemaConfig":{"columns":[{"name":"username","type":"STRING","nullable":false},{"name":"age","type":"BIGINT","nullable":false},{"name":"amount","type":"INT","nullable":true},{"name":"product","type":"STRING","nullable":true},{"name":"color","type":"STRING","nullable":true}],"primaryKey":["username","age"]}}'
          }
        ]
      }

      create_job(data).then(res => {
        console.log(res)
      })
    },
    getSourceAndTheirTable() {
      getSourceAndTheirTable().then(res => {
        console.log(res)
      })
    },
    luyang() {
      const obj = { 'name': '', 'jsonObj': {}}
      obj.name = 'liuyang'

      const jsonObj = { 'age': 29, 'address': 'shanghai' }
      obj.jsonObj = jsonObj

      console.log('----------')
      console.log(obj)
      const str = JSON.stringify(obj)

      console.log('----------')
      console.log(str)

      console.log('----------')
      console.log(JSON.parse(str).name)
      console.log(JSON.parse(str).jsonObj)
    },
    queryJobPageable() {
      const pathParams = []
      pathParams.push({ 'paramName': 'size', 'paramValue': '2' })
      pathParams.push({ 'paramName': 'page', 'paramValue': '0' })
      pathParams.push({ 'paramName': 'ownerName', 'paramValue': '' })
      pathParams.push({ 'paramName': 'jobName', 'paramValue': 'stephen' })
      queryJobPageable(pathParams).then(res => {
        if (res.code === '200') {
          console.log(res.data)
        }
        if (res.code === '500') {
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    },
    getCurrentUser() {
      console.log('---currentUser---')
      console.log(this.user)
      console.log(this.dwUsername)
    },
    testName() {
      console.log('---testName--')
      const obj = { 'userName': '', 'startup.scan_startup_timestamp_millis-mills': '' }
      obj.userName = 'wanglin'
      obj['startup.scan_startup_timestamp_millis-mills'] = '1660714200000'
      console.log(obj)
    }

  }

}
</script>

<style>
  .el-header, .el-footer {
    background-color: #eeeeee;
    color: #333;
    text-align: center;
    line-height: 60px;
  }

  .el-main {
    /*background-color: #cccccc;*/
    color: #1f2d3d;
    height: auto;
    /*text-align: center;*/
    /*line-height: 30px;*/
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .demo-form-inline{
    width: 50%;
    /*height: 60px;*/
    margin: auto;
    border-style: dashed;
    border-radius: 6px;
    border-width: 2px;
    border-color: #eeeeee;
    text-align: center;
    padding-top: 15px;
  }

  .transition-box {
    width: 50%;
    margin: auto;
    border-style: dashed;
    border-radius: 6px;
    border-width: 2px;
    border-color: #eeeeee;
    text-align: center;
  }

  .page_tt {
    padding-top: 5px;
    text-align: left;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  .box-card {
    width: 480px;
  }

  .el-carousel__item h3 {
    color: #475669;
    font-size: 18px;
    opacity: 0.75;
    line-height: 300px;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }

  .el-carousel__item:nth-child(2n+1) {
    background-color: #d3dce6;
  }

  .calendar {
    margin: auto;
    width: 40%;
  }

  .card_tab {
    margin: auto;
    width: 40%;
  }

  .dialog-set {
  }

  .postion_test{
    width: 100px;
    height: 100px;
    position: absolute;
  }

</style>
