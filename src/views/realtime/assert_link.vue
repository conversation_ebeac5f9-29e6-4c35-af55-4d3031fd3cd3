<template>
  <div style="width: 100%;height: 100%">
    <div style="margin:auto;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);height: 50px;padding-top: 10px;display: flex">
      <div>
        <el-select
          id="sourceSelector"
          v-model="searchTableInfo"
          filterable
          remote
          clearable
          reserve-keyword
          no-data-text="无匹配表"
          placeholder="搜索并选择资产表"
          :remote-method="remoteMethod"
          :loading="iLoading"
          size="medium"
          style="width: 850px;margin-left: 10px"
          :fit-input-width="true"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div style="margin-right: 10px;margin-left: 10px">
        <el-date-picker
          v-model="searchTime"
          type="datetime"
          size="medium"
          value-format="yyyy-MM-dd HH:mm"
          :default-value="nowDate"
          placeholder="选择指标查询时间"
        />
      </div>
      <div style="margin-right: 10px;margin-left: 10px;margin-top: 2px">
        <el-input v-model="delayValue" placeholder="请输入阈值(ms)" />
      </div>
      <div>
        <el-button size="medium" type="success" icon="el-icon-search" @click="showInfo">资产血缘查询</el-button>
      </div>
    </div>
    <div ref="chart" v-loading="isGraphLoading" class="relationship-chart" :style="{width: innerWidth, height: innerHeight}" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import echarts from 'echarts'
import { searchTableLineageInfo, tablePipline } from '@/api/job'

export default {
  name: 'RealtimeEcharts',
  components: {
  },
  props: {
  },
  data() {
    return {
      chart: null,
      options: [],
      iLoading: false,
      searchTableInfo: '',
      searchTime: '',
      json_data: '',
      isGraphLoading: false,
      delayValue: ''
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
    innerHeight() {
      return window.innerHeight + 'px'
    },
    innerWidth() {
      return window.innerWidth + 'px'
    },
    nowDate() {
      return this.formatDate()
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    dealData(data) {
      const jobName = data.searchNode
      const nodes = data.nodes
      const links = data.links

      const editData = []
      const editLink = []

      if (this.delayValue === '') {
        nodes.forEach(item => {
          if (item.name === jobName) {
            item.itemStyle = { color: '#0c0d0e' }
          } else {
            item.itemStyle = { color: '#7087cc' }
          }
          editData.push(item)
        })
        links.forEach(item => {
          editLink.push(item)
        })
      } else {
        nodes.forEach(item => {
          if (item.name === jobName) {
            item.itemStyle = { color: '#0c0d0e' }
          } else if (parseFloat(item.value.delay) >= parseFloat(this.delayValue)) {
            item.itemStyle = { color: '#8f530a' }
          } else {
            item.itemStyle = { color: '#7087cc' }
          }
          editData.push(item)
        })
        links.forEach(item => {
          if (parseFloat(item.edgeValue.delay) >= parseFloat(this.delayValue)) {
            item.lineStyle = { color: '#8f530a' }
          } else {
            item.lineStyle = { color: '#7087cc' }
          }
          editLink.push(item)
        })
      }

      return { 'nodes': editData, 'links': editLink }
    },
    dragGraph(data) {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.chart)

      // 配置关系图数据
      const option = {
        title: {
          text: ''
        },
        textStyle: {
          fontFamily: 'Microsoft YaHei',
          fontSize: 10,
          textBorderType: 'dashed',
          color: '#5470c6'
        },
        tooltip: {},
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [
          {
            type: 'graph',
            layout: 'none',
            symbolSize: 15,
            zoom: 1,
            animationDuration: 3000,
            roam: true,
            label: {
              show: true,
              position: 'right',
              distance: 5,
              fontSize: 10
            },
            lineStyle: {
              type: 'dashed',
              curveness: 0.1,
              cap: 'square',
              join: 'miter',
              capacity: 0.5,
              color: '#a1b4dc',
              width: 2
            },
            edgeSymbol: ['circle', 'arrow'],
            edgeSymbolSize: [1, 10],
            edgeLabel: {
              fontSize: 50
            },
            tooltip: {
              formatter: function(params) {
                // 在这里可以自定义悬浮显示的内容
                if (params.value === undefined) {
                  if (params.dataType === 'edge') {
                    return '作业名称：' + params.data.edgeValue.job + '<br/>' + ' 延迟：' + params.data.edgeValue.delay + '毫秒'
                  } else {
                    return '节点名称：' + params.name
                  }
                } else {
                  return '资产详情：' + params.value.table + '<br/>' + '延迟：' + params.value.delay + '毫秒'
                }
              }
            },
            data: data.nodes,
            links: data.links
          }
        ]
      }
      // 使用配置项显示关系图
      this.chart.setOption(option)
    },
    remoteMethod(query) {
      if (query !== '') {
        this.iLoading = true
        searchTableLineageInfo(query).then(res => {
          if (res.code === '200') {
            this.list = res.data.map(item => {
              const json = { 'searchTable': item.physicsTable, 'searchDb': item.tableDatabase, 'searchInstance': item.tableInstance }
              return { value: JSON.stringify(json), label: item.physicsTable + '(所属库:' + item.tableDatabase + ' 所属实例:' + item.tableInstance + ')' }
            })
            this.iLoading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      } else {
        this.options = []
      }
    },
    formatDate() {
      const date = new Date()

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，注意要 +1
      const day = String(date.getDate()).padStart(2, '0') // 获取日期
      const hours = String(date.getHours()).padStart(2, '0') // 获取小时
      const minutes = String(date.getMinutes()).padStart(2, '0') // 获取分钟

      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    showInfo() {
      if (this.searchTableInfo === null || this.searchTableInfo === '') {
        this.$message({
          type: 'warning',
          message: '请搜索并输入资产表'
        })
        return
      }
      if (this.searchTime === null || this.searchTime === '') {
        this.$message({
          type: 'warning',
          message: '请输入指标查询时间'
        })
        return
      }

      const searchTableInfoObj = JSON.parse(this.searchTableInfo)
      this.isGraphLoading = true
      const requestParam = { 'tableName': searchTableInfoObj.searchTable, 'db': searchTableInfoObj.searchDb, 'instance': searchTableInfoObj.searchInstance, 'ctime': this.searchTime }
      tablePipline(requestParam).then(res => {
        if (res.code === '200') {
          this.isGraphLoading = false
          const returnData = this.dealData(JSON.parse(res.data))
          this.dragGraph(returnData)
        }
        if (res.code === '500') {
          this.isGraphLoading = false
          this.$message({
            type: 'error',
            message: res.mes
          })
        }
      })
    }

  }
}
</script>

<style lang="scss" scoped>

</style>
