<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">作业名</label>
        <el-input v-model="query.ruleName" clearable placeholder="作业名" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">描述</label>
        <el-input v-model="query.ruleDesc" clearable placeholder="描述" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="999px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="作业名" prop="ruleName">
            <el-input v-model="form.ruleName"  :disabled="form.id" style="width: 800px;" />
          </el-form-item>
          <el-form-item label="描述" prop="ruleDesc">
            <el-input v-model="form.ruleDesc"  style="width: 800px;" />
          </el-form-item>
          <el-form-item label="输入表1" prop="sourceTable1">
            <el-select v-model="form.sourceTable1" filterable placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.表名"
                :key="item.id"
                :label="item.label"
                :value="item.value"
                :disabled="form.status==1||form.status==2||form.status==3?true:false"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="输入表2" prop="sourceTable2">
            <el-select v-model="form.sourceTable2" filterable placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.表名"
                :key="item.id"
                :label="item.label"
                :value="item.value"
                :disabled="form.status==1||form.status==2||form.status==3?true:false"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据缓存时间" prop="ttl">
            <el-select v-model="form.ttl" filterable placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.统计周期"
                :key="item.id"
                :label="item.label"
                :value="item.value"
                :disabled="form.status==1||form.status==2||form.status==3?true:false"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="sql逻辑" prop="sqlText">
            <el-input v-model="form.sqlText"  :disabled="form.status==1||form.status==2||form.status==3?true:false" :rows="20" placeholder="语法示例:【SELECT * FROM Orders a left join Shipments b on a.id=b.id and AND a.process_time BETWEEN b.process_time - INTERVAL '4' MINUTE AND b.process_time】要实现超时未关联上告警的效果,在on 条件中追加固定的 a.process_time BETWEEN b.process_time - INTERVAL '4' MINUTE AND b.process_time + INTERVAL '4' MINUTE 语法，如果超时为1分钟，则将字符串中数字4改为1即可"
                      type="textarea" style="width: 800px;" />
          </el-form-item>

          <el-form-item label="便捷复制">
            :【 a.process_time BETWEEN b.process_time - INTERVAL '1' MINUTE AND b.process_time + INTERVAL '1' MINUTE 】
          </el-form-item>
          <!--          <el-form-item label="超时时间">-->
          <!--            <el-input v-model="form.leftTableName" placeholder="sql逻辑最外层-左表名称/别名" style="width: 250px;" />-->
          <!--            <el-input v-model="form.rightTableName" placeholder="sql逻辑最外层-右表名称/别名" style="width: 250px;" />-->
          <!--            <el-input v-model="form.timeout" placeholder="右表超时时间,单位为秒" style="width: 250px;" />-->
          <!--          </el-form-item>-->
          <el-form-item label="话术模板">
            <el-input v-model="form.mess"  :disabled="form.status==1||form.status==2||form.status==3?true:false" style="width: 800px;" />
          </el-form-item>
          <el-form-item label="输出表" prop="sinkTableId">
            <el-select v-model="form.sinkTableId" filterable placeholder="请选择" style="width: 800px;">
              <el-option
                v-for="item in dict.告警表"
                :key="item.id"
                :label="item.label"
                :value="item.value"
                :disabled="form.status==1||form.status==2||form.status==3?true:false"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.userName" style="width: 800px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">保存</el-button>
          <el-button v-permission="['admin','reDataSourceMetadata:sync']" style="margin-left: -2px" type="success"
                     @click="sync(form.sourceTable1,form.sourceTable2)">表字段同步
          </el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="ruleName" label="作业名" />
        <el-table-column prop="ruleDesc" label="描述" />
        <el-table-column prop="sourceTable1" label="输入表1">
          <template slot-scope="scope">
            {{ dict.label.表名[scope.row.sourceTable1] }}
          </template>
        </el-table-column>
        <el-table-column prop="sourceTable2" label="输入表2">
          <template slot-scope="scope">
            {{ dict.label.表名[scope.row.sourceTable2] }}
          </template>
        </el-table-column>
        <el-table-column prop="sinkTableId" label="输出表">
          <template slot-scope="scope">
            {{ dict.label.告警表[scope.row.sinkTableId] }}
          </template>
        </el-table-column>
        <el-table-column prop="ttl" label="数据缓存时间">
          <template slot-scope="scope">
            {{ dict.label.统计周期[scope.row.ttl] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            {{ dict.label.作业状态[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="heartbeatTime" label="心跳时间" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="userName" label="创建者" />
        <el-table-column prop="deptName" label="来源" />

        <el-table-column v-if="checkPer(['admin',,'operatorJob:deploy','operatorJob:stop'])" label="操作" width="250px"
                         align="right">
          <template slot-scope="scope">
            <el-button v-permission="['admin','operatorJob:create']" style="margin-left: -2px" type="primary" size="mini"
                       @click="create(scope.row.id)">创建作业
            </el-button>
            <el-button v-permission="['admin','operatorJob:deploy']" style="margin-left: -2px" type="primary" size="mini"
                       @click="deploy(scope.row.id)">发布
            </el-button>
            <el-button v-permission="['admin','operatorJob:stop']" style="margin-left: -2px" type="success" size="mini"
                       @click="stop(scope.row.id)">下线
            </el-button>
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','intervalJoin:edit','intervalJoin:del'])" label="" width="150px" align="left">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudReDataSourceMetadata from '@/api/reDataSourceMetadata'
import operatorJob from '@/api/operatorJob'
import crudIntervalJoin from '@/api/intervalJoin'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'


const defaultForm = { id: null, ruleName: null, ruleDesc: null, leftTableName:null,rightTableName:null,timeout:null,sourceTable1: null, sourceTable2: null, ttl: null, sqlText: null, mess: null, status: null, heartbeatTime: null, createTime: null, userName: null, deptName: null, sinkTableId: null }
export default {
  name: 'IntervalJoin',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [ '表名', '统计周期', '作业状态', '告警表'],
  cruds() {
    return CRUD({ title: '超时join输出Kafka', url: 'api/intervalJoin', idField: 'id', sort: 'id,desc', crudMethod: { ...crudIntervalJoin }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'intervalJoin:add'],
        edit: ['admin', 'intervalJoin:edit'],
        del: ['admin', 'intervalJoin:del']
      },
      rules: {
        ruleName: [
          { required: true, message: '作业名不能为空', trigger: 'blur' }
        ],
        ruleDesc: [
          { required: true, message: '描述不能为空', trigger: 'blur' }
        ],
        sourceTable1: [
          { required: true, message: '输入表1不能为空', trigger: 'blur' }
        ],
        sourceTable2: [
          { required: true, message: '输入表2不能为空', trigger: 'blur' }
        ],
        ttl: [
          { required: true, message: '数据缓存不能为空', trigger: 'blur' }
        ],
        sqlText: [
          { required: true, message: 'sql逻辑不能为空', trigger: 'blur' }
        ],
        sinkTableId: [
          { required: true, message: '输出表不能为空', trigger: 'blur' }
        ],
      },
      queryTypeOptions: [
        { key: 'ruleName', display_name: '作业名' },
        { key: 'ruleDesc', display_name: '描述' }
      ]
    }
  },
  methods: {
    sync(id1,id2) {
      crudReDataSourceMetadata.synctwotable(id1,id2).then(res => {
        console.log('-------------------------------')
        console.log(id)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    create(id) {
      operatorJob.create(id).then(res => {
        console.log('-------------------------------')
        console.log(res)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    deploy(id) {
      operatorJob.deploy(id).then(res => {
        console.log('-------------------------------')
        console.log(res)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    stop(id) {
      operatorJob.stop(id).then(res => {
        console.log('-------------------------------')
        console.log(res)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
