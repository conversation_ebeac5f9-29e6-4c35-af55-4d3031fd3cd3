<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">表达式</label>
        <el-input v-model="query.ruleExpress" clearable placeholder="表达式" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">作业</label>
        <el-select
          v-model="query.teJobInfoId"
          filterable
          placeholder="请选择"
          clearable
          style="width: 165px;"
          @blur="selectBlur"
          @clear="selectClear"
          @change="selectChange"
        >
          <el-option
            v-for="item in dict.分流作业名"
            :key="item.id"
            :label="item.label"
            :value="item.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>
<!--        <label class="el-form-item-label">规则组id</label>-->
<!--        <el-input v-model="query.teJobRuleGroupId" clearable placeholder="规则组id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />-->
<!--       -->
        <label class="el-form-item-label">规则输出目标表</label>
        <el-select
          v-model="query.reDataSourceMetadataId"
          filterable
          placeholder="请选择"
          clearable
          style="width: 245px;"
          @blur="selectBlur"
          @clear="selectClear"
          @change="selectChange"
        >
          <el-option
            v-for="item1 in dict.分流目标表"
            :key="item1.id"
            :label="item1.label"
            :value="item1.value"
            @keyup.enter.native="crud.toQuery"
          />
        </el-select>

        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="1000px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="180px">
          <el-form-item label="所属作业" prop="teJobInfoId">
            <el-select v-model="form.teJobInfoId" filterable placeholder="请选择" style="width: 670px;" >
              <el-option
                v-for="item in dict.分流作业名"
                :key="item.id"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="规则组id">
            <el-input v-model="form.teJobRuleGroupId" placeholder="预留项，不填" style="width: 670px;" />
          </el-form-item>
          <el-form-item label="演练数据" prop="checkData">
            <el-input v-model="form.checkData" :rows="10" type="textarea" style="width: 670px;" />
          </el-form-item>
          <el-form-item label="规则表达式" prop="ruleExpress">
            <el-input v-model="form.ruleExpress" :rows="3" type="textarea" style="width: 670px;" />
          </el-form-item>
          <el-form-item label="规则输出目标表" prop="reDataSourceMetadataId">
            <el-select v-model="form.reDataSourceMetadataId" filterable placeholder="请选择" style="width: 670px;" >
              <el-option
                v-for="item in dict.分流目标表"
                :key="item.id"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="规则状态" prop="isUsed">
            <el-select v-model="form.isUsed" filterable placeholder="请选择" style="width: 670px;" >
              <el-option
                v-for="item in dict.规则状态"
                :key="item.id"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button v-permission="['admin','teJobRuleExpress:edit']" style="margin-left: -2px" type="success"
                     @click="checkData(form)">规则逻辑校验
          </el-button>
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="自增id" />

        <el-table-column prop="teJobInfoId" label="所属作业">
          <template slot-scope="scope">
            {{ dict.label.分流作业名[scope.row.teJobInfoId] }}
          </template>
        </el-table-column>

        <el-table-column prop="teJobRuleGroupId" label="规则组id" />
        <el-table-column prop="ruleExpress" label="规则表达式" />
        <el-table-column prop="reDataSourceMetadataId" label="规则输出目标表">
          <template slot-scope="scope">
            {{ dict.label.分流目标表[scope.row.reDataSourceMetadataId] }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="isUsed" label="状态">
          <template slot-scope="scope">
            {{ dict.label.规则状态[scope.row.isUsed] }}
          </template>
        </el-table-column>
        <el-table-column prop="ruleType" label="类型"/>
        <el-table-column prop="exprType" label="表达式引擎"/>
        <el-table-column prop="ruleTag" label="标签"/>


        <el-table-column v-if="checkPer(['admin','teJobRuleExpress:edit','teJobRuleExpress:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTeJobRuleExpress from '@/api/teJobRuleExpress'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, ruleExpress: null, teJobInfoId: null, teJobRuleGroupId: null, createTime: null, modifyTime: null, reDataSourceMetadataId: null,ruleType:2,ruleTag:'split' }
export default {
  name: 'TeJobRuleExpress',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['分流目标表','分流作业名','规则状态'],
  cruds() {
    return CRUD({ title: '规则表达式管理', url: 'api/teJobRuleExpress', idField: 'id', sort: 'id,desc', ruleTag: 'split',crudMethod: { ...crudTeJobRuleExpress }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'teJobRuleExpress:add'],
        edit: ['admin', 'teJobRuleExpress:edit'],
        del: ['admin', 'teJobRuleExpress:del']
      },
      rules: {
        ruleExpress: [
          { required: true, message: '规则表达式不能为空', trigger: 'blur' }
        ],
        teJobInfoId: [
          { required: true, message: '作业id不能为空', trigger: 'blur' }
        ],
        reDataSourceMetadataId: [
          { required: true, message: '规则输出目标表不能为空', trigger: 'blur' }
        ],
        isUsed: [
          { required: true, message: '规则状态不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'ruleExpress', display_name: '规则表达式' },
        { key: 'teJobInfoId', display_name: '作业' },
        { key: 'teJobRuleGroupId', display_name: '规则组id' },
        { key: 'reDataSourceMetadataId', display_name: '规则输出目标表' }
      ]
    }
  },
  methods: {
    checkData(data) {
      crudTeJobRuleExpress.checkData(data).then(res => {
        console.log('-------------------------------')
        console.log(ids)
        this.crud.notify(res, CRUD.NOTIFICATION_TYPE.SUCCESS)
      }).catch(err => {
        console.log('-------------------------------')
        console.log(err.response.data.message)
      })
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
