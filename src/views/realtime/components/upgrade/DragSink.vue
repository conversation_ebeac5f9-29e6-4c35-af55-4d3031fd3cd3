<template>
  <div class="app-container">
    <el-dialog
      title="结果表配置"
      :visible.sync="tag"
      width="57%"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="10vh"
    >

      <div style="margin-bottom: -20px">
        <el-form ref="ruleFormDragSink" :rules="rules" :model="ruleForm" label-width="120px" style="margin-top: -20px">
          <el-form-item label="结果表" prop="sinkTableId">
            <el-select
              id="sourceSelector"
              v-model="ruleForm.sinkTableId"
              filterable
              remote
              clearable
              reserve-keyword
              no-data-text="无匹配结果表"
              placeholder="请搜索结果表..."
              style="width: 620px"
              :remote-method="remoteMethod"
              :loading="iLoading"
              @change="selectChange"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="主键" prop="primaryKeyInput">
            <div style="display: flex">
              <el-input
                v-model="ruleForm.primaryKeyInput"
                placeholder="请输入主键字段..."
                clearable
                style="width: 620px; margin-right: 5px"
              />
              <el-tooltip placement="bottom">
                <div slot="content">
                  主键格式： user_id 或 user_id,date <br><br>
                  主键作用： 分区字段[kafka、odps...] 或 更新字段[rds、hbase...]
                </div>
                <div class="el-icon-info" />
              </el-tooltip>
            </div>

          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose('0')">取 消</el-button>
        <el-button type="primary" @click="onClose('1')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import {
  getTableInfoCommonSql,
  getSourceTypeNameMapping,
  getDataSourceTypeByTableId,
  get_table_detail_Info_by_ids
} from '@/api/job'

export default {
  name: 'DragSink',
  components: {
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    extend: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectData: [],
      options: [],
      isPrimaryKeyShow: 1,
      iLoading: false,

      connector: '',

      sourceTypeNameMapping: [],

      ruleForm: {
        sinkTableId: '',
        primaryKeyInput: ''
      },

      rules: {
        sinkTableId: [
          { required: true, message: '请选择结果表', trigger: 'change' }
        ],
        primaryKeyInput: [
          { required: true, message: '请添加主键字段', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    tag() {
      return this.isShow && this.type === 'SinkTransformation'
    }
  },
  watch: {
    isShow(newValue, oldValue) {
      this.$refs['ruleFormDragSink'].resetFields()
      const tempExtend = JSON.parse(this.extend)
      if (newValue === true) {
        if (tempExtend.connectConfig !== undefined) {
          this.connector = tempExtend.connectConfig.connector
          this.ruleForm.sinkTableId = tempExtend.connectConfig.tableId
          this.ruleForm.primaryKeyInput = tempExtend.schemaConfig.primaryKey

          this.showTableName(this.ruleForm.sinkTableId)
        } else {
          this.connector = ''
          this.ruleForm.sinkTableId = ''
          this.ruleForm.primaryKeyInput = ''
        }
      }
    }
  },
  created() {
  },
  mounted() {
    getSourceTypeNameMapping().then(res => {
      if (res.code === '200') {
        this.sourceTypeNameMapping = res.data
        console.log(res.data)
      }
    })
  },
  methods: {
    onClose(type) {
      if (type === '1') {
        this.$refs['ruleFormDragSink'].validate((valid) => {
          const transformation = { 'connectConfig': { 'connector': '', 'tableId': '' }, 'schemaConfig': { 'primaryKey': '' }}
          transformation.connectConfig.connector = this.connector
          transformation.connectConfig.tableId = this.ruleForm.sinkTableId
          transformation.schemaConfig.primaryKey = this.ruleForm.primaryKeyInput
          if (valid) {
            this.$emit('close', JSON.stringify(transformation))
            this.$refs['ruleFormDragSink'].resetFields()
          } else {
            return false
          }
        })
      } else {
        this.$emit('close')
        this.$refs['ruleFormDragSink'].resetFields()
      }
    },
    remoteMethod(query) {
      if (query !== '') {
        this.iLoading = true
        const pathParams = []
        pathParams.push({ 'paramName': 'tableName', 'paramValue': query })
        getTableInfoCommonSql(pathParams).then(res => {
          if (res.code === '200') {
            this.list = res.data.map(item => {
              return { value: item.tableId, label: item.tableName + '(所属实例:' + item.dataSourceName + ')' }
            })
            this.iLoading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase()
                .indexOf(query.toLowerCase()) > -1
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      } else {
        this.options = []
      }
    },
    selectChange() {
      if (this.ruleForm.sinkTableId !== '') {
        getDataSourceTypeByTableId(this.ruleForm.sinkTableId).then(res => {
          if (res.code === '200') {
            this.connector = res.data
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }
    },
    showTableName(tableId) {
      if (tableId !== '') {
        console.log(tableId)
        const tableIdList = []
        tableIdList.push(tableId)
        get_table_detail_Info_by_ids(tableIdList).then(res => {
          if (res.code === '200') {
            this.options = res.data.map(item => {
              return { value: item.tableId, label: item.tableName + '(所属实例:' + item.dataSourceName + ')' }
            })
          }
          if (res.code === '500') {
            this.$message({
              type: 'error',
              message: res.mes
            })
          }
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>

</style>

