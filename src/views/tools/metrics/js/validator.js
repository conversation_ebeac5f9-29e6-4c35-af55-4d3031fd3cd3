// 校验指标明细demo格式
export function validateJson(rule, value, callback) {
  if (!value) {
    return callback()
  }
  if (value) {
    try {
      const obj = JSON.parse(value)
      if (typeof obj === 'object' && obj) {
        callback()
      } else {
        callback(new Error('不符合json格式要求！'))
      }
    } catch (e) {
      callback(new Error('不符合json格式要求！'))
    }
  }
}

// 校验存储topic id
export function validateDataSourceMetadataId(rule, value, callback) {
  if (!value) {
    return callback()
  }
  if (value) {
    const reg = /^[1-9]\d*$/
    if (reg.test(value) === true) {
      callback()
    } else {
      callback(new Error('必须输入正整数！'))
    }
  }
}
