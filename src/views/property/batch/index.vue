<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="800px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="表id" prop="tableId">
            <el-input v-model="form.tableId" style="width: 680px;" />

          </el-form-item>
          <el-form-item
            label="消息类型"
            prop="messType"
          >
            <el-select
              v-model="form.messType"
              filterable
              placeholder="请选择"
              style="width: 680px;"
            >
              <el-option
                v-for="item in dict.消息类型"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="字段名" prop="binlogText">
            <el-input v-model="form.binlogText" :rows="30" type="textarea" style="width: 680px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column v-if="checkPer(['admin','reDataSourceMetadataBatch:edit','reDataSourceMetadataBatch:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudReDataSourceMetadataBatch from '@/api/reDataSourceMetadataBatch'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { createTime: null, modifyTime: null, createBy: null, dept: null, tableId: null, binlogText: null }
export default {
  name: 'ReDataSourceMetadataBatch',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['消息类型'],
  cruds() {
    return CRUD({ title: '一键导入', url: 'api/reDataSourceMetadataBatch', idField: 'tableId', sort: 'tableId,desc', crudMethod: { ...crudReDataSourceMetadataBatch }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'reDataSourceMetadataBatch:add'],
        edit: ['admin', 'reDataSourceMetadataBatch:edit'],
        del: ['admin', 'reDataSourceMetadataBatch:del']
      },
      rules: {
        tableId: [
          { required: true, message: '自增id不能为空', trigger: 'blur' }
        ],
        binlogText: [
          { required: true, message: '字段名不能为空', trigger: 'blur' }
        ]
      }}
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
