<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">作业名</label>
        <el-input v-model="query.jobName" clearable placeholder="作业名" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <label class="el-form-item-label">创建人</label>
        <el-input v-model="query.createUserName" clearable placeholder="创建人" style="width: 185px;" class="filter-item"
                  @keyup.enter.native="crud.toQuery"/>
        <rrOperation :crud="crud"/>
      </div>

      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="自增id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="作业名" prop="jobName">
            <el-input v-model="form.jobName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="作业务负责人" prop="jobOwner">
            <el-input v-model="form.jobOwner" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="ts 事件时间" prop="eventTime">
            <el-input v-model="form.eventTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="告警标签" prop="tag">
            <el-input v-model="form.tag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="异常数据源" prop="tableName">
            <el-input v-model="form.tableName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="异常数据">
            <el-input v-model="form.msg" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="修改时间" prop="modifyTime">
            <el-input v-model="form.modifyTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="自增id" />
        <el-table-column prop="jobName" label="作业名" />
        <el-table-column prop="jobOwner" label="作业务负责人" />
        <el-table-column prop="eventTime" label="ts 事件时间" />
        <el-table-column prop="tag" label="告警标签" />
        <el-table-column prop="tableName" label="异常数据源" />
        <el-table-column prop="msg" label="异常数据" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="modifyTime" label="修改时间" />
        <el-table-column v-if="checkPer(['admin','teJobDqcMessage:edit','teJobDqcMessage:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTeJobDqcMessage from '@/api/teJobDqcMessage'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, jobName: null, jobOwner: null, eventTime: null, tag: null, tableName: null, msg: null, createTime: null, modifyTime: null }
export default {
  name: 'TeJobDqcMessage',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'dqc异常消息', url: 'api/teJobDqcMessage', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTeJobDqcMessage }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'teJobDqcMessage:add'],
        edit: ['admin', 'teJobDqcMessage:edit'],
        del: ['admin', 'teJobDqcMessage:del']
      },
      rules: {
        jobName: [
          { required: true, message: '作业名不能为空', trigger: 'blur' }
        ],
        jobOwner: [
          { required: true, message: '作业务负责人不能为空', trigger: 'blur' }
        ],
        eventTime: [
          { required: true, message: 'ts 事件时间不能为空', trigger: 'blur' }
        ],
        tag: [
          { required: true, message: '告警标签不能为空', trigger: 'blur' }
        ],
        tableName: [
          { required: true, message: '异常数据源不能为空', trigger: 'blur' }
        ],
        createTime: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' }
        ],
        modifyTime: [
          { required: true, message: '修改时间不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
